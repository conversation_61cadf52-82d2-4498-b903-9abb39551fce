package com.example.weatherwidget.`data`

import androidx.room.InvalidationTracker
import androidx.room.RoomOpenDelegate
import androidx.room.migration.AutoMigrationSpec
import androidx.room.migration.Migration
import androidx.room.util.TableInfo
import androidx.room.util.TableInfo.Companion.read
import androidx.room.util.dropFtsSyncTriggers
import androidx.sqlite.SQLiteConnection
import androidx.sqlite.execSQL
import javax.`annotation`.processing.Generated
import kotlin.Lazy
import kotlin.String
import kotlin.Suppress
import kotlin.collections.List
import kotlin.collections.Map
import kotlin.collections.MutableList
import kotlin.collections.MutableMap
import kotlin.collections.MutableSet
import kotlin.collections.Set
import kotlin.collections.mutableListOf
import kotlin.collections.mutableMapOf
import kotlin.collections.mutableSetOf
import kotlin.reflect.KClass

@Generated(value = ["androidx.room.RoomProcessor"])
@Suppress(names = ["UNCHECKED_CAST", "DEPRECATION", "REDUNDANT_PROJECTION", "REMOVAL"])
public class WeatherDatabase_Impl : WeatherDatabase() {
  private val _weatherDao: Lazy<WeatherDao> = lazy {
    WeatherDao_Impl(this)
  }

  protected override fun createOpenDelegate(): RoomOpenDelegate {
    val _openDelegate: RoomOpenDelegate = object : RoomOpenDelegate(1,
        "e69121ed67f253e567009db426e20c40", "ef792899c693eab9864170072f078c18") {
      public override fun createAllTables(connection: SQLiteConnection) {
        connection.execSQL("CREATE TABLE IF NOT EXISTS `weather` (`cityId` INTEGER NOT NULL, `cityName` TEXT NOT NULL, `country` TEXT NOT NULL, `temperature` REAL NOT NULL, `feelsLike` REAL NOT NULL, `humidity` INTEGER NOT NULL, `pressure` INTEGER NOT NULL, `windSpeed` REAL NOT NULL, `windDirection` INTEGER NOT NULL, `weatherCondition` TEXT NOT NULL, `weatherDescription` TEXT NOT NULL, `weatherIconCode` INTEGER NOT NULL, `lastUpdated` INTEGER NOT NULL, `isFavorite` INTEGER NOT NULL, PRIMARY KEY(`cityId`))")
        connection.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)")
        connection.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'e69121ed67f253e567009db426e20c40')")
      }

      public override fun dropAllTables(connection: SQLiteConnection) {
        connection.execSQL("DROP TABLE IF EXISTS `weather`")
      }

      public override fun onCreate(connection: SQLiteConnection) {
      }

      public override fun onOpen(connection: SQLiteConnection) {
        internalInitInvalidationTracker(connection)
      }

      public override fun onPreMigrate(connection: SQLiteConnection) {
        dropFtsSyncTriggers(connection)
      }

      public override fun onPostMigrate(connection: SQLiteConnection) {
      }

      public override fun onValidateSchema(connection: SQLiteConnection):
          RoomOpenDelegate.ValidationResult {
        val _columnsWeather: MutableMap<String, TableInfo.Column> = mutableMapOf()
        _columnsWeather.put("cityId", TableInfo.Column("cityId", "INTEGER", true, 1, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsWeather.put("cityName", TableInfo.Column("cityName", "TEXT", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsWeather.put("country", TableInfo.Column("country", "TEXT", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsWeather.put("temperature", TableInfo.Column("temperature", "REAL", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsWeather.put("feelsLike", TableInfo.Column("feelsLike", "REAL", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsWeather.put("humidity", TableInfo.Column("humidity", "INTEGER", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsWeather.put("pressure", TableInfo.Column("pressure", "INTEGER", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsWeather.put("windSpeed", TableInfo.Column("windSpeed", "REAL", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsWeather.put("windDirection", TableInfo.Column("windDirection", "INTEGER", true, 0,
            null, TableInfo.CREATED_FROM_ENTITY))
        _columnsWeather.put("weatherCondition", TableInfo.Column("weatherCondition", "TEXT", true,
            0, null, TableInfo.CREATED_FROM_ENTITY))
        _columnsWeather.put("weatherDescription", TableInfo.Column("weatherDescription", "TEXT",
            true, 0, null, TableInfo.CREATED_FROM_ENTITY))
        _columnsWeather.put("weatherIconCode", TableInfo.Column("weatherIconCode", "INTEGER", true,
            0, null, TableInfo.CREATED_FROM_ENTITY))
        _columnsWeather.put("lastUpdated", TableInfo.Column("lastUpdated", "INTEGER", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsWeather.put("isFavorite", TableInfo.Column("isFavorite", "INTEGER", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        val _foreignKeysWeather: MutableSet<TableInfo.ForeignKey> = mutableSetOf()
        val _indicesWeather: MutableSet<TableInfo.Index> = mutableSetOf()
        val _infoWeather: TableInfo = TableInfo("weather", _columnsWeather, _foreignKeysWeather,
            _indicesWeather)
        val _existingWeather: TableInfo = read(connection, "weather")
        if (!_infoWeather.equals(_existingWeather)) {
          return RoomOpenDelegate.ValidationResult(false, """
              |weather(com.example.weatherwidget.data.model.WeatherInfo).
              | Expected:
              |""".trimMargin() + _infoWeather + """
              |
              | Found:
              |""".trimMargin() + _existingWeather)
        }
        return RoomOpenDelegate.ValidationResult(true, null)
      }
    }
    return _openDelegate
  }

  protected override fun createInvalidationTracker(): InvalidationTracker {
    val _shadowTablesMap: MutableMap<String, String> = mutableMapOf()
    val _viewTables: MutableMap<String, Set<String>> = mutableMapOf()
    return InvalidationTracker(this, _shadowTablesMap, _viewTables, "weather")
  }

  public override fun clearAllTables() {
    super.performClear(false, "weather")
  }

  protected override fun getRequiredTypeConverterClasses(): Map<KClass<*>, List<KClass<*>>> {
    val _typeConvertersMap: MutableMap<KClass<*>, List<KClass<*>>> = mutableMapOf()
    _typeConvertersMap.put(WeatherDao::class, WeatherDao_Impl.getRequiredConverters())
    return _typeConvertersMap
  }

  public override fun getRequiredAutoMigrationSpecClasses(): Set<KClass<out AutoMigrationSpec>> {
    val _autoMigrationSpecsSet: MutableSet<KClass<out AutoMigrationSpec>> = mutableSetOf()
    return _autoMigrationSpecsSet
  }

  public override
      fun createAutoMigrations(autoMigrationSpecs: Map<KClass<out AutoMigrationSpec>, AutoMigrationSpec>):
      List<Migration> {
    val _autoMigrations: MutableList<Migration> = mutableListOf()
    return _autoMigrations
  }

  public override fun weatherDao(): WeatherDao = _weatherDao.value
}
