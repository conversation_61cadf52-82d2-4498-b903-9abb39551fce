# Weather Widget for Android

This project implements a weather widget for an existing weather application using modern Android development practices and libraries.

## Features

- Weather widget displaying current weather conditions for a selected city
- Widget configuration screen to select from saved cities
- Automatic background updates every 1-2 hours
- Displays city name, temperature, weather icon, description, humidity, and wind speed
- Last updated timestamp
- Tap to open main application

## Architecture

The project follows a clean architecture approach with MVVM pattern and reactive programming using Kotlin Flows.

![Architecture Flow Diagram](app/src/main/res/raw/weather_widget_flow_diagram.svg)

### Key Components

- **UI Layer**: Jetpack Compose for the main app and configuration activity, Jetpack Glance for the widget
- **ViewModel Layer**: Manages UI state and business logic
- **Repository Layer**: Coordinates data operations between local and remote sources
- **Data Sources**:
  - Local: Room Database for weather data, DataStore for preferences
  - Remote: Retrofit for API calls to OpenWeatherMap
- **Background Processing**: WorkManager with CoroutineWorker for periodic updates
- **Dependency Injection**: Koin for providing dependencies

## Technologies Used

- **Kotlin**: Primary programming language
- **Jetpack Libraries**:
  - Compose: Modern UI toolkit
  - Glance: Widget framework
  - Room: SQLite database abstraction
  - DataStore: Key-value preferences storage
  - WorkManager: Background task scheduling
  - ViewModel: UI state management
  - LiveData & Flow: Reactive programming
- **Koin**: Dependency injection
- **Retrofit**: Type-safe HTTP client
- **Coroutines**: Asynchronous programming
- **Coil**: Image loading

## Project Structure

```
app/
├── src/
│   ├── main/
│   │   ├── java/com/example/weatherwidget/
│   │   │   ├── data/                  # Data layer
│   │   │   │   ├── local/             # Room database, entities, DAOs
│   │   │   │   ├── remote/            # API services
│   │   │   │   └── repository/        # Repository implementations
│   │   │   ├── di/                    # Dependency injection modules
│   │   │   ├── domain/                # Domain models and use cases
│   │   │   ├── ui/                    # UI components
│   │   │   │   ├── main/              # Main activity
│   │   │   │   └── widget/            # Widget configuration
│   │   │   ├── util/                  # Utility classes
│   │   │   ├── widget/                # Widget implementation
│   │   │   │   ├── glance/            # Glance widget UI
│   │   │   │   └── receiver/          # Widget receivers
│   │   │   └── worker/                # Background workers
│   │   ├── res/                       # Resources
│   │   └── AndroidManifest.xml        # App manifest
│   └── test/                          # Unit tests
└── build.gradle                       # App-level build file
```

## Implementation Details

### Widget Implementation

The widget is implemented using Jetpack Glance, which provides a modern, declarative approach to building app widgets. The widget UI is defined in `WeatherWidget.kt` and uses a `GlanceAppWidget` to display weather information.

### Data Flow

1. **Widget Configuration**: User selects a city from the list of saved cities in `WeatherWidgetConfigActivity`
2. **Data Storage**: Selected city ID is stored in DataStore preferences
3. **Widget Update**: `WeatherWidget` reads the selected city ID and displays weather information
4. **Background Updates**: `WeatherUpdateWorker` periodically fetches fresh weather data and updates all active widgets

### Background Updates

Periodic updates are scheduled using WorkManager:

1. `WeatherWidgetManager` schedules a periodic work request when a widget is added
2. `WeatherUpdateWorker` runs in the background to fetch fresh weather data
3. After updating the database, it triggers a widget update via `GlanceAppWidgetManager`

## Setup Instructions

1. Clone the repository
2. Open the project in Android Studio
3. Add your OpenWeatherMap API key in the appropriate place (see below)
4. Build and run the application

### API Key

To use this application, you need to obtain an API key from [OpenWeatherMap](https://openweathermap.org/api) and add it to the app. You can set it in the DataStore preferences or modify the repository implementation to use your key.

## License

This project is licensed under the MIT License - see the LICENSE file for details.