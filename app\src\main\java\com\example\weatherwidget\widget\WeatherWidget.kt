package com.example.weatherwidget.widget

import android.content.Context
import android.content.Intent
import androidx.compose.runtime.Composable
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.glance.GlanceId
import androidx.glance.GlanceModifier
import androidx.glance.Image
import androidx.glance.ImageProvider
import androidx.glance.action.ActionParameters
import androidx.glance.action.clickable
import androidx.glance.appwidget.GlanceAppWidget
import androidx.glance.appwidget.action.ActionCallback
import androidx.glance.appwidget.action.actionRunCallback
import androidx.glance.appwidget.provideContent
import androidx.glance.background
import androidx.glance.layout.Alignment
import androidx.glance.layout.Column
import androidx.glance.layout.Row
import androidx.glance.layout.Spacer
import androidx.glance.layout.fillMaxSize
import androidx.glance.layout.fillMaxWidth
import androidx.glance.layout.height
import androidx.glance.layout.padding
import androidx.glance.layout.size
import androidx.glance.layout.width
import androidx.glance.text.FontWeight
import androidx.glance.text.Text
import androidx.glance.text.TextAlign
import androidx.glance.text.TextStyle
import com.example.weatherwidget.R
import com.example.weatherwidget.data.model.WeatherInfo
import com.example.weatherwidget.data.repository.WeatherRepository
import com.example.weatherwidget.ui.MainActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.withContext
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale


class WeatherWidget : GlanceAppWidget(), KoinComponent {

    private val weatherRepository: WeatherRepository by inject()

    override suspend fun provideGlance(context: Context, id: GlanceId) {
        // Fetch data outside of provideContent
        val cityId = withContext(Dispatchers.IO) {
            weatherRepository.getSelectedWidgetCityId()
        }

        val weatherInfo = if (cityId != null) {
            withContext(Dispatchers.IO) {
                weatherRepository.getWeatherInfoById(cityId).firstOrNull()
            }
        } else null

        // Provide the widget content with the fetched data
        provideContent {
            WeatherWidgetContent(context, weatherInfo)
        }
    }


    @Composable
    private fun WeatherWidgetContent(context: Context, weatherInfo: WeatherInfo?) {
        Column(
            modifier = GlanceModifier
                .fillMaxSize()
                .background(ImageProvider(R.drawable.widget_background))
                .padding(16.dp)
                .clickable(actionRunCallback<OpenAppAction>()),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalAlignment = Alignment.CenterVertically
        ) {
            if (weatherInfo != null) {
                // City name and country
                Text(
                    text = "${weatherInfo.cityName}, ${weatherInfo.country}",
                    style = TextStyle(
                        fontWeight = FontWeight.Bold,
                        fontSize = 16.sp,
                        textAlign = TextAlign.Center
                    ),
                    modifier = GlanceModifier.fillMaxWidth()
                )

                Spacer(modifier = GlanceModifier.height(8.dp))

                // Weather icon and temperature
                Row(
                    modifier = GlanceModifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // Weather icon
                    Image(
                        provider = ImageProvider(getWeatherIconResource(weatherInfo.weatherIconCode)),
                        contentDescription = weatherInfo.weatherDescription,
                        modifier = GlanceModifier.size(48.dp)
                    )

                    Spacer(modifier = GlanceModifier.width(8.dp))

                    // Temperature
                    Text(
                        text = "${weatherInfo.temperature.toInt()}°C",
                        style = TextStyle(
                            fontWeight = FontWeight.Bold,
                            fontSize = 24.sp
                        )
                    )
                }

                Spacer(modifier = GlanceModifier.height(8.dp))

                // Weather condition
                Text(
                    text = weatherInfo.weatherDescription.capitalize(),
                    style = TextStyle(
                        fontSize = 14.sp,
                        textAlign = TextAlign.Center
                    ),
                    modifier = GlanceModifier.fillMaxWidth()
                )

                Spacer(modifier = GlanceModifier.height(8.dp))

                // Additional info (humidity, wind)
                Row(
                    modifier = GlanceModifier.fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "Humidity: ${weatherInfo.humidity}%",
                        style = TextStyle(fontSize = 12.sp)
                    )

                    Spacer(modifier = GlanceModifier.width(8.dp))

                    Text(
                        text = "Wind: ${weatherInfo.windSpeed} m/s",
                        style = TextStyle(fontSize = 12.sp)
                    )
                }

                Spacer(modifier = GlanceModifier.height(4.dp))

                // Last updated time
                Text(
                    text = "Updated: ${formatLastUpdated(weatherInfo.lastUpdated)}",
                    style = TextStyle(
                        fontSize = 10.sp,
                        textAlign = TextAlign.Center
                    ),
                    modifier = GlanceModifier.fillMaxWidth()
                )
            } else {
                // No weather data available
                Text(
                    text = "Tap to set up weather widget",
                    style = TextStyle(
                        fontSize = 14.sp,
                        textAlign = TextAlign.Center
                    ),
                    modifier = GlanceModifier.fillMaxWidth()
                )
            }
        }
    }

    private fun getWeatherIconResource(iconCode: String): Int {
        return when (iconCode) {
            "01d" -> R.drawable.ic_clear_day
            "01n" -> R.drawable.ic_clear_night
            "02d" -> R.drawable.ic_partly_cloudy_day
            "02n" -> R.drawable.ic_partly_cloudy_night
            "03d", "03n" -> R.drawable.ic_cloudy
            "04d", "04n" -> R.drawable.ic_cloudy
            "09d", "09n" -> R.drawable.ic_rain
            "10d" -> R.drawable.ic_rain_day
            "10n" -> R.drawable.ic_rain_night
            "11d", "11n" -> R.drawable.ic_thunderstorm
            "13d", "13n" -> R.drawable.ic_snow
            "50d", "50n" -> R.drawable.ic_fog
            else -> R.drawable.ic_clear_day
        }
    }

    private fun formatLastUpdated(timestamp: Long): String {
        val dateFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
        return dateFormat.format(Date(timestamp))
    }

    private fun String.capitalize(): String {
        return this.replaceFirstChar {
            if (it.isLowerCase()) it.titlecase(Locale.getDefault()) else it.toString()
        }
    }
}

/**
 * Action to open the main app when widget is clicked
 */
class OpenAppAction : ActionCallback {
    override suspend fun onAction(
        context: Context,
        glanceId: GlanceId,
        parameters: ActionParameters
    ) {
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        context.startActivity(intent)
    }
}