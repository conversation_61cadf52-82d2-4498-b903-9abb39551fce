package com.example.weatherwidget.widget

import android.content.Context
import android.content.Intent
import androidx.compose.runtime.Composable
import androidx.glance.GlanceId
import androidx.glance.action.ActionParameters
import androidx.glance.appwidget.GlanceAppWidget
import androidx.glance.appwidget.action.ActionCallback
import androidx.glance.appwidget.provideContent
import com.example.weatherwidget.data.model.DefaultWidgetConfiguration
import com.example.weatherwidget.data.model.WeatherInfo
import com.example.weatherwidget.data.model.WidgetConfiguration
import com.example.weatherwidget.data.repository.WeatherRepository
import com.example.weatherwidget.ui.MainActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.withContext
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject


class WeatherWidget : GlanceAppWidget(), KoinComponent {

    private val weatherRepository: WeatherRepository by inject()

    override suspend fun provideGlance(context: Context, id: GlanceId) {
        // Get widget ID from GlanceId
        val widgetId = id.toString().hashCode()

        // Fetch widget configuration and weather data
        val (configuration, weatherInfo) = withContext(Dispatchers.IO) {
            val config = weatherRepository.getWidgetConfiguration(widgetId).firstOrNull()
                ?: DefaultWidgetConfiguration.create(widgetId)

            val cityId = config.locationDisplay.selectedCityId
                ?: weatherRepository.getSelectedWidgetCityId()

            val weather = if (cityId != null) {
                weatherRepository.getWeatherInfoById(cityId).firstOrNull()
            } else null

            Pair(config, weather)
        }

        // Check if system is in dark mode
        val isSystemDark = (context.resources.configuration.uiMode and
                android.content.res.Configuration.UI_MODE_NIGHT_MASK) ==
                android.content.res.Configuration.UI_MODE_NIGHT_YES

        // Provide the widget content with the fetched data
        provideContent {
            WeatherWidgetContent(context, weatherInfo, configuration, isSystemDark)
        }
    }


    @Composable
    private fun WeatherWidgetContent(
        context: Context,
        weatherInfo: WeatherInfo?,
        configuration: WidgetConfiguration,
        isSystemDark: Boolean
    ) {
        // Use the new configurable widget content from WeatherWidgetPreview
        GlanceWeatherWidgetContent(
            weatherInfo = weatherInfo,
            configuration = configuration,
            isSystemDark = isSystemDark
        )
    }


}

/**
 * Action to open the main app when widget is clicked
 */
class OpenAppAction : ActionCallback {
    override suspend fun onAction(
        context: Context,
        glanceId: GlanceId,
        parameters: ActionParameters
    ) {
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        context.startActivity(intent)
    }
}