plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.compose)

    alias(libs.plugins.room)
    alias(libs.plugins.ksp)
    alias(libs.plugins.kotlin.serialization)
    alias(libs.plugins.about.library)
}

android {
    namespace 'com.example.weatherwidget'
    compileSdk 36


       room {
        schemaDirectory("$projectDir/schemas")
        generateKotlin = true
    }
    ksp {
        arg("room.incremental", "true")
//        arg("room.generateKotlin", "true")
    }

    defaultConfig {
        applicationId "com.example.weatherwidget"
        minSdk 28
        targetSdk 36
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary true
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }
    buildFeatures {
        compose true
    }
    packagingOptions {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }
}

dependencies {
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.androidx.activity.compose)
    implementation(platform(libs.compose.bom))
    implementation(libs.androidx.ui)
    implementation(libs.androidx.ui.graphics)
    implementation(libs.androidx.material3)

    debugImplementation(libs.androidx.ui.tooling)
    implementation(libs.androidx.ui.tooling.preview)

    runtimeOnly(libs.kotlinx.coroutines.android)
    implementation(libs.bundles.compose)    // ui, material3, iconsExtended

    implementation(libs.room.ktx)
    implementation(libs.room.runtime)
    ksp(libs.room.compiler)

    implementation(platform(libs.koin.bom))
    implementation(libs.koin.androidx.compose)


    // DataStore
    implementation(libs.androidx.dataStore.preferences)
//    implementation(libs.androidx.datastore)


    implementation(libs.javax.inject)
    implementation(libs.kotlinx.datetime)


    // Work
    implementation(libs.androidx.workmanager)
    implementation(libs.koin.androidx.workmanager)


    implementation(libs.kotlinx.serialization.json)

    implementation(libs.aboutlibrary.core)
    implementation(libs.aboutlibrary.compose)


    // SplashScreen
    implementation(libs.androidx.core.splashscreen)

    // Widgets
//    implementation(libs.androidx.glance.material3)
    implementation(libs.androidx.glance)

    implementation(libs.androidx.navigation.compose)
    implementation(libs.androidx.hilt.navigation.compose)

    // NETWORK
    implementation(libs.play.services.location)

    implementation(libs.retrofit)
    implementation(libs.retrofit.converter.gson)
    implementation(libs.okhttp.logging)

    // material 2
    implementation(libs.compose.material)

    implementation(libs.coil.kt.compose)

}