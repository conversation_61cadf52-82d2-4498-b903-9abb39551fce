1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.weatherwidget"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="28"
9        android:targetSdkVersion="36" />
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
14-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:8:5-81
14-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:8:22-78
15    <uses-permission android:name="android.permission.WAKE_LOCK" />
15-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:5-68
15-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:22-65
16    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
16-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:5-77
16-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:22-74
17
18    <permission
18-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\30287725489871af330b7aa82a2b9f1f\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
19        android:name="com.example.weatherwidget.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
19-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\30287725489871af330b7aa82a2b9f1f\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
20        android:protectionLevel="signature" />
20-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\30287725489871af330b7aa82a2b9f1f\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
21
22    <uses-permission android:name="com.example.weatherwidget.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
22-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\30287725489871af330b7aa82a2b9f1f\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
22-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\30287725489871af330b7aa82a2b9f1f\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
23
24    <application
24-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:10:5-67:19
25        android:name="com.example.weatherwidget.WeatherApp"
25-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:11:9-35
26        android:allowBackup="true"
26-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:12:9-35
27        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
27-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\30287725489871af330b7aa82a2b9f1f\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
28        android:dataExtractionRules="@xml/data_extraction_rules"
28-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:13:9-65
29        android:debuggable="true"
30        android:extractNativeLibs="false"
31        android:fullBackupContent="@xml/backup_rules"
31-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:14:9-54
32        android:icon="@mipmap/ic_launcher"
32-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:15:9-43
33        android:label="@string/app_name"
33-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:16:9-41
34        android:roundIcon="@mipmap/ic_launcher_round"
34-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:17:9-54
35        android:supportsRtl="true"
35-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:18:9-35
36        android:theme="@style/Theme.CleanWeather" >
36-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:19:9-50
37
38        <!-- Disable WorkManager automatic initialization -->
39        <provider
40            android:name="androidx.startup.InitializationProvider"
40-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:24:13-67
41            android:authorities="com.example.weatherwidget.androidx-startup"
41-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:25:13-68
42            android:exported="false" >
42-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:26:13-37
43            <meta-data
43-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9e9496b1403961c155e46b481d6b657e\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
44                android:name="androidx.emoji2.text.EmojiCompatInitializer"
44-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9e9496b1403961c155e46b481d6b657e\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
45                android:value="androidx.startup" />
45-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9e9496b1403961c155e46b481d6b657e\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
46            <meta-data
46-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\070431985ac0ce73e7537b3ab66df7b1\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
47                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
47-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\070431985ac0ce73e7537b3ab66df7b1\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
48                android:value="androidx.startup" />
48-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\070431985ac0ce73e7537b3ab66df7b1\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
49            <meta-data
49-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
50                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
50-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
51                android:value="androidx.startup" />
51-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
52        </provider>
53
54        <!-- Main Activity -->
55        <activity
55-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:35:9-43:20
56            android:name="com.example.weatherwidget.ui.MainActivity"
56-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:36:13-44
57            android:exported="true"
57-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:37:13-36
58            android:theme="@style/Theme.CleanWeather" >
58-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:38:13-54
59            <intent-filter>
59-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:39:13-42:29
60                <action android:name="android.intent.action.MAIN" />
60-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:40:17-69
60-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:40:25-66
61
62                <category android:name="android.intent.category.LAUNCHER" />
62-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:41:17-77
62-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:41:27-74
63            </intent-filter>
64        </activity>
65
66        <!-- Widget Provider -->
67        <receiver
67-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:46:9-55:20
68            android:name="com.example.weatherwidget.widget.WeatherWidgetReceiver"
68-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:47:13-57
69            android:exported="true" >
69-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:48:13-36
70            <intent-filter>
70-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:49:13-51:29
71                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
71-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:50:17-84
71-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:50:25-81
72            </intent-filter>
73
74            <meta-data
74-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:52:13-54:63
75                android:name="android.appwidget.provider"
75-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:53:17-58
76                android:resource="@xml/weather_widget_info" />
76-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:54:17-60
77        </receiver>
78
79        <!-- Widget Configuration Activity -->
80        <activity
80-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:58:9-65:20
81            android:name="com.example.weatherwidget.widget.WeatherWidgetConfigActivity"
81-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:59:13-63
82            android:exported="false"
82-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:60:13-37
83            android:theme="@style/Theme.CleanWeather" >
83-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:61:13-54
84            <intent-filter>
84-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:62:13-64:29
85                <action android:name="android.appwidget.action.APPWIDGET_CONFIGURE" />
85-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:63:17-87
85-->C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:63:25-84
86            </intent-filter>
87        </activity>
88        <activity
88-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\99eb3aee547f429176c0b3577d528a52\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
89            android:name="com.google.android.gms.common.api.GoogleApiActivity"
89-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\99eb3aee547f429176c0b3577d528a52\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
90            android:exported="false"
90-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\99eb3aee547f429176c0b3577d528a52\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
91            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
91-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\99eb3aee547f429176c0b3577d528a52\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
92
93        <meta-data
93-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6bdbe06209508b26c4e38693a2ab85d8\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
94            android:name="com.google.android.gms.version"
94-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6bdbe06209508b26c4e38693a2ab85d8\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
95            android:value="@integer/google_play_services_version" />
95-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6bdbe06209508b26c4e38693a2ab85d8\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
96
97        <activity
97-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:23:9-28:62
98            android:name="androidx.glance.appwidget.action.ActionTrampolineActivity"
98-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:24:13-85
99            android:enabled="true"
99-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:25:13-35
100            android:excludeFromRecents="true"
100-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:26:13-46
101            android:exported="false"
101-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:27:13-37
102            android:theme="@android:style/Theme.NoDisplay" />
102-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:28:13-59
103        <activity
103-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:29:9-36:81
104            android:name="androidx.glance.appwidget.action.InvisibleActionTrampolineActivity"
104-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:30:13-94
105            android:enabled="true"
105-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:31:13-35
106            android:exported="false"
106-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:32:13-37
107            android:launchMode="singleInstance"
107-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:33:13-48
108            android:noHistory="true"
108-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:34:13-37
109            android:taskAffinity="androidx.glance.appwidget.ListAdapterCallbackTrampoline"
109-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:35:13-91
110            android:theme="@style/Widget.Glance.AppWidget.CallbackTrampoline" />
110-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:36:13-78
111
112        <receiver
112-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:38:9-41:40
113            android:name="androidx.glance.appwidget.action.ActionCallbackBroadcastReceiver"
113-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:39:13-92
114            android:enabled="true"
114-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:40:13-35
115            android:exported="false" />
115-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:41:13-37
116        <receiver
116-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:42:9-45:40
117            android:name="androidx.glance.appwidget.UnmanagedSessionReceiver"
117-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:43:13-78
118            android:enabled="true"
118-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:44:13-35
119            android:exported="false" />
119-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:45:13-37
120        <receiver
120-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:46:9-53:20
121            android:name="androidx.glance.appwidget.MyPackageReplacedReceiver"
121-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:47:13-79
122            android:enabled="true"
122-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:48:13-35
123            android:exported="false" >
123-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:49:13-37
124            <intent-filter>
124-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:50:13-52:29
125                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
125-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:51:17-84
125-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:51:25-81
126            </intent-filter>
127        </receiver>
128
129        <service
129-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:55:9-58:72
130            android:name="androidx.glance.appwidget.GlanceRemoteViewsService"
130-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:56:13-78
131            android:exported="true"
131-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:57:13-36
132            android:permission="android.permission.BIND_REMOTEVIEWS" />
132-->[androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:58:13-69
133
134        <activity
134-->[androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\9d7fa17d87fa8eff4ec49e9b1333f9cb\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
135            android:name="androidx.compose.ui.tooling.PreviewActivity"
135-->[androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\9d7fa17d87fa8eff4ec49e9b1333f9cb\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
136            android:exported="true" />
136-->[androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\9d7fa17d87fa8eff4ec49e9b1333f9cb\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
137
138        <service
138-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
139            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
139-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
140            android:directBootAware="false"
140-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
141            android:enabled="@bool/enable_system_alarm_service_default"
141-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
142            android:exported="false" />
142-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
143        <service
143-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
144            android:name="androidx.work.impl.background.systemjob.SystemJobService"
144-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
145            android:directBootAware="false"
145-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
146            android:enabled="@bool/enable_system_job_service_default"
146-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
147            android:exported="true"
147-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
148            android:permission="android.permission.BIND_JOB_SERVICE" />
148-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
149        <service
149-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
150            android:name="androidx.work.impl.foreground.SystemForegroundService"
150-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
151            android:directBootAware="false"
151-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
152            android:enabled="@bool/enable_system_foreground_service_default"
152-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
153            android:exported="false" />
153-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
154
155        <receiver
155-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
156            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
156-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
157            android:directBootAware="false"
157-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
158            android:enabled="true"
158-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
159            android:exported="false" />
159-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
160        <receiver
160-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
161            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
161-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
162            android:directBootAware="false"
162-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
163            android:enabled="false"
163-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
164            android:exported="false" >
164-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
165            <intent-filter>
165-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
166                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
166-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
166-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
167                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
167-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
167-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
168            </intent-filter>
169        </receiver>
170        <receiver
170-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
171            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
171-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
172            android:directBootAware="false"
172-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
173            android:enabled="false"
173-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
174            android:exported="false" >
174-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
175            <intent-filter>
175-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
176                <action android:name="android.intent.action.BATTERY_OKAY" />
176-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
176-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
177                <action android:name="android.intent.action.BATTERY_LOW" />
177-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
177-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
178            </intent-filter>
179        </receiver>
180        <receiver
180-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
181            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
181-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
182            android:directBootAware="false"
182-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
183            android:enabled="false"
183-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
184            android:exported="false" >
184-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
185            <intent-filter>
185-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
186                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
186-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
186-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
187                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
187-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
187-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
188            </intent-filter>
189        </receiver>
190        <receiver
190-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
191            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
191-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
192            android:directBootAware="false"
192-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
193            android:enabled="false"
193-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
194            android:exported="false" >
194-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
195            <intent-filter>
195-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
196                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
196-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
196-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
197            </intent-filter>
198        </receiver>
199        <receiver
199-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
200            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
200-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
201            android:directBootAware="false"
201-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
202            android:enabled="false"
202-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
203            android:exported="false" >
203-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
204            <intent-filter>
204-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
205                <action android:name="android.intent.action.BOOT_COMPLETED" />
205-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:17-79
205-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:25-76
206                <action android:name="android.intent.action.TIME_SET" />
206-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
206-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
207                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
207-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
207-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
208            </intent-filter>
209        </receiver>
210        <receiver
210-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
211            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
211-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
212            android:directBootAware="false"
212-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
213            android:enabled="@bool/enable_system_alarm_service_default"
213-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
214            android:exported="false" >
214-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
215            <intent-filter>
215-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
216                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
216-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
216-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
217            </intent-filter>
218        </receiver>
219        <receiver
219-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
220            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
220-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
221            android:directBootAware="false"
221-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
222            android:enabled="true"
222-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
223            android:exported="true"
223-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
224            android:permission="android.permission.DUMP" >
224-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
225            <intent-filter>
225-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
226                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
226-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
226-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
227            </intent-filter>
228        </receiver>
229
230        <service
230-->[androidx.core:core-remoteviews:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\190d4b6fe45ede2433fdba7f25f72b62\transformed\core-remoteviews-1.1.0\AndroidManifest.xml:24:9-27:63
231            android:name="androidx.core.widget.RemoteViewsCompatService"
231-->[androidx.core:core-remoteviews:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\190d4b6fe45ede2433fdba7f25f72b62\transformed\core-remoteviews-1.1.0\AndroidManifest.xml:25:13-73
232            android:permission="android.permission.BIND_REMOTEVIEWS" />
232-->[androidx.core:core-remoteviews:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\190d4b6fe45ede2433fdba7f25f72b62\transformed\core-remoteviews-1.1.0\AndroidManifest.xml:26:13-69
233        <service
233-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\0d3deac4716d283862aeb386c4e6aa32\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
234            android:name="androidx.room.MultiInstanceInvalidationService"
234-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\0d3deac4716d283862aeb386c4e6aa32\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
235            android:directBootAware="true"
235-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\0d3deac4716d283862aeb386c4e6aa32\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
236            android:exported="false" />
236-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\0d3deac4716d283862aeb386c4e6aa32\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
237
238        <receiver
238-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
239            android:name="androidx.profileinstaller.ProfileInstallReceiver"
239-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
240            android:directBootAware="false"
240-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
241            android:enabled="true"
241-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
242            android:exported="true"
242-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
243            android:permission="android.permission.DUMP" >
243-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
244            <intent-filter>
244-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
245                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
245-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
245-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
246            </intent-filter>
247            <intent-filter>
247-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
248                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
248-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
248-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
249            </intent-filter>
250            <intent-filter>
250-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
251                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
251-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
251-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
252            </intent-filter>
253            <intent-filter>
253-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
254                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
254-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
254-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
255            </intent-filter>
256        </receiver>
257    </application>
258
259</manifest>
