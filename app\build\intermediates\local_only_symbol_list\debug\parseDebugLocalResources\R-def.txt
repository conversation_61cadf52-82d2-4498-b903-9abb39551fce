R_DEF: Internal format may change without notice
local
color accent
color divider
color icons
color primary
color primary_dark
color primary_light
color primary_text
color secondary_text
color status_error
color status_success
color status_warning
color temperature_cold
color temperature_cool
color temperature_hot
color temperature_mild
color temperature_warm
color widget_background_end
color widget_background_start
color widget_text_primary
color widget_text_secondary
dimen activity_horizontal_margin
dimen activity_vertical_margin
dimen button_corner_radius
dimen button_height
dimen button_min_width
dimen button_padding
dimen card_corner_radius
dimen card_elevation
dimen card_margin
dimen list_item_height
dimen list_item_icon_size
dimen list_item_padding
dimen list_item_spacing
dimen text_size_large
dimen text_size_medium
dimen text_size_small
dimen text_size_xlarge
dimen text_size_xxlarge
dimen widget_city_text_size
dimen widget_corner_radius
dimen widget_desc_text_size
dimen widget_details_text_size
dimen widget_icon_size
dimen widget_margin
dimen widget_padding
dimen widget_temp_text_size
drawable ic_clear_day
drawable ic_clear_night
drawable ic_cloudy
drawable ic_fog
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_partly_cloudy_day
drawable ic_partly_cloudy_night
drawable ic_rain
drawable ic_rain_day
drawable ic_rain_night
drawable ic_snow
drawable ic_thunderstorm
drawable widget_background
drawable widget_preview
layout widget_loading
mipmap ic_launcher
mipmap ic_launcher_round
raw aboutlibraries
raw weather_widget_flow_diagram
string app_name
string config_cancel
string config_no_cities
string config_save
string config_success
string config_title
string config_update_1_hour
string config_update_2_hours
string config_update_4_hours
string config_update_frequency
string delete_confirmation
string delete_no
string delete_yes
string error_api_key
string error_city_not_found
string error_loading_weather
string error_network
string humidity_format
string no_weather_data
string refresh_all
string search_button
string search_hint
string temperature_format
string widget_description
string widget_error
string widget_last_updated
string widget_loading
string widget_name
string widget_tap_to_refresh
string wind_format
style Theme.CleanWeather
xml backup_rules
xml data_extraction_rules
xml weather_widget_info
