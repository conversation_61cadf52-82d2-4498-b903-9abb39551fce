{"libraries": [{"uniqueId": "androidx.activity:activity", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.10.1", "description": "Provides the base Activity subclass and the relevant hooks to build a composable structure on top.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Activity", "website": "https://developer.android.com/jetpack/androidx/releases/activity#1.10.1", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.activity:activity-compose", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.10.1", "description": "Compose integration with Activity", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Activity Compose", "website": "https://developer.android.com/jetpack/androidx/releases/activity#1.10.1", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.activity:activity-ktx", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.10.1", "description": "Kotlin extensions for 'activity' artifact", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Activity Kotlin Extensions", "website": "https://developer.android.com/jetpack/androidx/releases/activity#1.10.1", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.annotation:annotation", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.9.1", "description": "Provides source annotations for tooling and readability.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Annotation", "website": "https://developer.android.com/jetpack/androidx/releases/annotation#1.9.1", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.annotation:annotation-experimental", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.4.1", "description": "Java annotation for use on unstable Android API surfaces. When used in conjunction with the Experimental annotation lint checks, this annotation provides functional parity with <PERSON><PERSON><PERSON>'s Experimental annotation.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Experimental annotation", "website": "https://developer.android.com/jetpack/androidx/releases/annotation#1.4.1", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.annotation:annotation-jvm", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.9.1", "description": "Provides source annotations for tooling and readability.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Annotation", "website": "https://developer.android.com/jetpack/androidx/releases/annotation#1.9.1", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.appcompat:appcompat", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.7.0", "description": "Provides backwards-compatible implementations of UI-related Android SDK functionality, including dark mode and Material theming.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "AppCompat", "website": "https://developer.android.com/jetpack/androidx/releases/appcompat#1.7.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.appcompat:appcompat-resources", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.7.0", "description": "Provides backward-compatible implementations of resource-related Android SDKfunctionality, including color state list theming.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "AppCompat Resources", "website": "https://developer.android.com/jetpack/androidx/releases/appcompat#1.7.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.arch.core:core-common", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.2.0", "description": "Android Arch-Common", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Arch-Common", "website": "https://developer.android.com/jetpack/androidx/releases/arch-core#2.2.0", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.arch.core:core-runtime", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.2.0", "description": "Android Arch-Runtime", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Arch-Runtime", "website": "https://developer.android.com/jetpack/androidx/releases/arch-core#2.2.0", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.autofill:autofill", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.0.0", "description": "AndroidX Autofill", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "AndroidX Autofill", "website": "https://developer.android.com/jetpack/androidx", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.collection:collection", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.5.0", "description": "Standalone efficient collections.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "collections", "website": "https://developer.android.com/jetpack/androidx/releases/collection#1.5.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.collection:collection-jvm", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.5.0", "description": "Standalone efficient collections.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "collections", "website": "https://developer.android.com/jetpack/androidx/releases/collection#1.5.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.collection:collection-ktx", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.5.0", "description": "Kotlin extensions for 'collection' artifact", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Collections Kotlin Extensions", "website": "https://developer.android.com/jetpack/androidx/releases/collection#1.5.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.animation:animation", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Compose animation library", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose Animation", "website": "https://developer.android.com/jetpack/androidx/releases/compose-animation#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.animation:animation-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Compose animation library", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose Animation", "website": "https://developer.android.com/jetpack/androidx/releases/compose-animation#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.animation:animation-core", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Animation engine and animation primitives that are the building blocks of the Compose animation library", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose Animation Core", "website": "https://developer.android.com/jetpack/androidx/releases/compose-animation#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.animation:animation-core-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Animation engine and animation primitives that are the building blocks of the Compose animation library", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose Animation Core", "website": "https://developer.android.com/jetpack/androidx/releases/compose-animation#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.foundation:foundation", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Higher level abstractions of the Compose UI primitives. This library is design system agnostic, providing the high-level building blocks for both application and design-system developers", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose Foundation", "website": "https://developer.android.com/jetpack/androidx/releases/compose-foundation#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.foundation:foundation-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Higher level abstractions of the Compose UI primitives. This library is design system agnostic, providing the high-level building blocks for both application and design-system developers", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose Foundation", "website": "https://developer.android.com/jetpack/androidx/releases/compose-foundation#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.foundation:foundation-layout", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Compose layout implementations", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose Layouts", "website": "https://developer.android.com/jetpack/androidx/releases/compose-foundation#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.foundation:foundation-layout-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Compose layout implementations", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose Layouts", "website": "https://developer.android.com/jetpack/androidx/releases/compose-foundation#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.material3:material3", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.3.2", "description": "Compose Material You Design Components library", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose Material3 Components", "website": "https://developer.android.com/jetpack/androidx/releases/compose-material3#1.3.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.material3:material3-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.3.2", "description": "Compose Material You Design Components library", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose Material3 Components", "website": "https://developer.android.com/jetpack/androidx/releases/compose-material3#1.3.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.material:material", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Compose Material Design Components library", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose Material Components", "website": "https://developer.android.com/jetpack/androidx/releases/compose-material#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.material:material-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Compose Material Design Components library", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose Material Components", "website": "https://developer.android.com/jetpack/androidx/releases/compose-material#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.material:material-icons-core", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.7.8", "description": "Compose Material Design core icons. This module contains the most commonly used set of Material icons.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose Material Icons Core", "website": "https://developer.android.com/jetpack/androidx/releases/compose-material#1.7.8", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.material:material-icons-core-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.7.8", "description": "Compose Material Design core icons. This module contains the most commonly used set of Material icons.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose Material Icons Core", "website": "https://developer.android.com/jetpack/androidx/releases/compose-material#1.7.8", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.material:material-icons-extended", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.7.8", "description": "Compose Material Design extended icons. This module contains all Material icons. It is a very large dependency and should not be included directly.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose Material Icons Extended", "website": "https://developer.android.com/jetpack/androidx/releases/compose-material#1.7.8", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.material:material-icons-extended-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.7.8", "description": "Compose Material Design extended icons. This module contains all Material icons. It is a very large dependency and should not be included directly.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose Material Icons Extended", "website": "https://developer.android.com/jetpack/androidx/releases/compose-material#1.7.8", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.material:material-ripple", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Material ripple used to build interactive components", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose Material Ripple", "website": "https://developer.android.com/jetpack/androidx/releases/compose-material#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.material:material-ripple-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Material ripple used to build interactive components", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose Material Ripple", "website": "https://developer.android.com/jetpack/androidx/releases/compose-material#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.runtime:runtime", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Tree composition support for code generated by the Compose compiler plugin and corresponding public API", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose Runtime", "website": "https://developer.android.com/jetpack/androidx/releases/compose-runtime#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.runtime:runtime-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Tree composition support for code generated by the Compose compiler plugin and corresponding public API", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose Runtime", "website": "https://developer.android.com/jetpack/androidx/releases/compose-runtime#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.runtime:runtime-saveable", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Compose components that allow saving and restoring the local ui state", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose Saveable", "website": "https://developer.android.com/jetpack/androidx/releases/compose-runtime#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.runtime:runtime-saveable-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Compose components that allow saving and restoring the local ui state", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose Saveable", "website": "https://developer.android.com/jetpack/androidx/releases/compose-runtime#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.ui:ui", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Compose UI primitives. This library contains the primitives that form the Compose UI Toolkit, such as drawing, measurement and layout.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose UI", "website": "https://developer.android.com/jetpack/androidx/releases/compose-ui#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.ui:ui-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Compose UI primitives. This library contains the primitives that form the Compose UI Toolkit, such as drawing, measurement and layout.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose UI", "website": "https://developer.android.com/jetpack/androidx/releases/compose-ui#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.ui:ui-geometry", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Compose classes related to dimensions without units", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose Geometry", "website": "https://developer.android.com/jetpack/androidx/releases/compose-ui#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.ui:ui-geometry-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Compose classes related to dimensions without units", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose Geometry", "website": "https://developer.android.com/jetpack/androidx/releases/compose-ui#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.ui:ui-graphics", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Compose graphics", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose Graphics", "website": "https://developer.android.com/jetpack/androidx/releases/compose-ui#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.ui:ui-graphics-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Compose graphics", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose Graphics", "website": "https://developer.android.com/jetpack/androidx/releases/compose-ui#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.ui:ui-text", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Compose Text primitives and utilities", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose UI Text", "website": "https://developer.android.com/jetpack/androidx/releases/compose-ui#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.ui:ui-text-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Compose Text primitives and utilities", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose UI Text", "website": "https://developer.android.com/jetpack/androidx/releases/compose-ui#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.ui:ui-tooling", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Compose tooling library. This library exposes information to our tools for better IDE support.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose <PERSON>", "website": "https://developer.android.com/jetpack/androidx/releases/compose-ui#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.ui:ui-tooling-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Compose tooling library. This library exposes information to our tools for better IDE support.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose <PERSON>", "website": "https://developer.android.com/jetpack/androidx/releases/compose-ui#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.ui:ui-tooling-data", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Compose tooling library data. This library provides data about compose for different tooling purposes.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose Tooling Data", "website": "https://developer.android.com/jetpack/androidx/releases/compose-ui#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.ui:ui-tooling-data-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Compose tooling library data. This library provides data about compose for different tooling purposes.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose Tooling Data", "website": "https://developer.android.com/jetpack/androidx/releases/compose-ui#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.ui:ui-tooling-preview", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Compose tooling library API. This library provides the API required to declare @Preview composables in user apps.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose UI Preview Tooling", "website": "https://developer.android.com/jetpack/androidx/releases/compose-ui#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.ui:ui-tooling-preview-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Compose tooling library API. This library provides the API required to declare @Preview composables in user apps.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose UI Preview Tooling", "website": "https://developer.android.com/jetpack/androidx/releases/compose-ui#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.ui:ui-unit", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Compose classes for simple units", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose Unit", "website": "https://developer.android.com/jetpack/androidx/releases/compose-ui#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.ui:ui-unit-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Compose classes for simple units", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose Unit", "website": "https://developer.android.com/jetpack/androidx/releases/compose-ui#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.ui:ui-util", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Internal Compose utilities used by other modules", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose <PERSON>", "website": "https://developer.android.com/jetpack/androidx/releases/compose-ui#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose.ui:ui-util-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.2", "description": "Internal Compose utilities used by other modules", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose <PERSON>", "website": "https://developer.android.com/jetpack/androidx/releases/compose-ui#1.8.2", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.compose:compose-bom", "funding": [], "developers": [], "artifactVersion": "2025.05.01", "description": "A compatible set of Jetpack Compose libraries.", "name": "Jetpack Compose Libraries BOM", "website": "https://developer.android.com/jetpack", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.concurrent:concurrent-futures", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.0", "description": "Androidx implementation of Guava's ListenableFuture", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "AndroidX Futures", "website": "https://developer.android.com/topic/libraries/architecture/index.html", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.concurrent:concurrent-futures-ktx", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.0", "description": "Kotlin Extensions for Androidx implementation of Guava's ListenableFuture", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "AndroidX Futures Kotlin Extensions", "website": "https://developer.android.com/topic/libraries/architecture/index.html", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.core:core", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.16.0", "description": "Provides backward-compatible implementations of Android platform APIs and features.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Core", "website": "https://developer.android.com/jetpack/androidx/releases/core#1.16.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.core:core-ktx", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.16.0", "description": "Kotlin extensions for 'core' artifact", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Core Kotlin Extensions", "website": "https://developer.android.com/jetpack/androidx/releases/core#1.16.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.core:core-remoteviews", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.0", "description": "AndroidX RemoteViews Support", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "RemoteViews", "website": "https://developer.android.com/jetpack/androidx/releases/core#1.1.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.core:core-splashscreen", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.0.1", "description": "This library provides the compatibility APIs for SplashScreen and helper method to enable a splashscreen on devices prior Android 12", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "SplashScreen", "website": "https://developer.android.com/jetpack/androidx/releases/core#1.0.1", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.core:core-viewtree", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.0.0", "description": "Provides ViewTree extensions packaged for use by other core androidx libraries", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "androidx.core:core-viewtree", "website": "https://developer.android.com/jetpack/androidx/releases/core#1.0.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.cursoradapter:cursoradapter", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.0.0", "description": "The Support Library is a static library that you can add to your Android application in order to use APIs that are either not available for older platform versions or utility APIs that aren't a part of the framework APIs. Compatible on devices running API 14 or later.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "Support Cursor Adapter", "website": "http://developer.android.com/tools/extras/support-library.html", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.customview:customview", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.0.0", "description": "The Support Library is a static library that you can add to your Android application in order to use APIs that are either not available for older platform versions or utility APIs that aren't a part of the framework APIs. Compatible on devices running API 14 or later.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "Support Custom View", "website": "http://developer.android.com/tools/extras/support-library.html", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.customview:customview-poolingcontainer", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.0.0", "description": "Utilities for listening to the lifecycle of containers that manage their child Views' lifecycle, such as RecyclerView", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "androidx.customview:poolingcontainer", "website": "https://developer.android.com/jetpack/androidx/releases/customview#1.0.0", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.datastore:datastore", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.7", "description": "Android DataStore - contains the underlying store used by each serialization method along with components that require an Android dependency", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "DataStore", "website": "https://developer.android.com/jetpack/androidx/releases/datastore#1.1.7", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.datastore:datastore-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.7", "description": "Android DataStore - contains the underlying store used by each serialization method along with components that require an Android dependency", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "DataStore", "website": "https://developer.android.com/jetpack/androidx/releases/datastore#1.1.7", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.datastore:datastore-core", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.7", "description": "Android DataStore Core - contains the underlying store used by each serialization method", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "DataStore Core", "website": "https://developer.android.com/jetpack/androidx/releases/datastore#1.1.7", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.datastore:datastore-core-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.7", "description": "Android DataStore Core - contains the underlying store used by each serialization method", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "DataStore Core", "website": "https://developer.android.com/jetpack/androidx/releases/datastore#1.1.7", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.datastore:datastore-core-okio", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.7", "description": "Android DataStore Core Okio- contains APIs to use datastore-core in multiplatform via okio", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "DataStore Core Okio", "website": "https://developer.android.com/jetpack/androidx/releases/datastore#1.1.7", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.datastore:datastore-core-okio-jvm", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.7", "description": "Android DataStore Core Okio- contains APIs to use datastore-core in multiplatform via okio", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "DataStore Core Okio", "website": "https://developer.android.com/jetpack/androidx/releases/datastore#1.1.7", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.datastore:datastore-preferences", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.7", "description": "Android Preferences DataStore", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Preferences DataStore", "website": "https://developer.android.com/jetpack/androidx/releases/datastore#1.1.7", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.datastore:datastore-preferences-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.7", "description": "Android Preferences DataStore", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Preferences DataStore", "website": "https://developer.android.com/jetpack/androidx/releases/datastore#1.1.7", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.datastore:datastore-preferences-core", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.7", "description": "Android Preferences DataStore without the Android Dependencies", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Preferences DataStore Core", "website": "https://developer.android.com/jetpack/androidx/releases/datastore#1.1.7", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.datastore:datastore-preferences-core-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.7", "description": "Android Preferences DataStore without the Android Dependencies", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Preferences DataStore Core", "website": "https://developer.android.com/jetpack/androidx/releases/datastore#1.1.7", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.datastore:datastore-preferences-external-protobuf", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.7", "description": "Repackaged proto-lite dependency for use by datastore preferences", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Preferences External Protobuf", "website": "https://developer.android.com/jetpack/androidx/releases/datastore#1.1.7", "licenses": ["BSD-3-<PERSON><PERSON>"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.datastore:datastore-preferences-proto", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.7", "description": "<PERSON><PERSON><PERSON> the generated proto for use by datastore-preferences.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Preferences DataStore Proto", "website": "https://developer.android.com/jetpack/androidx/releases/datastore#1.1.7", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.drawerlayout:drawerlayout", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.0.0", "description": "The Support Library is a static library that you can add to your Android application in order to use APIs that are either not available for older platform versions or utility APIs that aren't a part of the framework APIs. Compatible on devices running API 14 or later.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "Support Drawer Layout", "website": "http://developer.android.com/tools/extras/support-library.html", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.emoji2:emoji2", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.4.0", "description": "Core library to enable emoji compatibility in Kitkat and newer devices to avoid the empty emoji characters.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Emoji2", "website": "https://developer.android.com/jetpack/androidx/releases/emoji2#1.4.0", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.emoji2:emoji2-views-helper", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.4.0", "description": "Provide helper classes for Emoji2 views.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Emoji2 Views Helper", "website": "https://developer.android.com/jetpack/androidx/releases/emoji2#1.4.0", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.exifinterface:exifinterface", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.3.7", "description": "Android Support ExifInterface", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Support ExifInterface", "website": "https://developer.android.com/jetpack/androidx/releases/exifinterface#1.3.7", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.fragment:fragment", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.3", "description": "The Support Library is a static library that you can add to your Android application in order to use APIs that are either not available for older platform versions or utility APIs that aren't a part of the framework APIs. Compatible on devices running API 14 or later.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "fragment", "website": "https://developer.android.com/jetpack/androidx/releases/fragment#1.8.3", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.fragment:fragment-ktx", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.8.3", "description": "Kotlin extensions for 'fragment' artifact", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Fragment Kotlin Extensions", "website": "https://developer.android.com/jetpack/androidx/releases/fragment#1.8.3", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.glance:glance", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.1", "description": "Glance allows developers to build layouts for remote surfaces using a Jetpack Compose-style API.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Glance", "website": "https://developer.android.com/jetpack/androidx/releases/glance#1.1.1", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.glance:glance-appwidget", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.1", "description": "Glance-appwidgets allows developers to build layouts for Android AppWidgets using a Jetpack Compose-style API.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Glance For App Widgets", "website": "https://developer.android.com/jetpack/androidx/releases/glance#1.1.1", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.glance:glance-appwidget-external-protobuf", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.1", "description": "Repackaged proto-lite dependency for use by galnce", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Glance AppWidget External Protobuf", "website": "https://developer.android.com/jetpack/androidx/releases/glance#1.1.1", "licenses": ["BSD-3-<PERSON><PERSON>"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.glance:glance-appwidget-proto", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.1", "description": "Protos for use with glance app widgets.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Glance AppWidget Protos", "website": "https://developer.android.com/jetpack/androidx/releases/glance#1.1.1", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.graphics:graphics-path", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.0.1", "description": "Query segment data for android.graphics.Path objects", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Graphics Path", "website": "https://developer.android.com/jetpack/androidx/releases/graphics#1.0.1", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.hilt:hilt-navigation", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.2.0", "description": "Android Navigation Hilt Extension", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Navigation Hilt Extension", "website": "https://developer.android.com/jetpack/androidx/releases/hilt#1.2.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.hilt:hilt-navigation-compose", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.2.0", "description": "Navigation Compose Hilt Integration", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Navigation Compose Hilt Integration", "website": "https://developer.android.com/jetpack/androidx/releases/hilt#1.2.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.interpolator:interpolator", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.0.0", "description": "The Support Library is a static library that you can add to your Android application in order to use APIs that are either not available for older platform versions or utility APIs that aren't a part of the framework APIs. Compatible on devices running API 14 or later.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "Support Interpolators", "website": "http://developer.android.com/tools/extras/support-library.html", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.lifecycle:lifecycle-common", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.9.0", "description": "Android Lifecycle-Common", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Lifecycle-Common", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.9.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.lifecycle:lifecycle-common-java8", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.9.0", "description": "Android Lifecycle-Common for Java 8 Language", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Lifecycle-Common for Java 8", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.9.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.lifecycle:lifecycle-common-jvm", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.9.0", "description": "Android Lifecycle-Common", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Lifecycle-Common", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.9.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.lifecycle:lifecycle-livedata", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.9.0", "description": "Android Lifecycle LiveData", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Lifecycle LiveData", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.9.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.lifecycle:lifecycle-livedata-core", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.9.0", "description": "Android Lifecycle LiveData Core", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Lifecycle LiveData Core", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.9.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.lifecycle:lifecycle-livedata-core-ktx", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.9.0", "description": "Kotlin extensions for 'livedata-core' artifact", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "LiveData Core Kotlin Extensions", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.9.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.lifecycle:lifecycle-process", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.9.0", "description": "Android Lifecycle Process", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Lifecycle Process", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.9.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.lifecycle:lifecycle-runtime", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.9.0", "description": "Android Lifecycle Runtime", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Lifecycle Runtime", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.9.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.lifecycle:lifecycle-runtime-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.9.0", "description": "Android Lifecycle Runtime", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Lifecycle Runtime", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.9.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.lifecycle:lifecycle-runtime-compose", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.9.0", "description": "Compose integration with Lifecycle", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Lifecycle Runtime Compose", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.9.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.lifecycle:lifecycle-runtime-compose-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.9.0", "description": "Compose integration with Lifecycle", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Lifecycle Runtime Compose", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.9.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.lifecycle:lifecycle-runtime-ktx", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.9.0", "description": "Kotlin extensions for 'lifecycle' artifact", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Lifecycle Kotlin Extensions", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.9.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.lifecycle:lifecycle-runtime-ktx-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.9.0", "description": "Kotlin extensions for 'lifecycle' artifact", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Lifecycle Kotlin Extensions", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.9.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.lifecycle:lifecycle-service", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.9.0", "description": "Android Lifecycle Service", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Lifecycle Service", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.9.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.lifecycle:lifecycle-viewmodel", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.9.0", "description": "Android Lifecycle ViewModel", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Lifecycle ViewModel", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.9.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.lifecycle:lifecycle-viewmodel-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.9.0", "description": "Android Lifecycle ViewModel", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Lifecycle ViewModel", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.9.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.lifecycle:lifecycle-viewmodel-compose", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.9.0", "description": "Compose integration with Lifecycle ViewModel", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Lifecycle ViewModel Compose", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.9.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.lifecycle:lifecycle-viewmodel-compose-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.9.0", "description": "Compose integration with Lifecycle ViewModel", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Lifecycle ViewModel Compose", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.9.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.lifecycle:lifecycle-viewmodel-ktx", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.9.0", "description": "Kotlin extensions for 'viewmodel' artifact", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Lifecycle ViewModel Kotlin Extensions", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.9.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.lifecycle:lifecycle-viewmodel-savedstate", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.9.0", "description": "Android Lifecycle ViewModel", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Lifecycle ViewModel with SavedState", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.9.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.lifecycle:lifecycle-viewmodel-savedstate-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.9.0", "description": "Android Lifecycle ViewModel", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Lifecycle ViewModel with SavedState", "website": "https://developer.android.com/jetpack/androidx/releases/lifecycle#2.9.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.loader:loader", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.0.0", "description": "The Support Library is a static library that you can add to your Android application in order to use APIs that are either not available for older platform versions or utility APIs that aren't a part of the framework APIs. Compatible on devices running API 14 or later.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "Support loader", "website": "http://developer.android.com/tools/extras/support-library.html", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.navigation:navigation-common", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.9.0", "description": "Android Navigation-Common", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Navigation Common", "website": "https://developer.android.com/jetpack/androidx/releases/navigation#2.9.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.navigation:navigation-common-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.9.0", "description": "Android Navigation-Common", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Navigation Common", "website": "https://developer.android.com/jetpack/androidx/releases/navigation#2.9.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.navigation:navigation-compose", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.9.0", "description": "Compose integration with Navigation", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose Navigation", "website": "https://developer.android.com/jetpack/androidx/releases/navigation#2.9.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.navigation:navigation-compose-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.9.0", "description": "Compose integration with Navigation", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Compose Navigation", "website": "https://developer.android.com/jetpack/androidx/releases/navigation#2.9.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.navigation:navigation-runtime", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.9.0", "description": "Android Navigation-Runtime", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Navigation Runtime", "website": "https://developer.android.com/jetpack/androidx/releases/navigation#2.9.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.navigation:navigation-runtime-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.9.0", "description": "Android Navigation-Runtime", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Navigation Runtime", "website": "https://developer.android.com/jetpack/androidx/releases/navigation#2.9.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.profileinstaller:profileinstaller", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.4.0", "description": "Allows libraries to prepopulate ahead of time compilation traces to be read by ART", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Profile Installer", "website": "https://developer.android.com/jetpack/androidx/releases/profileinstaller#1.4.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.resourceinspection:resourceinspection-annotation", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.0.1", "description": "Annotation processors for Android resource and layout inspection", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Resource Inspection - Annotations", "website": "https://developer.android.com/jetpack/androidx/releases/resourceinspection#1.0.1", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.room:room-common", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.7.1", "description": "Android Room-Common", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Room-Common", "website": "https://developer.android.com/jetpack/androidx/releases/room#2.7.1", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.room:room-common-jvm", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.7.1", "description": "Android Room-Common", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Room-Common", "website": "https://developer.android.com/jetpack/androidx/releases/room#2.7.1", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.room:room-ktx", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.7.1", "description": "Android Room Kotlin Extensions", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Room Kotlin Extensions", "website": "https://developer.android.com/jetpack/androidx/releases/room#2.7.1", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.room:room-runtime", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.7.1", "description": "Android Room-Runtime", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Room-Runtime", "website": "https://developer.android.com/jetpack/androidx/releases/room#2.7.1", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.room:room-runtime-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.7.1", "description": "Android Room-Runtime", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Room-Runtime", "website": "https://developer.android.com/jetpack/androidx/releases/room#2.7.1", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.savedstate:savedstate", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.3.0", "description": "Android Lifecycle Saved State", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Saved State", "website": "https://developer.android.com/jetpack/androidx/releases/savedstate#1.3.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.savedstate:savedstate-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.3.0", "description": "Android Lifecycle Saved State", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Saved State", "website": "https://developer.android.com/jetpack/androidx/releases/savedstate#1.3.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.savedstate:savedstate-compose", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.3.0", "description": "Compose integration with Saved State", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Saved State Compose", "website": "https://developer.android.com/jetpack/androidx/releases/savedstate#1.3.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.savedstate:savedstate-compose-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.3.0", "description": "Compose integration with Saved State", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Saved State Compose", "website": "https://developer.android.com/jetpack/androidx/releases/savedstate#1.3.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.savedstate:savedstate-ktx", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.3.0", "description": "Kotlin extensions for 'savedstate' artifact", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "SavedState Kotlin Extensions", "website": "https://developer.android.com/jetpack/androidx/releases/savedstate#1.3.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.sqlite:sqlite", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.5.0", "description": "SQLite API", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "SQLite", "website": "https://developer.android.com/jetpack/androidx/releases/sqlite#2.5.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.sqlite:sqlite-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.5.0", "description": "SQLite API", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "SQLite", "website": "https://developer.android.com/jetpack/androidx/releases/sqlite#2.5.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.sqlite:sqlite-framework", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.5.0", "description": "The implementation of SQLite library using the framework code.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "SQLite Framework Integration", "website": "https://developer.android.com/jetpack/androidx/releases/sqlite#2.5.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.sqlite:sqlite-framework-android", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.5.0", "description": "The implementation of SQLite library using the framework code.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "SQLite Framework Integration", "website": "https://developer.android.com/jetpack/androidx/releases/sqlite#2.5.0", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.startup:startup-runtime", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.1", "description": "Android App Startup Runtime", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android App Startup Runtime", "website": "https://developer.android.com/jetpack/androidx/releases/startup#1.1.1", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.tracing:tracing", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.2.0", "description": "Android Tracing", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Tracing", "website": "https://developer.android.com/jetpack/androidx/releases/tracing#1.2.0", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.tracing:tracing-ktx", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.2.0", "description": "Android Tracing", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "Android Tracing Runtime Kotlin Extensions", "website": "https://developer.android.com/jetpack/androidx/releases/tracing#1.2.0", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.vectordrawable:vectordrawable", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.0", "description": "Android Support VectorDrawable", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "Support VectorDrawable", "website": "https://developer.android.com/jetpack/androidx", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.vectordrawable:vectordrawable-animated", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.0", "description": "Android Support AnimatedVectorDrawable", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "Support AnimatedVectorDrawable", "website": "https://developer.android.com/jetpack/androidx", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.versionedparcelable:versionedparcelable", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.1.1", "description": "Provides a stable but relatively compact binary serialization format that can be passed across processes or persisted safely.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "VersionedParcelable", "website": "http://developer.android.com/tools/extras/support-library.html", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.viewpager:viewpager", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "1.0.0", "description": "The Support Library is a static library that you can add to your Android application in order to use APIs that are either not available for older platform versions or utility APIs that aren't a part of the framework APIs. Compatible on devices running API 14 or later.", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "http://source.android.com"}, "name": "Support View Pager", "website": "http://developer.android.com/tools/extras/support-library.html", "licenses": ["Apache-2.0"]}, {"uniqueId": "androidx.work:work-runtime", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.10.1", "description": "Android WorkManager runtime library", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "WorkManager Runtime", "website": "https://developer.android.com/jetpack/androidx/releases/work#2.10.1", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "androidx.work:work-runtime-ktx", "funding": [], "developers": [{"name": "The Android Open Source Project"}], "artifactVersion": "2.10.1", "description": "Android WorkManager Kotlin Extensions", "scm": {"connection": "scm:git:https://android.googlesource.com/platform/frameworks/support", "url": "https://cs.android.com/androidx/platform/frameworks/support"}, "name": "WorkManager Kotlin Extensions", "website": "https://developer.android.com/jetpack/androidx/releases/work#2.10.1", "licenses": ["Apache-2.0"], "organization": {"name": "The Android Open Source Project"}}, {"uniqueId": "co.touchlab:stately-concurrency", "funding": [], "developers": [{"name": "<PERSON>"}], "artifactVersion": "2.1.0", "description": "Multithreaded Kotlin Multiplatform Utilities", "scm": {"connection": "scm:git:git://github.com/touchlab/Stately.git", "url": "https://github.com/touchlab/Stately", "developerConnection": "scm:git:git://github.com/touchlab/Stately.git"}, "name": "Stately", "website": "https://github.com/touchlab/Stately", "licenses": ["Apache-2.0"]}, {"uniqueId": "co.touchlab:stately-concurrency-jvm", "funding": [], "developers": [{"name": "<PERSON>"}], "artifactVersion": "2.1.0", "description": "Multithreaded Kotlin Multiplatform Utilities", "scm": {"connection": "scm:git:git://github.com/touchlab/Stately.git", "url": "https://github.com/touchlab/Stately", "developerConnection": "scm:git:git://github.com/touchlab/Stately.git"}, "name": "Stately", "website": "https://github.com/touchlab/Stately", "licenses": ["Apache-2.0"]}, {"uniqueId": "co.touchlab:stately-concurrent-collections", "funding": [], "developers": [{"name": "<PERSON>"}], "artifactVersion": "2.1.0", "description": "Multithreaded Kotlin Multiplatform Utilities", "scm": {"connection": "scm:git:git://github.com/touchlab/Stately.git", "url": "https://github.com/touchlab/Stately", "developerConnection": "scm:git:git://github.com/touchlab/Stately.git"}, "name": "Stately", "website": "https://github.com/touchlab/Stately", "licenses": ["Apache-2.0"]}, {"uniqueId": "co.touchlab:stately-concurrent-collections-jvm", "funding": [], "developers": [{"name": "<PERSON>"}], "artifactVersion": "2.1.0", "description": "Multithreaded Kotlin Multiplatform Utilities", "scm": {"connection": "scm:git:git://github.com/touchlab/Stately.git", "url": "https://github.com/touchlab/Stately", "developerConnection": "scm:git:git://github.com/touchlab/Stately.git"}, "name": "Stately", "website": "https://github.com/touchlab/Stately", "licenses": ["Apache-2.0"]}, {"uniqueId": "co.touchlab:stately-strict", "funding": [], "developers": [{"name": "<PERSON>"}], "artifactVersion": "2.1.0", "description": "Multithreaded Kotlin Multiplatform Utilities", "scm": {"connection": "scm:git:git://github.com/touchlab/Stately.git", "url": "https://github.com/touchlab/Stately", "developerConnection": "scm:git:git://github.com/touchlab/Stately.git"}, "name": "Stately", "website": "https://github.com/touchlab/Stately", "licenses": ["Apache-2.0"]}, {"uniqueId": "co.touchlab:stately-strict-jvm", "funding": [], "developers": [{"name": "<PERSON>"}], "artifactVersion": "2.1.0", "description": "Multithreaded Kotlin Multiplatform Utilities", "scm": {"connection": "scm:git:git://github.com/touchlab/Stately.git", "url": "https://github.com/touchlab/Stately", "developerConnection": "scm:git:git://github.com/touchlab/Stately.git"}, "name": "Stately", "website": "https://github.com/touchlab/Stately", "licenses": ["Apache-2.0"]}, {"uniqueId": "com.google.accompanist:accompanist-drawablepainter", "funding": [], "developers": [{"name": "Google"}], "artifactVersion": "0.32.0", "description": "Utilities for Jetpack Compose", "scm": {"connection": "scm:git:git://github.com/google/accompanist.git", "url": "https://github.com/google/accompanist/", "developerConnection": "scm:git:git://github.com/google/accompanist.git"}, "name": "Accompanist <PERSON><PERSON> Painter library", "website": "https://github.com/google/accompanist/", "licenses": ["Apache-2.0"]}, {"uniqueId": "com.google.android.gms:play-services-base", "funding": [], "developers": [], "artifactVersion": "18.5.0", "description": "", "name": "play-services-base", "licenses": ["ASDKL"]}, {"uniqueId": "com.google.android.gms:play-services-basement", "funding": [], "developers": [], "artifactVersion": "18.4.0", "description": "", "name": "play-services-basement", "licenses": ["ASDKL"]}, {"uniqueId": "com.google.android.gms:play-services-location", "funding": [], "developers": [], "artifactVersion": "21.3.0", "description": "", "name": "play-services-location", "licenses": ["ASDKL"]}, {"uniqueId": "com.google.android.gms:play-services-tasks", "funding": [], "developers": [], "artifactVersion": "18.2.0", "description": "", "name": "play-services-tasks", "licenses": ["ASDKL"]}, {"uniqueId": "com.google.code.findbugs:jsr305", "funding": [], "developers": [], "artifactVersion": "3.0.2", "description": "JSR305 Annotations for Findbugs", "scm": {"connection": "scm:git:https://code.google.com/p/jsr-305/", "url": "https://code.google.com/p/jsr-305/", "developerConnection": "scm:git:https://code.google.com/p/jsr-305/"}, "name": "FindBugs-jsr305", "website": "http://findbugs.sourceforge.net/", "licenses": ["Apache-2.0"]}, {"uniqueId": "com.google.code.gson:gson", "funding": [], "developers": [{"organisationUrl": "https://www.google.com"}], "artifactVersion": "2.10.1", "description": "Gson JSON library", "scm": {"connection": "scm:git:https://github.com/google/gson.git/gson", "url": "https://github.com/google/gson/gson/", "developerConnection": "scm:git:**************:google/gson.git/gson"}, "name": "Gson", "website": "https://github.com/google/gson/gson", "licenses": ["Apache-2.0"]}, {"uniqueId": "com.google.dagger:dagger", "funding": [], "developers": [], "artifactVersion": "2.49", "description": "A fast dependency injector for Android and Java.", "scm": {"connection": "scm:git:git://github.com/google/dagger.git", "url": "https://github.com/google/dagger/", "developerConnection": "scm:git:ssh://**************/google/dagger.git"}, "name": "<PERSON>gger", "website": "https://github.com/google/dagger", "licenses": ["Apache-2.0"], "organization": {"url": "https://www.google.com", "name": "Google, Inc."}}, {"uniqueId": "com.google.dagger:dagger-lint-aar", "funding": [], "developers": [], "artifactVersion": "2.49", "description": "A fast dependency injector for Android and Java.", "scm": {"connection": "scm:git:git://github.com/google/dagger.git", "url": "https://github.com/google/dagger/", "developerConnection": "scm:git:ssh://**************/google/dagger.git"}, "name": "Dagger Lint Rules AAR Distribution", "website": "https://github.com/google/dagger", "licenses": ["Apache-2.0"], "organization": {"url": "https://www.google.com", "name": "Google, Inc."}}, {"uniqueId": "com.google.dagger:hilt-android", "funding": [], "developers": [], "artifactVersion": "2.49", "description": "A fast dependency injector for Android and Java.", "scm": {"connection": "scm:git:git://github.com/google/dagger.git", "url": "https://github.com/google/dagger/", "developerConnection": "scm:git:ssh://**************/google/dagger.git"}, "name": "Hilt Android", "website": "https://github.com/google/dagger", "licenses": ["Apache-2.0"], "organization": {"url": "https://www.google.com", "name": "Google, Inc."}}, {"uniqueId": "com.google.dagger:hilt-core", "funding": [], "developers": [], "artifactVersion": "2.49", "description": "A fast dependency injector for Android and Java.", "scm": {"connection": "scm:git:git://github.com/google/dagger.git", "url": "https://github.com/google/dagger/", "developerConnection": "scm:git:ssh://**************/google/dagger.git"}, "name": "<PERSON><PERSON>", "website": "https://github.com/google/dagger", "licenses": ["Apache-2.0"], "organization": {"url": "https://www.google.com", "name": "Google, Inc."}}, {"uniqueId": "com.google.guava:listenablefuture", "funding": [], "developers": [{"organisationUrl": "http://www.google.com", "name": "<PERSON>"}], "artifactVersion": "1.0", "description": "Contains Guava's com.google.common.util.concurrent.ListenableFuture class,\n    without any of its other classes -- but is also available in a second\n    \"version\" that omits the class to avoid conflicts with the copy in Guava\n    itself. The idea is:\n\n    - If users want only ListenableFuture, they depend on listenablefuture-1.0.\n\n    - If users want all of Guava, they depend on guava, which, as of Guava\n    27.0, depends on\n    listenablefuture-9999.0-empty-to-avoid-conflict-with-guava. The 9999.0-...\n    version number is enough for some build systems (notably, Gradle) to select\n    that empty artifact over the \"real\" listenablefuture-1.0 -- avoiding a\n    conflict with the copy of ListenableFuture in guava itself. If users are\n    using an older version of Guava or a build system other than Gradle, they\n    may see class conflicts. If so, they can solve them by manually excluding\n    the listenablefuture artifact or manually forcing their build systems to\n    use 9999.0-....", "scm": {"connection": "scm:git:https://github.com/google/guava.git/listenablefuture", "url": "https://github.com/google/guava/listenablefuture", "developerConnection": "scm:git:**************:google/guava.git/listenablefuture"}, "name": "Guava ListenableFuture only", "website": "https://github.com/google/guava/listenablefuture", "licenses": ["Apache-2.0"]}, {"uniqueId": "com.mikepenz:aboutlibraries-compose", "funding": [], "developers": [{"name": "<PERSON>"}], "artifactVersion": "12.1.2", "description": "AboutLibraries automatically detects all dependencies of a project and collects their information including the license. Optionally visualising it via the provided ui components.", "scm": {"connection": "scm:**************:mikepenz/AboutLibraries.git", "url": "https://github.com/mikepenz/AboutLibraries", "developerConnection": "scm:**************:mikepenz/AboutLibraries.git"}, "name": "AboutLibraries Compose Material 2 Library", "website": "https://github.com/mikepenz/AboutLibraries", "licenses": ["Apache-2.0"]}, {"uniqueId": "com.mikepenz:aboutlibraries-compose-android", "funding": [], "developers": [{"name": "<PERSON>"}], "artifactVersion": "12.1.2", "description": "AboutLibraries automatically detects all dependencies of a project and collects their information including the license. Optionally visualising it via the provided ui components.", "scm": {"connection": "scm:**************:mikepenz/AboutLibraries.git", "url": "https://github.com/mikepenz/AboutLibraries", "developerConnection": "scm:**************:mikepenz/AboutLibraries.git"}, "name": "AboutLibraries Compose Material 2 Library", "website": "https://github.com/mikepenz/AboutLibraries", "licenses": ["Apache-2.0"]}, {"uniqueId": "com.mikepenz:aboutlibraries-compose-core", "funding": [], "developers": [{"name": "<PERSON>"}], "artifactVersion": "12.1.2", "description": "AboutLibraries automatically detects all dependencies of a project and collects their information including the license. Optionally visualising it via the provided ui components.", "scm": {"connection": "scm:**************:mikepenz/AboutLibraries.git", "url": "https://github.com/mikepenz/AboutLibraries", "developerConnection": "scm:**************:mikepenz/AboutLibraries.git"}, "name": "AboutLibraries Compose UI Library", "website": "https://github.com/mikepenz/AboutLibraries", "licenses": ["Apache-2.0"]}, {"uniqueId": "com.mikepenz:aboutlibraries-compose-core-android", "funding": [], "developers": [{"name": "<PERSON>"}], "artifactVersion": "12.1.2", "description": "AboutLibraries automatically detects all dependencies of a project and collects their information including the license. Optionally visualising it via the provided ui components.", "scm": {"connection": "scm:**************:mikepenz/AboutLibraries.git", "url": "https://github.com/mikepenz/AboutLibraries", "developerConnection": "scm:**************:mikepenz/AboutLibraries.git"}, "name": "AboutLibraries Compose UI Library", "website": "https://github.com/mikepenz/AboutLibraries", "licenses": ["Apache-2.0"]}, {"uniqueId": "com.mikepenz:aboutlibraries-core", "funding": [], "developers": [{"name": "<PERSON>"}], "artifactVersion": "12.1.2", "description": "AboutLibraries automatically detects all dependencies of a project and collects their information including the license. Optionally visualising it via the provided ui components.", "scm": {"connection": "scm:**************:mikepenz/AboutLibraries.git", "url": "https://github.com/mikepenz/AboutLibraries", "developerConnection": "scm:**************:mikepenz/AboutLibraries.git"}, "name": "AboutLibraries Core Library", "website": "https://github.com/mikepenz/AboutLibraries", "licenses": ["Apache-2.0"]}, {"uniqueId": "com.mikepenz:aboutlibraries-core-android", "funding": [], "developers": [{"name": "<PERSON>"}], "artifactVersion": "12.1.2", "description": "AboutLibraries automatically detects all dependencies of a project and collects their information including the license. Optionally visualising it via the provided ui components.", "scm": {"connection": "scm:**************:mikepenz/AboutLibraries.git", "url": "https://github.com/mikepenz/AboutLibraries", "developerConnection": "scm:**************:mikepenz/AboutLibraries.git"}, "name": "AboutLibraries Core Library", "website": "https://github.com/mikepenz/AboutLibraries", "licenses": ["Apache-2.0"]}, {"uniqueId": "com.squareup.okhttp3:logging-interceptor", "funding": [], "developers": [{"name": "Square, Inc."}], "artifactVersion": "4.12.0", "description": "Square’s meticulous HTTP client for Java and Kotlin.", "scm": {"connection": "scm:git:https://github.com/square/okhttp.git", "url": "https://github.com/square/okhttp", "developerConnection": "scm:git:ssh://**************/square/okhttp.git"}, "name": "okhttp-logging-interceptor", "website": "https://square.github.io/okhttp/", "licenses": ["Apache-2.0"]}, {"uniqueId": "com.squareup.okhttp3:okhttp", "funding": [], "developers": [{"name": "Square, Inc."}], "artifactVersion": "4.12.0", "description": "Square’s meticulous HTTP client for Java and Kotlin.", "scm": {"connection": "scm:git:https://github.com/square/okhttp.git", "url": "https://github.com/square/okhttp", "developerConnection": "scm:git:ssh://**************/square/okhttp.git"}, "name": "okhttp", "website": "https://square.github.io/okhttp/", "licenses": ["Apache-2.0"]}, {"uniqueId": "com.squareup.okio:okio", "funding": [], "developers": [{"name": "Square, Inc."}], "artifactVersion": "3.9.0", "description": "A modern I/O library for Android, Java, and Kotlin Multiplatform.", "scm": {"connection": "scm:git:git://github.com/square/okio.git", "url": "https://github.com/square/okio/", "developerConnection": "scm:git:ssh://**************/square/okio.git"}, "name": "okio", "website": "https://github.com/square/okio/", "licenses": ["Apache-2.0"]}, {"uniqueId": "com.squareup.okio:okio-jvm", "funding": [], "developers": [{"name": "Square, Inc."}], "artifactVersion": "3.9.0", "description": "A modern I/O library for Android, Java, and Kotlin Multiplatform.", "scm": {"connection": "scm:git:git://github.com/square/okio.git", "url": "https://github.com/square/okio/", "developerConnection": "scm:git:ssh://**************/square/okio.git"}, "name": "okio", "website": "https://github.com/square/okio/", "licenses": ["Apache-2.0"]}, {"uniqueId": "com.squareup.retrofit2:converter-gson", "funding": [], "developers": [{"name": "Square, Inc."}], "artifactVersion": "2.11.0", "description": "A Retrofit Converter which uses Gson for serialization.", "scm": {"connection": "scm:git:git://github.com/square/retrofit.git", "url": "https://github.com/square/retrofit/", "developerConnection": "scm:git:ssh://**************/square/retrofit.git"}, "name": "Converter: <PERSON><PERSON>", "website": "https://github.com/square/retrofit", "licenses": ["Apache-2.0"]}, {"uniqueId": "com.squareup.retrofit2:retrofit", "funding": [], "developers": [{"name": "Square, Inc."}], "artifactVersion": "2.11.0", "description": "A type-safe HTTP client for Android and Java.", "scm": {"connection": "scm:git:git://github.com/square/retrofit.git", "url": "https://github.com/square/retrofit/", "developerConnection": "scm:git:ssh://**************/square/retrofit.git"}, "name": "Retrofit", "website": "https://github.com/square/retrofit", "licenses": ["Apache-2.0"]}, {"uniqueId": "io.coil-kt:coil", "funding": [], "developers": [{"name": "Coil Contributors"}], "artifactVersion": "2.7.0", "description": "An image loading library for Android backed by Kotlin Coroutines.", "scm": {"connection": "scm:git:git://github.com/coil-kt/coil.git", "url": "https://github.com/coil-kt/coil", "developerConnection": "scm:git:ssh://**************/coil-kt/coil.git"}, "name": "coil", "website": "https://github.com/coil-kt/coil", "licenses": ["Apache-2.0"]}, {"uniqueId": "io.coil-kt:coil-base", "funding": [], "developers": [{"name": "Coil Contributors"}], "artifactVersion": "2.7.0", "description": "An image loading library for Android backed by Kotlin Coroutines.", "scm": {"connection": "scm:git:git://github.com/coil-kt/coil.git", "url": "https://github.com/coil-kt/coil", "developerConnection": "scm:git:ssh://**************/coil-kt/coil.git"}, "name": "coil-base", "website": "https://github.com/coil-kt/coil", "licenses": ["Apache-2.0"]}, {"uniqueId": "io.coil-kt:coil-compose", "funding": [], "developers": [{"name": "Coil Contributors"}], "artifactVersion": "2.7.0", "description": "An image loading library for Android backed by Kotlin Coroutines.", "scm": {"connection": "scm:git:git://github.com/coil-kt/coil.git", "url": "https://github.com/coil-kt/coil", "developerConnection": "scm:git:ssh://**************/coil-kt/coil.git"}, "name": "coil-compose", "website": "https://github.com/coil-kt/coil", "licenses": ["Apache-2.0"]}, {"uniqueId": "io.coil-kt:coil-compose-base", "funding": [], "developers": [{"name": "Coil Contributors"}], "artifactVersion": "2.7.0", "description": "An image loading library for Android backed by Kotlin Coroutines.", "scm": {"connection": "scm:git:git://github.com/coil-kt/coil.git", "url": "https://github.com/coil-kt/coil", "developerConnection": "scm:git:ssh://**************/coil-kt/coil.git"}, "name": "coil-compose-base", "website": "https://github.com/coil-kt/coil", "licenses": ["Apache-2.0"]}, {"uniqueId": "io.insert-koin:koin-android", "funding": [], "developers": [{"name": "<PERSON><PERSON><PERSON>"}], "artifactVersion": "4.0.0", "description": "KOIN - Kotlin simple Dependency Injection Framework", "scm": {"connection": "https://github.com/InsertKoinIO/koin.git", "url": "https://github.com/InsertKoinIO/koin"}, "name": "<PERSON><PERSON>", "website": "https://insert-koin.io/", "licenses": ["Apache-2.0"]}, {"uniqueId": "io.insert-koin:koin-androidx-compose", "funding": [], "developers": [{"name": "<PERSON><PERSON><PERSON>"}], "artifactVersion": "4.0.0", "description": "KOIN - Kotlin simple Dependency Injection Framework", "scm": {"connection": "https://github.com/InsertKoinIO/koin.git", "url": "https://github.com/InsertKoinIO/koin"}, "name": "<PERSON><PERSON>", "website": "https://insert-koin.io/", "licenses": ["Apache-2.0"]}, {"uniqueId": "io.insert-koin:koin-androidx-workmanager", "funding": [], "developers": [{"name": "<PERSON><PERSON><PERSON>"}], "artifactVersion": "4.0.0", "description": "KOIN - Kotlin simple Dependency Injection Framework", "scm": {"connection": "https://github.com/InsertKoinIO/koin.git", "url": "https://github.com/InsertKoinIO/koin"}, "name": "<PERSON><PERSON>", "website": "https://insert-koin.io/", "licenses": ["Apache-2.0"]}, {"uniqueId": "io.insert-koin:koin-bom", "funding": [], "developers": [{"name": "<PERSON><PERSON><PERSON>"}], "artifactVersion": "4.0.0", "description": "KOIN - Kotlin simple Dependency Injection Framework", "scm": {"connection": "https://github.com/InsertKoinIO/koin.git", "url": "https://github.com/InsertKoinIO/koin"}, "name": "<PERSON><PERSON>", "website": "https://insert-koin.io/", "licenses": ["Apache-2.0"]}, {"uniqueId": "io.insert-koin:koin-compose", "funding": [], "developers": [{"name": "<PERSON><PERSON><PERSON>"}], "artifactVersion": "4.0.0", "description": "KOIN - Kotlin simple Dependency Injection Framework", "scm": {"connection": "https://github.com/InsertKoinIO/koin.git", "url": "https://github.com/InsertKoinIO/koin"}, "name": "<PERSON><PERSON>", "website": "https://insert-koin.io/", "licenses": ["Apache-2.0"]}, {"uniqueId": "io.insert-koin:koin-compose-jvm", "funding": [], "developers": [{"name": "<PERSON><PERSON><PERSON>"}], "artifactVersion": "4.0.0", "description": "KOIN - Kotlin simple Dependency Injection Framework", "scm": {"connection": "https://github.com/InsertKoinIO/koin.git", "url": "https://github.com/InsertKoinIO/koin"}, "name": "<PERSON><PERSON>", "website": "https://insert-koin.io/", "licenses": ["Apache-2.0"]}, {"uniqueId": "io.insert-koin:koin-core", "funding": [], "developers": [{"name": "<PERSON><PERSON><PERSON>"}], "artifactVersion": "4.0.0", "description": "KOIN - Kotlin simple Dependency Injection Framework", "scm": {"connection": "https://github.com/InsertKoinIO/koin.git", "url": "https://github.com/InsertKoinIO/koin"}, "name": "<PERSON><PERSON>", "website": "https://insert-koin.io/", "licenses": ["Apache-2.0"]}, {"uniqueId": "io.insert-koin:koin-core-jvm", "funding": [], "developers": [{"name": "<PERSON><PERSON><PERSON>"}], "artifactVersion": "4.0.0", "description": "KOIN - Kotlin simple Dependency Injection Framework", "scm": {"connection": "https://github.com/InsertKoinIO/koin.git", "url": "https://github.com/InsertKoinIO/koin"}, "name": "<PERSON><PERSON>", "website": "https://insert-koin.io/", "licenses": ["Apache-2.0"]}, {"uniqueId": "io.insert-koin:koin-core-viewmodel", "funding": [], "developers": [{"name": "<PERSON><PERSON><PERSON>"}], "artifactVersion": "4.0.0", "description": "KOIN - Kotlin simple Dependency Injection Framework", "scm": {"connection": "https://github.com/InsertKoinIO/koin.git", "url": "https://github.com/InsertKoinIO/koin"}, "name": "<PERSON><PERSON>", "website": "https://insert-koin.io/", "licenses": ["Apache-2.0"]}, {"uniqueId": "io.insert-koin:koin-core-viewmodel-jvm", "funding": [], "developers": [{"name": "<PERSON><PERSON><PERSON>"}], "artifactVersion": "4.0.0", "description": "KOIN - Kotlin simple Dependency Injection Framework", "scm": {"connection": "https://github.com/InsertKoinIO/koin.git", "url": "https://github.com/InsertKoinIO/koin"}, "name": "<PERSON><PERSON>", "website": "https://insert-koin.io/", "licenses": ["Apache-2.0"]}, {"uniqueId": "javax.inject:javax.inject", "funding": [], "developers": [], "artifactVersion": "1", "description": "The javax.inject API", "scm": {"url": "http://code.google.com/p/atinject/source/checkout"}, "name": "javax.inject", "website": "http://code.google.com/p/atinject/", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.androidx.core:core-bundle", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Compose Multiplatform Team"}], "artifactVersion": "1.0.0", "description": "Provides <PERSON><PERSON>le in Kotlin Multiplatform projects", "scm": {"connection": "scm:git:https://github.com/JetBrains/compose-jb.git", "url": "https://github.com/JetBrains/compose-jb", "developerConnection": "scm:git:https://github.com/JetBrains/compose-jb.git"}, "name": "androidx.core:core-bundle", "website": "https://github.com/JetBrains/compose-jb", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.androidx.core:core-bundle-android-debug", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Compose Multiplatform Team"}], "artifactVersion": "1.0.0", "description": "Provides <PERSON><PERSON>le in Kotlin Multiplatform projects", "scm": {"connection": "scm:git:https://github.com/JetBrains/compose-jb.git", "url": "https://github.com/JetBrains/compose-jb", "developerConnection": "scm:git:https://github.com/JetBrains/compose-jb.git"}, "name": "androidx.core:core-bundle", "website": "https://github.com/JetBrains/compose-jb", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.androidx.lifecycle:lifecycle-common", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Compose Multiplatform Team"}], "artifactVersion": "2.8.4", "description": "Android Lifecycle-Common", "scm": {"connection": "scm:git:https://github.com/JetBrains/compose-jb.git", "url": "https://github.com/JetBrains/compose-jb", "developerConnection": "scm:git:https://github.com/JetBrains/compose-jb.git"}, "name": "Lifecycle-Common", "website": "https://github.com/JetBrains/compose-jb", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.androidx.lifecycle:lifecycle-runtime", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Compose Multiplatform Team"}], "artifactVersion": "2.8.4", "description": "Android Lifecycle Runtime", "scm": {"connection": "scm:git:https://github.com/JetBrains/compose-jb.git", "url": "https://github.com/JetBrains/compose-jb", "developerConnection": "scm:git:https://github.com/JetBrains/compose-jb.git"}, "name": "Lifecycle Runtime", "website": "https://github.com/JetBrains/compose-jb", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.androidx.lifecycle:lifecycle-runtime-compose", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Compose Multiplatform Team"}], "artifactVersion": "2.8.4", "description": "Compose integration with Lifecycle", "scm": {"connection": "scm:git:https://github.com/JetBrains/compose-jb.git", "url": "https://github.com/JetBrains/compose-jb", "developerConnection": "scm:git:https://github.com/JetBrains/compose-jb.git"}, "name": "Lifecycle Runtime Compose", "website": "https://github.com/JetBrains/compose-jb", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.androidx.lifecycle:lifecycle-viewmodel", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Compose Multiplatform Team"}], "artifactVersion": "2.8.4", "description": "Android Lifecycle ViewModel", "scm": {"connection": "scm:git:https://github.com/JetBrains/compose-jb.git", "url": "https://github.com/JetBrains/compose-jb", "developerConnection": "scm:git:https://github.com/JetBrains/compose-jb.git"}, "name": "Lifecycle ViewModel", "website": "https://github.com/JetBrains/compose-jb", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.androidx.lifecycle:lifecycle-viewmodel-savedstate", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Compose Multiplatform Team"}], "artifactVersion": "2.8.0", "description": "Android Lifecycle ViewModel", "scm": {"connection": "scm:git:https://github.com/JetBrains/compose-jb.git", "url": "https://github.com/JetBrains/compose-jb", "developerConnection": "scm:git:https://github.com/JetBrains/compose-jb.git"}, "name": "Lifecycle ViewModel with SavedState", "website": "https://github.com/JetBrains/compose-jb", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.androidx.savedstate:savedstate", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Compose Multiplatform Team"}], "artifactVersion": "1.2.0", "description": "Android Lifecycle Saved State", "scm": {"connection": "scm:git:https://github.com/JetBrains/compose-jb.git", "url": "https://github.com/JetBrains/compose-jb", "developerConnection": "scm:git:https://github.com/JetBrains/compose-jb.git"}, "name": "Saved State", "website": "https://github.com/JetBrains/compose-jb", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.compose.animation:animation", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Compose Multiplatform Team"}], "artifactVersion": "1.8.0", "description": "Compose animation library", "scm": {"connection": "scm:git:https://github.com/JetBrains/compose-jb.git", "url": "https://github.com/JetBrains/compose-jb", "developerConnection": "scm:git:https://github.com/JetBrains/compose-jb.git"}, "name": "Compose Animation", "website": "https://github.com/JetBrains/compose-jb", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.compose.animation:animation-core", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Compose Multiplatform Team"}], "artifactVersion": "1.8.0", "description": "Animation engine and animation primitives that are the building blocks of the Compose animation library", "scm": {"connection": "scm:git:https://github.com/JetBrains/compose-jb.git", "url": "https://github.com/JetBrains/compose-jb", "developerConnection": "scm:git:https://github.com/JetBrains/compose-jb.git"}, "name": "Compose Animation Core", "website": "https://github.com/JetBrains/compose-jb", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.compose.annotation-internal:annotation", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Compose Multiplatform Team"}], "artifactVersion": "1.8.0", "description": "Provides source annotations for tooling and readability.", "scm": {"connection": "scm:git:https://github.com/JetBrains/compose-jb.git", "url": "https://github.com/JetBrains/compose-jb", "developerConnection": "scm:git:https://github.com/JetBrains/compose-jb.git"}, "name": "Annotation", "website": "https://github.com/JetBrains/compose-jb", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.compose.collection-internal:collection", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Compose Multiplatform Team"}], "artifactVersion": "1.8.0", "description": "Standalone efficient collections.", "scm": {"connection": "scm:git:https://github.com/JetBrains/compose-jb.git", "url": "https://github.com/JetBrains/compose-jb", "developerConnection": "scm:git:https://github.com/JetBrains/compose-jb.git"}, "name": "collections", "website": "https://github.com/JetBrains/compose-jb", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.compose.foundation:foundation", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Compose Multiplatform Team"}], "artifactVersion": "1.8.0", "description": "Higher level abstractions of the Compose UI primitives. This library is design system agnostic, providing the high-level building blocks for both application and design-system developers", "scm": {"connection": "scm:git:https://github.com/JetBrains/compose-jb.git", "url": "https://github.com/JetBrains/compose-jb", "developerConnection": "scm:git:https://github.com/JetBrains/compose-jb.git"}, "name": "Compose Foundation", "website": "https://github.com/JetBrains/compose-jb", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.compose.foundation:foundation-layout", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Compose Multiplatform Team"}], "artifactVersion": "1.8.0", "description": "Compose layout implementations", "scm": {"connection": "scm:git:https://github.com/JetBrains/compose-jb.git", "url": "https://github.com/JetBrains/compose-jb", "developerConnection": "scm:git:https://github.com/JetBrains/compose-jb.git"}, "name": "Compose Layouts", "website": "https://github.com/JetBrains/compose-jb", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.compose.material:material", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Compose Multiplatform Team"}], "artifactVersion": "1.8.0", "description": "Compose Material Design Components library", "scm": {"connection": "scm:git:https://github.com/JetBrains/compose-jb.git", "url": "https://github.com/JetBrains/compose-jb", "developerConnection": "scm:git:https://github.com/JetBrains/compose-jb.git"}, "name": "Compose Material Components", "website": "https://github.com/JetBrains/compose-jb", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.compose.material:material-ripple", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Compose Multiplatform Team"}], "artifactVersion": "1.8.0", "description": "Material ripple used to build interactive components", "scm": {"connection": "scm:git:https://github.com/JetBrains/compose-jb.git", "url": "https://github.com/JetBrains/compose-jb", "developerConnection": "scm:git:https://github.com/JetBrains/compose-jb.git"}, "name": "Compose Material Ripple", "website": "https://github.com/JetBrains/compose-jb", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.compose.runtime:runtime", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Compose Multiplatform Team"}], "artifactVersion": "1.8.0", "description": "Tree composition support for code generated by the Compose compiler plugin and corresponding public API", "scm": {"connection": "scm:git:https://github.com/JetBrains/compose-jb.git", "url": "https://github.com/JetBrains/compose-jb", "developerConnection": "scm:git:https://github.com/JetBrains/compose-jb.git"}, "name": "Compose Runtime", "website": "https://github.com/JetBrains/compose-jb", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.compose.runtime:runtime-saveable", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Compose Multiplatform Team"}], "artifactVersion": "1.8.0", "description": "Compose components that allow saving and restoring the local ui state", "scm": {"connection": "scm:git:https://github.com/JetBrains/compose-jb.git", "url": "https://github.com/JetBrains/compose-jb", "developerConnection": "scm:git:https://github.com/JetBrains/compose-jb.git"}, "name": "Compose Saveable", "website": "https://github.com/JetBrains/compose-jb", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.compose.ui:ui", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Compose Multiplatform Team"}], "artifactVersion": "1.8.0", "description": "Compose UI primitives. This library contains the primitives that form the Compose UI Toolkit, such as drawing, measurement and layout.", "scm": {"connection": "scm:git:https://github.com/JetBrains/compose-jb.git", "url": "https://github.com/JetBrains/compose-jb", "developerConnection": "scm:git:https://github.com/JetBrains/compose-jb.git"}, "name": "Compose UI primitives", "website": "https://github.com/JetBrains/compose-jb", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.compose.ui:ui-geometry", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Compose Multiplatform Team"}], "artifactVersion": "1.8.0", "description": "Compose classes related to dimensions without units", "scm": {"connection": "scm:git:https://github.com/JetBrains/compose-jb.git", "url": "https://github.com/JetBrains/compose-jb", "developerConnection": "scm:git:https://github.com/JetBrains/compose-jb.git"}, "name": "Compose Geometry", "website": "https://github.com/JetBrains/compose-jb", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.compose.ui:ui-graphics", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Compose Multiplatform Team"}], "artifactVersion": "1.8.0", "description": "Compose graphics", "scm": {"connection": "scm:git:https://github.com/JetBrains/compose-jb.git", "url": "https://github.com/JetBrains/compose-jb", "developerConnection": "scm:git:https://github.com/JetBrains/compose-jb.git"}, "name": "Compose Graphics", "website": "https://github.com/JetBrains/compose-jb", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.compose.ui:ui-text", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Compose Multiplatform Team"}], "artifactVersion": "1.8.0", "description": "Compose Text primitives and utilities", "scm": {"connection": "scm:git:https://github.com/JetBrains/compose-jb.git", "url": "https://github.com/JetBrains/compose-jb", "developerConnection": "scm:git:https://github.com/JetBrains/compose-jb.git"}, "name": "Compose UI Text", "website": "https://github.com/JetBrains/compose-jb", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.compose.ui:ui-tooling-preview", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Compose Multiplatform Team"}], "artifactVersion": "1.8.0", "description": "Compose tooling library API. This library provides the API required to declare @Preview composables in user apps.", "scm": {"connection": "scm:git:https://github.com/JetBrains/compose-jb.git", "url": "https://github.com/JetBrains/compose-jb", "developerConnection": "scm:git:https://github.com/JetBrains/compose-jb.git"}, "name": "Compose UI Preview Tooling", "website": "https://github.com/JetBrains/compose-jb", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.compose.ui:ui-unit", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Compose Multiplatform Team"}], "artifactVersion": "1.8.0", "description": "Compose classes for simple units", "scm": {"connection": "scm:git:https://github.com/JetBrains/compose-jb.git", "url": "https://github.com/JetBrains/compose-jb", "developerConnection": "scm:git:https://github.com/JetBrains/compose-jb.git"}, "name": "Compose Unit", "website": "https://github.com/JetBrains/compose-jb", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.compose.ui:ui-util", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Compose Multiplatform Team"}], "artifactVersion": "1.8.0", "description": "Internal Compose utilities used by other modules", "scm": {"connection": "scm:git:https://github.com/JetBrains/compose-jb.git", "url": "https://github.com/JetBrains/compose-jb", "developerConnection": "scm:git:https://github.com/JetBrains/compose-jb.git"}, "name": "Compose <PERSON>", "website": "https://github.com/JetBrains/compose-jb", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.kotlin:kotlin-android-extensions-runtime", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Kotlin Team"}], "artifactVersion": "1.9.22", "description": "Kotlin Android Extensions Runtime", "scm": {"connection": "scm:git:https://github.com/JetBrains/kotlin.git", "url": "https://github.com/JetBrains/kotlin", "developerConnection": "scm:git:https://github.com/JetBrains/kotlin.git"}, "name": "Kotlin Android Extensions Runtime", "website": "https://kotlinlang.org/", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.kotlin:kotlin-parcelize-runtime", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Kotlin Team"}], "artifactVersion": "1.9.22", "description": "Runtime library for the Parcelize compiler plugin", "scm": {"connection": "scm:git:https://github.com/JetBrains/kotlin.git", "url": "https://github.com/JetBrains/kotlin", "developerConnection": "scm:git:https://github.com/JetBrains/kotlin.git"}, "name": "Parcelize Runtime", "website": "https://kotlinlang.org/", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.kotlin:kotlin-stdlib", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Kotlin Team"}], "artifactVersion": "2.1.21", "description": "Kotlin Standard Library", "scm": {"connection": "scm:git:https://github.com/JetBrains/kotlin.git", "url": "https://github.com/JetBrains/kotlin", "developerConnection": "scm:git:https://github.com/JetBrains/kotlin.git"}, "name": "<PERSON><PERSON><PERSON>", "website": "https://kotlinlang.org/", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.kotlin:kotlin-stdlib-common", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Kotlin Team"}], "artifactVersion": "2.1.21", "description": "Kotlin Common Standard Library (legacy, use kotlin-stdlib instead)", "scm": {"connection": "scm:git:https://github.com/JetBrains/kotlin.git", "url": "https://github.com/JetBrains/kotlin", "developerConnection": "scm:git:https://github.com/JetBrains/kotlin.git"}, "name": "<PERSON><PERSON><PERSON>", "website": "https://kotlinlang.org/", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.kotlin:kotlin-stdlib-jdk7", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Kotlin Team"}], "artifactVersion": "1.9.0", "description": "Kotlin Standard Library JDK 7 extension", "scm": {"connection": "scm:git:https://github.com/JetBrains/kotlin.git", "url": "https://github.com/JetBrains/kotlin", "developerConnection": "scm:git:https://github.com/JetBrains/kotlin.git"}, "name": "Kotlin Stdlib Jdk7", "website": "https://kotlinlang.org/", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.kotlin:kotlin-stdlib-jdk8", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "Kotlin Team"}], "artifactVersion": "1.9.0", "description": "Kotlin Standard Library JDK 8 extension", "scm": {"connection": "scm:git:https://github.com/JetBrains/kotlin.git", "url": "https://github.com/JetBrains/kotlin", "developerConnection": "scm:git:https://github.com/JetBrains/kotlin.git"}, "name": "Kotlin Stdlib Jdk8", "website": "https://kotlinlang.org/", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.kotlinx:kotlinx-collections-immutable", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "JetBrains Team"}], "artifactVersion": "0.3.8", "description": "Kotlin Immutable Collections multiplatform library", "scm": {"url": "https://github.com/Kotlin/kotlinx.collections.immutable"}, "name": "kotlinx-collections-immutable", "website": "https://github.com/Kotlin/kotlinx.collections.immutable", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.kotlinx:kotlinx-collections-immutable-jvm", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "JetBrains Team"}], "artifactVersion": "0.3.8", "description": "Kotlin Immutable Collections multiplatform library", "scm": {"url": "https://github.com/Kotlin/kotlinx.collections.immutable"}, "name": "kotlinx-collections-immutable", "website": "https://github.com/Kotlin/kotlinx.collections.immutable", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.kotlinx:kotlinx-coroutines-android", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "JetBrains Team"}], "artifactVersion": "1.10.2", "description": "Coroutines support libraries for Kotlin", "scm": {"url": "https://github.com/Kotlin/kotlinx.coroutines"}, "name": "kotlinx-coroutines-android", "website": "https://github.com/Kotlin/kotlinx.coroutines", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.kotlinx:kotlinx-coroutines-bom", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "JetBrains Team"}], "artifactVersion": "1.10.2", "description": "Coroutines support libraries for Kotlin", "scm": {"url": "https://github.com/Kotlin/kotlinx.coroutines"}, "name": "kotlinx-coroutines-bom", "website": "https://github.com/Kotlin/kotlinx.coroutines", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.kotlinx:kotlinx-coroutines-core", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "JetBrains Team"}], "artifactVersion": "1.10.2", "description": "Coroutines support libraries for Kotlin", "scm": {"url": "https://github.com/Kotlin/kotlinx.coroutines"}, "name": "kotlinx-coroutines-core", "website": "https://github.com/Kotlin/kotlinx.coroutines", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "JetBrains Team"}], "artifactVersion": "1.10.2", "description": "Coroutines support libraries for Kotlin", "scm": {"url": "https://github.com/Kotlin/kotlinx.coroutines"}, "name": "kotlinx-coroutines-core", "website": "https://github.com/Kotlin/kotlinx.coroutines", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.kotlinx:kotlinx-datetime", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "JetBrains Team"}], "artifactVersion": "0.6.2", "description": "<PERSON><PERSON>in <PERSON>time Library", "scm": {"url": "https://github.com/Kotlin/kotlinx-datetime"}, "name": "kotlinx-datetime", "website": "https://github.com/Kotlin/kotlinx-datetime", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.kotlinx:kotlinx-datetime-jvm", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "JetBrains Team"}], "artifactVersion": "0.6.2", "description": "<PERSON><PERSON>in <PERSON>time Library", "scm": {"url": "https://github.com/Kotlin/kotlinx-datetime"}, "name": "kotlinx-datetime", "website": "https://github.com/Kotlin/kotlinx-datetime", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.kotlinx:kotlinx-serialization-bom", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "JetBrains Team"}], "artifactVersion": "1.8.1", "description": "Kotlin multiplatform serialization runtime library", "scm": {"url": "https://github.com/Kotlin/kotlinx.serialization"}, "name": "kotlinx-serialization-bom", "website": "https://github.com/Kotlin/kotlinx.serialization", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.kotlinx:kotlinx-serialization-core", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "JetBrains Team"}], "artifactVersion": "1.8.1", "description": "Kotlin multiplatform serialization runtime library", "scm": {"url": "https://github.com/Kotlin/kotlinx.serialization"}, "name": "kotlinx-serialization-core", "website": "https://github.com/Kotlin/kotlinx.serialization", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.kotlinx:kotlinx-serialization-core-jvm", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "JetBrains Team"}], "artifactVersion": "1.8.1", "description": "Kotlin multiplatform serialization runtime library", "scm": {"url": "https://github.com/Kotlin/kotlinx.serialization"}, "name": "kotlinx-serialization-core", "website": "https://github.com/Kotlin/kotlinx.serialization", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.kotlinx:kotlinx-serialization-json", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "JetBrains Team"}], "artifactVersion": "1.8.1", "description": "Kotlin multiplatform serialization runtime library", "scm": {"url": "https://github.com/Kotlin/kotlinx.serialization"}, "name": "kotlinx-serialization-json", "website": "https://github.com/Kotlin/kotlinx.serialization", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains.kotlinx:kotlinx-serialization-json-jvm", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "JetBrains Team"}], "artifactVersion": "1.8.1", "description": "Kotlin multiplatform serialization runtime library", "scm": {"url": "https://github.com/Kotlin/kotlinx.serialization"}, "name": "kotlinx-serialization-json", "website": "https://github.com/Kotlin/kotlinx.serialization", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jetbrains:annotations", "funding": [], "developers": [{"organisationUrl": "https://www.jetbrains.com", "name": "JetBrains Team"}], "artifactVersion": "23.0.0", "description": "A set of annotations used for code inspection support and code documentation.", "scm": {"connection": "scm:git:git://github.com/JetBrains/java-annotations.git", "url": "https://github.com/JetBrains/java-annotations", "developerConnection": "scm:git:ssh://github.com:JetBrains/java-annotations.git"}, "name": "JetBrains Java Annotations", "website": "https://github.com/JetBrains/java-annotations", "licenses": ["Apache-2.0"]}, {"uniqueId": "org.jspecify:jspecify", "funding": [], "developers": [{"name": "<PERSON>"}], "artifactVersion": "1.0.0", "description": "An artifact of well-named and well-specified annotations to power static analysis checks", "scm": {"connection": "scm:git:**************:jspecify/jspecify.git", "url": "https://github.com/jspecify/jspecify/", "developerConnection": "scm:git:**************:jspecify/jspecify.git"}, "name": "JSpecify annotations", "website": "http://jspecify.org/", "licenses": ["Apache-2.0"]}], "licenses": {"ASDKL": {"hash": "ASDKL", "internalHash": "ASDKL", "url": "https://developer.android.com/studio/terms.html", "spdxId": "ASDKL", "name": "Android Software Development Kit License"}, "Apache-2.0": {"content": "Apache License\nVersion 2.0, January 2004\nhttp://www.apache.org/licenses/\n\nTERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION\n\n1. Definitions.\n\n\"License\" shall mean the terms and conditions for use, reproduction, and distribution as defined by Sections 1 through 9 of this document.\n\n\"Licensor\" shall mean the copyright owner or entity authorized by the copyright owner that is granting the License.\n\n\"Legal Entity\" shall mean the union of the acting entity and all other entities that control, are controlled by, or are under common control with that entity. For the purposes of this definition, \"control\" means (i) the power, direct or indirect, to cause the direction or management of such entity, whether by contract or otherwise, or (ii) ownership of fifty percent (50%) or more of the outstanding shares, or (iii) beneficial ownership of such entity.\n\n\"You\" (or \"Your\") shall mean an individual or Legal Entity exercising permissions granted by this License.\n\n\"Source\" form shall mean the preferred form for making modifications, including but not limited to software source code, documentation source, and configuration files.\n\n\"Object\" form shall mean any form resulting from mechanical transformation or translation of a Source form, including but not limited to compiled object code, generated documentation, and conversions to other media types.\n\n\"Work\" shall mean the work of authorship, whether in Source or Object form, made available under the License, as indicated by a copyright notice that is included in or attached to the work (an example is provided in the Appendix below).\n\n\"Derivative Works\" shall mean any work, whether in Source or Object form, that is based on (or derived from) the Work and for which the editorial revisions, annotations, elaborations, or other modifications represent, as a whole, an original work of authorship. For the purposes of this License, Derivative Works shall not include works that remain separable from, or merely link (or bind by name) to the interfaces of, the Work and Derivative Works thereof.\n\n\"Contribution\" shall mean any work of authorship, including the original version of the Work and any modifications or additions to that Work or Derivative Works thereof, that is intentionally submitted to Licensor for inclusion in the Work by the copyright owner or by an individual or Legal Entity authorized to submit on behalf of the copyright owner. For the purposes of this definition, \"submitted\" means any form of electronic, verbal, or written communication sent to the Licensor or its representatives, including but not limited to communication on electronic mailing lists, source code control systems, and issue tracking systems that are managed by, or on behalf of, the Licensor for the purpose of discussing and improving the Work, but excluding communication that is conspicuously marked or otherwise designated in writing by the copyright owner as \"Not a Contribution.\"\n\n\"Contributor\" shall mean Licensor and any individual or Legal Entity on behalf of whom a Contribution has been received by Licensor and subsequently incorporated within the Work.\n\n2. Grant of Copyright License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable copyright license to reproduce, prepare Derivative Works of, publicly display, publicly perform, sublicense, and distribute the Work and such Derivative Works in Source or Object form.\n\n3. Grant of Patent License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable (except as stated in this section) patent license to make, have made, use, offer to sell, sell, import, and otherwise transfer the Work, where such license applies only to those patent claims licensable by such Contributor that are necessarily infringed by their Contribution(s) alone or by combination of their Contribution(s) with the Work to which such Contribution(s) was submitted. If You institute patent litigation against any entity (including a cross-claim or counterclaim in a lawsuit) alleging that the Work or a Contribution incorporated within the Work constitutes direct or contributory patent infringement, then any patent licenses granted to You under this License for that Work shall terminate as of the date such litigation is filed.\n\n4. Redistribution. You may reproduce and distribute copies of the Work or Derivative Works thereof in any medium, with or without modifications, and in Source or Object form, provided that You meet the following conditions:\n\n     (a) You must give any other recipients of the Work or Derivative Works a copy of this License; and\n\n     (b) You must cause any modified files to carry prominent notices stating that You changed the files; and\n\n     (c) You must retain, in the Source form of any Derivative Works that You distribute, all copyright, patent, trademark, and attribution notices from the Source form of the Work, excluding those notices that do not pertain to any part of the Derivative Works; and\n\n     (d) If the Work includes a \"NOTICE\" text file as part of its distribution, then any Derivative Works that You distribute must include a readable copy of the attribution notices contained within such NOTICE file, excluding those notices that do not pertain to any part of the Derivative Works, in at least one of the following places: within a NOTICE text file distributed as part of the Derivative Works; within the Source form or documentation, if provided along with the Derivative Works; or, within a display generated by the Derivative Works, if and wherever such third-party notices normally appear. The contents of the NOTICE file are for informational purposes only and do not modify the License. You may add Your own attribution notices within Derivative Works that You distribute, alongside or as an addendum to the NOTICE text from the Work, provided that such additional attribution notices cannot be construed as modifying the License.\n\n     You may add Your own copyright statement to Your modifications and may provide additional or different license terms and conditions for use, reproduction, or distribution of Your modifications, or for any such Derivative Works as a whole, provided Your use, reproduction, and distribution of the Work otherwise complies with the conditions stated in this License.\n\n5. Submission of Contributions. Unless You explicitly state otherwise, any Contribution intentionally submitted for inclusion in the Work by You to the Licensor shall be under the terms and conditions of this License, without any additional terms or conditions. Notwithstanding the above, nothing herein shall supersede or modify the terms of any separate license agreement you may have executed with Licensor regarding such Contributions.\n\n6. Trademarks. This License does not grant permission to use the trade names, trademarks, service marks, or product names of the Licensor, except as required for reasonable and customary use in describing the origin of the Work and reproducing the content of the NOTICE file.\n\n7. Disclaimer of Warranty. Unless required by applicable law or agreed to in writing, Licensor provides the Work (and each Contributor provides its Contributions) on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied, including, without limitation, any warranties or conditions of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A PARTICULAR PURPOSE. You are solely responsible for determining the appropriateness of using or redistributing the Work and assume any risks associated with Your exercise of permissions under this License.\n\n8. Limitation of Liability. In no event and under no legal theory, whether in tort (including negligence), contract, or otherwise, unless required by applicable law (such as deliberate and grossly negligent acts) or agreed to in writing, shall any Contributor be liable to You for damages, including any direct, indirect, special, incidental, or consequential damages of any character arising as a result of this License or out of the use or inability to use the Work (including but not limited to damages for loss of goodwill, work stoppage, computer failure or malfunction, or any and all other commercial damages or losses), even if such Contributor has been advised of the possibility of such damages.\n\n9. Accepting Warranty or Additional Liability. While redistributing the Work or Derivative Works thereof, You may choose to offer, and charge a fee for, acceptance of support, warranty, indemnity, or other liability obligations and/or rights consistent with this License. However, in accepting such obligations, You may act only on Your own behalf and on Your sole responsibility, not on behalf of any other Contributor, and only if You agree to indemnify, defend, and hold each Contributor harmless for any liability incurred by, or claims asserted against, such Contributor by reason of your accepting any such warranty or additional liability.\n\nEND OF TERMS AND CONDITIONS\n\nAPPENDIX: How to apply the Apache License to your work.\n\nTo apply the Apache License to your work, attach the following boilerplate notice, with the fields enclosed by brackets \"[]\" replaced with your own identifying information. (Don't include the brackets!)  The text should be enclosed in the appropriate comment syntax for the file format. We also recommend that a file or class name and description of purpose be included on the same \"printed page\" as the copyright notice for easier identification within third-party archives.\n\nCopyright [yyyy] [name of copyright owner]\n\nLicensed under the Apache License, Version 2.0 (the \"License\");\nyou may not use this file except in compliance with the License.\nYou may obtain a copy of the License at\n\nhttp://www.apache.org/licenses/LICENSE-2.0\n\nUnless required by applicable law or agreed to in writing, software\ndistributed under the License is distributed on an \"AS IS\" BASIS,\nWITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\nSee the License for the specific language governing permissions and\nlimitations under the License.", "hash": "Apache-2.0", "internalHash": "Apache-2.0", "url": "https://spdx.org/licenses/Apache-2.0.html", "spdxId": "Apache-2.0", "name": "Apache License 2.0"}, "BSD-3-Clause": {"content": "Copyright (c) <<var;name=copyright;original= <year> <owner>;match=.+>>. All rights reserved. \n\nRedistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:\n\n1. Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer. \n\n2. Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution. \n\n3. Neither the name of <<var;name=organizationClause3;original=the copyright holder;match=.+>> nor the names of its contributors may be used to endorse or promote products derived from this software without specific prior written permission.\n\nTHIS SOFTWARE IS PROVIDED BY <<var;name=copyrightHolderAsIs;original=THE COPYRIGHT HOLDERS AND CONTRIBUTORS;match=.+>> \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL <<var;name=copyrightHolderLiability;original=THE COPYRIGHT HOLDER OR CONTRIBUTORS;match=.+>> BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. ", "hash": "BSD-3-<PERSON><PERSON>", "internalHash": "BSD-3-<PERSON><PERSON>", "url": "https://spdx.org/licenses/BSD-3-Clause.html", "spdxId": "BSD-3-<PERSON><PERSON>", "name": "BSD 3-Clause \"New\" or \"Revised\" License"}}}