package com.example.weatherwidget.data

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.*
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import java.io.IOException

/**
 * Singleton object to manage widget preferences using Jetpack DataStore
 */
class WidgetPreferencesManager(private val context: Context) {

    companion object {
        private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(
            name = "widget_preferences"
        )
        
        // Keys for preferences
        private val SELECTED_CITY_ID = intPreferencesKey("selected_city_id")
        private val UPDATE_FREQUENCY_HOURS = intPreferencesKey("update_frequency_hours")
        private val API_KEY = stringPreferencesKey("api_key")
        private val LAST_UPDATE_TIME = longPreferencesKey("last_update_time")
        
        // Default values
        const val DEFAULT_UPDATE_FREQUENCY_HOURS = 1
        const val DEFAULT_CITY_ID = -1
    }

    /**
     * Get the selected city ID for the widget
     */
    val selectedCityIdFlow: Flow<Int> = context.dataStore.data
        .catch { exception ->
            if (exception is IOException) {
                emit(emptyPreferences())
            } else {
                throw exception
            }
        }
        .map { preferences ->
            preferences[SELECTED_CITY_ID] ?: DEFAULT_CITY_ID
        }

    /**
     * Get the update frequency in hours
     */
    val updateFrequencyHoursFlow: Flow<Int> = context.dataStore.data
        .catch { exception ->
            if (exception is IOException) {
                emit(emptyPreferences())
            } else {
                throw exception
            }
        }
        .map { preferences ->
            preferences[UPDATE_FREQUENCY_HOURS] ?: DEFAULT_UPDATE_FREQUENCY_HOURS
        }

    /**
     * Get the API key
     */
    val apiKeyFlow: Flow<String> = context.dataStore.data
        .catch { exception ->
            if (exception is IOException) {
                emit(emptyPreferences())
            } else {
                throw exception
            }
        }
        .map { preferences ->
            preferences[API_KEY] ?: ""
        }

    /**
     * Get the last update time in milliseconds
     */
    val lastUpdateTimeFlow: Flow<Long> = context.dataStore.data
        .catch { exception ->
            if (exception is IOException) {
                emit(emptyPreferences())
            } else {
                throw exception
            }
        }
        .map { preferences ->
            preferences[LAST_UPDATE_TIME] ?: 0L
        }

    /**
     * Save the selected city ID
     */
    suspend fun saveSelectedCityId(cityId: Int) {
        context.dataStore.edit { preferences ->
            preferences[SELECTED_CITY_ID] = cityId
        }
    }

    /**
     * Save the update frequency in hours
     */
    suspend fun saveUpdateFrequencyHours(hours: Int) {
        context.dataStore.edit { preferences ->
            preferences[UPDATE_FREQUENCY_HOURS] = hours
        }
    }

    /**
     * Save the API key
     */
    suspend fun saveApiKey(apiKey: String) {
        context.dataStore.edit { preferences ->
            preferences[API_KEY] = apiKey
        }
    }

    /**
     * Save the last update time
     */
    suspend fun saveLastUpdateTime(timeMillis: Long) {
        context.dataStore.edit { preferences ->
            preferences[LAST_UPDATE_TIME] = timeMillis
        }
    }
}