package com.example.weatherwidget.data

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.*
import androidx.datastore.preferences.preferencesDataStore
import com.example.weatherwidget.data.model.WidgetConfiguration
import com.example.weatherwidget.data.model.DefaultWidgetConfiguration
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.Json
import java.io.IOException

/**
 * Singleton object to manage widget preferences using Jetpack DataStore
 */
class WidgetPreferencesManager(private val context: Context) {

    companion object {
        private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(
            name = "widget_preferences"
        )

        // Keys for preferences
        private val SELECTED_CITY_ID = intPreferencesKey("selected_city_id")
        private val UPDATE_FREQUENCY_HOURS = intPreferencesKey("update_frequency_hours")
        private val API_KEY = stringPreferencesKey("api_key")
        private val LAST_UPDATE_TIME = longPreferencesKey("last_update_time")

        // Widget configuration keys (per widget instance)
        private fun widgetConfigKey(widgetId: Int) = stringPreferencesKey("widget_config_$widgetId")

        // Default values
        const val DEFAULT_UPDATE_FREQUENCY_HOURS = 1
        const val DEFAULT_CITY_ID = -1

        // JSON serializer for widget configurations
        private val json = Json {
            ignoreUnknownKeys = true
            encodeDefaults = true
        }
    }

    /**
     * Get the selected city ID for the widget
     */
    val selectedCityIdFlow: Flow<Int> = context.dataStore.data
        .catch { exception ->
            if (exception is IOException) {
                emit(emptyPreferences())
            } else {
                throw exception
            }
        }
        .map { preferences ->
            preferences[SELECTED_CITY_ID] ?: DEFAULT_CITY_ID
        }

    /**
     * Get the update frequency in hours
     */
    val updateFrequencyHoursFlow: Flow<Int> = context.dataStore.data
        .catch { exception ->
            if (exception is IOException) {
                emit(emptyPreferences())
            } else {
                throw exception
            }
        }
        .map { preferences ->
            preferences[UPDATE_FREQUENCY_HOURS] ?: DEFAULT_UPDATE_FREQUENCY_HOURS
        }

    /**
     * Get the API key
     */
    val apiKeyFlow: Flow<String> = context.dataStore.data
        .catch { exception ->
            if (exception is IOException) {
                emit(emptyPreferences())
            } else {
                throw exception
            }
        }
        .map { preferences ->
            preferences[API_KEY] ?: ""
        }

    /**
     * Get the last update time in milliseconds
     */
    val lastUpdateTimeFlow: Flow<Long> = context.dataStore.data
        .catch { exception ->
            if (exception is IOException) {
                emit(emptyPreferences())
            } else {
                throw exception
            }
        }
        .map { preferences ->
            preferences[LAST_UPDATE_TIME] ?: 0L
        }

    /**
     * Save the selected city ID
     */
    suspend fun saveSelectedCityId(cityId: Int) {
        context.dataStore.edit { preferences ->
            preferences[SELECTED_CITY_ID] = cityId
        }
    }

    /**
     * Save the update frequency in hours
     */
    suspend fun saveUpdateFrequencyHours(hours: Int) {
        context.dataStore.edit { preferences ->
            preferences[UPDATE_FREQUENCY_HOURS] = hours
        }
    }

    /**
     * Save the API key
     */
    suspend fun saveApiKey(apiKey: String) {
        context.dataStore.edit { preferences ->
            preferences[API_KEY] = apiKey
        }
    }

    /**
     * Save the last update time
     */
    suspend fun saveLastUpdateTime(timeMillis: Long) {
        context.dataStore.edit { preferences ->
            preferences[LAST_UPDATE_TIME] = timeMillis
        }
    }

    /**
     * Get widget configuration for a specific widget instance
     */
    fun getWidgetConfiguration(widgetId: Int): Flow<WidgetConfiguration> = context.dataStore.data
        .catch { exception ->
            if (exception is IOException) {
                emit(emptyPreferences())
            } else {
                throw exception
            }
        }
        .map { preferences ->
            val configJson = preferences[widgetConfigKey(widgetId)]
            if (configJson != null) {
                try {
                    json.decodeFromString<WidgetConfiguration>(configJson)
                } catch (e: Exception) {
                    DefaultWidgetConfiguration.create(widgetId)
                }
            } else {
                DefaultWidgetConfiguration.create(widgetId)
            }
        }

    /**
     * Save widget configuration for a specific widget instance
     */
    suspend fun saveWidgetConfiguration(config: WidgetConfiguration) {
        context.dataStore.edit { preferences ->
            val configJson = json.encodeToString(config)
            preferences[widgetConfigKey(config.widgetId)] = configJson
        }
    }

    /**
     * Delete widget configuration for a specific widget instance
     */
    suspend fun deleteWidgetConfiguration(widgetId: Int) {
        context.dataStore.edit { preferences ->
            preferences.remove(widgetConfigKey(widgetId))
        }
    }

    /**
     * Get all widget configurations
     */
    fun getAllWidgetConfigurations(): Flow<Map<Int, WidgetConfiguration>> = context.dataStore.data
        .catch { exception ->
            if (exception is IOException) {
                emit(emptyPreferences())
            } else {
                throw exception
            }
        }
        .map { preferences ->
            val configs = mutableMapOf<Int, WidgetConfiguration>()
            preferences.asMap().forEach { (key, value) ->
                if (key.name.startsWith("widget_config_") && value is String) {
                    try {
                        val widgetId = key.name.removePrefix("widget_config_").toInt()
                        val config = json.decodeFromString<WidgetConfiguration>(value)
                        configs[widgetId] = config
                    } catch (e: Exception) {
                        // Skip invalid configurations
                    }
                }
            }
            configs
        }
}