<svg xmlns="http://www.w3.org/2000/svg" width="800" height="600" viewBox="0 0 800 600">
  <style>
    .box { fill: #E3F2FD; stroke: #2196F3; stroke-width: 2; }
    .arrow { stroke: #757575; stroke-width: 2; marker-end: url(#arrowhead); }
    .text { font-family: Arial, sans-serif; font-size: 14px; fill: #212121; }
    .title { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #1976D2; }
    .subtitle { font-family: Arial, sans-serif; font-size: 12px; fill: #757575; }
  </style>
  
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#757575" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="400" y="30" text-anchor="middle" class="title" font-size="20">Weather Widget Architecture Flow</text>
  
  <!-- Components -->
  <!-- User Interface -->
  <rect x="50" y="80" width="180" height="100" rx="5" class="box" />
  <text x="140" y="100" text-anchor="middle" class="title">User Interface</text>
  <text x="140" y="120" text-anchor="middle" class="text">MainActivity</text>
  <text x="140" y="140" text-anchor="middle" class="text">WeatherWidgetConfigActivity</text>
  <text x="140" y="160" text-anchor="middle" class="text">WeatherWidget (Glance)</text>
  
  <!-- ViewModels -->
  <rect x="310" y="80" width="180" height="100" rx="5" class="box" />
  <text x="400" y="100" text-anchor="middle" class="title">ViewModels</text>
  <text x="400" y="120" text-anchor="middle" class="text">MainViewModel</text>
  <text x="400" y="140" text-anchor="middle" class="text">WeatherWidgetViewModel</text>
  <text x="400" y="160" text-anchor="middle" class="subtitle">(Manages UI state)</text>
  
  <!-- Repository -->
  <rect x="310" y="240" width="180" height="100" rx="5" class="box" />
  <text x="400" y="260" text-anchor="middle" class="title">Repository</text>
  <text x="400" y="280" text-anchor="middle" class="text">WeatherRepository</text>
  <text x="400" y="300" text-anchor="middle" class="subtitle">(Coordinates data sources)</text>
  
  <!-- Local Storage -->
  <rect x="50" y="400" width="180" height="100" rx="5" class="box" />
  <text x="140" y="420" text-anchor="middle" class="title">Local Storage</text>
  <text x="140" y="440" text-anchor="middle" class="text">Room Database</text>
  <text x="140" y="460" text-anchor="middle" class="text">DataStore Preferences</text>
  <text x="140" y="480" text-anchor="middle" class="subtitle">(Persistent data)</text>
  
  <!-- Remote Data -->
  <rect x="310" y="400" width="180" height="100" rx="5" class="box" />
  <text x="400" y="420" text-anchor="middle" class="title">Remote Data</text>
  <text x="400" y="440" text-anchor="middle" class="text">WeatherApiService</text>
  <text x="400" y="460" text-anchor="middle" class="text">Retrofit</text>
  <text x="400" y="480" text-anchor="middle" class="subtitle">(Weather API)</text>
  
  <!-- Background Processing -->
  <rect x="570" y="240" width="180" height="100" rx="5" class="box" />
  <text x="660" y="260" text-anchor="middle" class="title">Background Processing</text>
  <text x="660" y="280" text-anchor="middle" class="text">WeatherUpdateWorker</text>
  <text x="660" y="300" text-anchor="middle" class="text">WeatherWidgetManager</text>
  <text x="660" y="320" text-anchor="middle" class="subtitle">(Periodic updates)</text>
  
  <!-- Dependency Injection -->
  <rect x="570" y="80" width="180" height="100" rx="5" class="box" />
  <text x="660" y="100" text-anchor="middle" class="title">Dependency Injection</text>
  <text x="660" y="120" text-anchor="middle" class="text">Koin Modules</text>
  <text x="660" y="140" text-anchor="middle" class="text">KoinWorkerFactory</text>
  <text x="660" y="160" text-anchor="middle" class="subtitle">(Provides dependencies)</text>
  
  <!-- Arrows -->
  <!-- UI to ViewModel -->
  <line x1="230" y1="130" x2="310" y2="130" class="arrow" />
  
  <!-- ViewModel to Repository -->
  <line x1="400" y1="180" x2="400" y2="240" class="arrow" />
  
  <!-- Repository to Local Storage -->
  <line x1="310" y1="290" x2="230" y2="400" class="arrow" />
  
  <!-- Repository to Remote Data -->
  <line x1="400" y1="340" x2="400" y2="400" class="arrow" />
  
  <!-- Background Processing to Repository -->
  <line x1="570" y1="290" x2="490" y2="290" class="arrow" />
  
  <!-- Background Processing to UI -->
  <path d="M660 240 L660 50 L140 50 L140 80" class="arrow" fill="none" />
  
  <!-- DI to all components -->
  <path d="M660 180 L660 200 L400 200 L400 220" class="arrow" fill="none" />
  <path d="M660 180 L660 200 L570 200 L570 240" class="arrow" fill="none" />
  
  <!-- Data Flow Labels -->
  <text x="270" y="120" text-anchor="middle" class="subtitle">User actions</text>
  <text x="410" y="220" text-anchor="middle" class="subtitle">Data requests</text>
  <text x="270" y="350" text-anchor="middle" class="subtitle">Read/Write</text>
  <text x="410" y="380" text-anchor="middle" class="subtitle">API calls</text>
  <text x="530" y="280" text-anchor="middle" class="subtitle">Scheduled updates</text>
  <text x="400" y="40" text-anchor="middle" class="subtitle">Widget updates</text>
  <text x="570" y="190" text-anchor="middle" class="subtitle">Provides instances</text>
  
  <!-- Legend -->
  <rect x="50" y="530" width="15" height="15" class="box" />
  <text x="75" y="543" class="text">Component</text>
  
  <line x1="150" y1="538" x2="180" y2="538" class="arrow" />
  <text x="200" y="543" class="text">Data Flow</text>
  
  <text x="400" y="570" text-anchor="middle" class="subtitle">Flow-based reactive architecture with Kotlin Coroutines</text>
</svg>