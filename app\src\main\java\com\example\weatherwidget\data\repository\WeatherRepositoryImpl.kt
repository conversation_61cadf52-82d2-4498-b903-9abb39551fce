package com.example.weatherwidget.data.repository

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.longPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import com.example.weatherwidget.data.WeatherDao
import com.example.weatherwidget.data.api.WeatherApiService
import com.example.weatherwidget.data.model.WeatherInfo
import com.example.weatherwidget.data.model.toWeatherInfo
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.map

class WeatherRepositoryImpl(
    private val weatherDao: WeatherDao,
    private val weatherApiService: WeatherApiService,
    private val dataStore: DataStore<Preferences>
) : WeatherRepository {

    companion object {
        // DataStore keys
        private val SELECTED_WIDGET_CITY_ID = longPreferencesKey("selected_widget_city_id")
        private val WIDGET_UPDATE_FREQUENCY = intPreferencesKey("widget_update_frequency_hours")
        private val API_KEY = stringPreferencesKey("api_key")
        
        // Default values
        private const val DEFAULT_UPDATE_FREQUENCY_HOURS = 2
        private const val DEFAULT_API_KEY = "YOUR_API_KEY" // Replace with your actual API key or use BuildConfig
    }

    // Weather data operations
    override fun getAllWeatherInfo(): Flow<List<WeatherInfo>> {
        return weatherDao.getAllWeatherInfo()
    }

    override fun getFavoriteWeatherInfo(): Flow<List<WeatherInfo>> {
        return weatherDao.getFavoriteWeatherInfo()
    }

    override fun getWeatherInfoById(cityId: Long): Flow<WeatherInfo?> {
        return weatherDao.getWeatherInfoByIdAsFlow(cityId)
    }

    override suspend fun refreshWeatherById(cityId: Long): Result<WeatherInfo> {
        return try {
            val apiKey = getApiKey()
            val response = weatherApiService.getWeatherByCityId(cityId, apiKey = apiKey)
            val weatherInfo = response.toWeatherInfo()
            
            // Preserve favorite status
            val existingInfo = weatherDao.getWeatherInfoById(cityId)
            val updatedInfo = weatherInfo.copy(isFavorite = existingInfo?.isFavorite ?: false)
            
            weatherDao.insertWeatherInfo(updatedInfo)
            Result.success(updatedInfo)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun searchWeatherByCity(cityName: String): Result<WeatherInfo> {
        return try {
            val apiKey = getApiKey()
            val response = weatherApiService.getWeatherByCity(cityName, apiKey = apiKey)
            val weatherInfo = response.toWeatherInfo()
            weatherDao.insertWeatherInfo(weatherInfo)
            Result.success(weatherInfo)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun updateFavoriteStatus(cityId: Long, isFavorite: Boolean) {
        weatherDao.updateFavoriteStatus(cityId, isFavorite)
    }

    override suspend fun deleteWeatherInfo(cityId: Long) {
        weatherDao.deleteWeatherInfo(cityId)
    }

    // Widget preferences
    override suspend fun getSelectedWidgetCityId(): Long? {
        return dataStore.data.map { preferences ->
            preferences[SELECTED_WIDGET_CITY_ID]
        }.firstOrNull()
    }

    override suspend fun setSelectedWidgetCityId(cityId: Long) {
        dataStore.edit { preferences ->
            preferences[SELECTED_WIDGET_CITY_ID] = cityId
        }
    }

    override suspend fun getWidgetUpdateFrequencyHours(): Int {
        return dataStore.data.map { preferences ->
            preferences[WIDGET_UPDATE_FREQUENCY] ?: DEFAULT_UPDATE_FREQUENCY_HOURS
        }.firstOrNull() ?: DEFAULT_UPDATE_FREQUENCY_HOURS
    }

    override suspend fun setWidgetUpdateFrequencyHours(hours: Int) {
        dataStore.edit { preferences ->
            preferences[WIDGET_UPDATE_FREQUENCY] = hours
        }
    }

    // API key management
    override suspend fun getApiKey(): String {
        return dataStore.data.map { preferences ->
            preferences[API_KEY] ?: DEFAULT_API_KEY
        }.firstOrNull() ?: DEFAULT_API_KEY
    }

    override suspend fun setApiKey(apiKey: String) {
        dataStore.edit { preferences ->
            preferences[API_KEY] = apiKey
        }
    }
}