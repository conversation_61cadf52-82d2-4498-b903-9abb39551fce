{"logs": [{"outputFile": "com.example.weatherwidget.app-mergeDebugResources-80:/values-watch-v20/values-watch-v20.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\a6d2b7e5ecb18270abb575bfe8bf3041\\transformed\\appcompat-1.7.0\\res\\values-watch-v20\\values-watch-v20.xml", "from": {"startLines": "2,5,8", "startColumns": "4,4,4", "startOffsets": "55,214,385", "endLines": "4,7,10", "endColumns": "12,12,12", "endOffsets": "209,380,553"}, "to": {"startLines": "10,13,16", "startColumns": "4,4,4", "startOffsets": "634,793,964", "endLines": "12,15,18", "endColumns": "12,12,12", "endOffsets": "788,959,1132"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\43ef54042a4a5617b6e570bf9425eae0\\transformed\\core-splashscreen-1.0.1\\res\\values-watch-v20\\values-watch-v20.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,129,205,280,357,428,496,566", "endColumns": "73,75,74,76,70,67,69,67", "endOffsets": "124,200,275,352,423,491,561,629"}}]}]}