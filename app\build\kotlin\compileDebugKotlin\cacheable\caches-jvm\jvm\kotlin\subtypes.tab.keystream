)com.example.weatherwidget.data.WeatherDao.com.example.weatherwidget.data.WeatherDatabaseandroid.app.Application$androidx.work.Configuration.Providerandroidx.room.RoomDatabase2kotlinx.serialization.internal.GeneratedSerializerkotlin.Enum;com.example.weatherwidget.data.repository.WeatherRepository#androidx.activity.ComponentActivityandroidx.lifecycle.ViewModel)androidx.glance.appwidget.GlanceAppWidget%org.koin.core.component.KoinComponent/androidx.glance.appwidget.action.ActionCallback1androidx.glance.appwidget.GlanceAppWidgetReceiverandroidx.work.WorkerFactoryandroidx.work.CoroutineWorker                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       