<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">Weather Widget</string>
    
    <!-- Widget related strings -->
    <string name="widget_name">Weather Widget</string>
    <string name="widget_description">Displays current weather for a selected city</string>
    <string name="widget_loading">Loading weather…</string>
    <string name="widget_error">Error loading weather</string>
    <string name="widget_last_updated">Last updated: %1$s</string>
    <string name="widget_tap_to_refresh">Tap to refresh</string>
    
    <!-- Configuration activity strings -->
    <string name="config_title">Select a city for your widget</string>
    <string name="config_no_cities">No saved cities found. Please add cities in the main app first.</string>
    <string name="config_save">Save</string>
    <string name="config_cancel">Cancel</string>
    <string name="config_success">Widget configured successfully</string>
    <string name="config_update_frequency">Update frequency</string>
    <string name="config_update_1_hour">Every hour</string>
    <string name="config_update_2_hours">Every 2 hours</string>
    <string name="config_update_4_hours">Every 4 hours</string>
    
    <!-- Main activity strings -->
    <string name="search_hint">Search for a city…</string>
    <string name="search_button">Search</string>
    <string name="refresh_all">Refresh All</string>
    <string name="no_weather_data">No weather data available</string>
    <string name="delete_confirmation">Are you sure you want to delete this city?</string>
    <string name="delete_yes">Yes</string>
    <string name="delete_no">No</string>
    
    <!-- Weather information strings -->
    <string name="temperature_format">%1$.1f°C</string>
    <string name="humidity_format">Humidity: %1$d%%</string>
    <string name="wind_format">Wind: %1$.1f m/s</string>
    
    <!-- Error messages -->
    <string name="error_loading_weather">Error loading weather data</string>
    <string name="error_city_not_found">City not found</string>
    <string name="error_network">Network error. Please check your connection</string>
    <string name="error_api_key">Invalid API key</string>
</resources>