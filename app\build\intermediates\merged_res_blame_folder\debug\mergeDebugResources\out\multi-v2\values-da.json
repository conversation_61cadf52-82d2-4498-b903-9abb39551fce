{"logs": [{"outputFile": "com.example.weatherwidget.app-mergeDebugResources-81:/values-da/values-da.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\30287725489871af330b7aa82a2b9f1f\\transformed\\core-1.16.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "30,31,32,33,34,35,36,133", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2832,2928,3030,3127,3225,3332,3441,13845", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "2923,3025,3122,3220,3327,3436,3554,13941"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\99eb3aee547f429176c0b3577d528a52\\transformed\\play-services-base-18.5.0\\res\\values-da\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,574,679,818,939,1055,1156,1308,1410,1568,1691,1832,2006,2068,2126", "endColumns": "101,155,122,104,138,120,115,100,151,101,157,122,140,173,61,57,73", "endOffsets": "294,450,573,678,817,938,1054,1155,1307,1409,1567,1690,1831,2005,2067,2125,2199"}, "to": {"startLines": "39,40,41,42,43,44,45,46,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3731,3837,3997,4124,4233,4376,4501,4621,4853,5009,5115,5277,5404,5549,5727,5793,5855", "endColumns": "105,159,126,108,142,124,119,104,155,105,161,126,144,177,65,61,77", "endOffsets": "3832,3992,4119,4228,4371,4496,4616,4721,5004,5110,5272,5399,5544,5722,5788,5850,5928"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\a6d2b7e5ecb18270abb575bfe8bf3041\\transformed\\appcompat-1.7.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,867,958,1051,1144,1238,1332,1425,1520,1618,1709,1800,1879,1987,2094,2190,2303,2406,2507,2660,2757", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "200,294,410,495,595,708,786,862,953,1046,1139,1233,1327,1420,1515,1613,1704,1795,1874,1982,2089,2185,2298,2401,2502,2655,2752,2832"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,867,958,1051,1144,1238,1332,1425,1520,1618,1709,1800,1879,1987,2094,2190,2303,2406,2507,2660,13387", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "200,294,410,495,595,708,786,862,953,1046,1139,1233,1327,1420,1515,1613,1704,1795,1874,1982,2089,2185,2298,2401,2502,2655,2752,13462"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\f958aeb604e7c143e9bc08e83e2ba828\\transformed\\glance-appwidget-1.1.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,260,371", "endColumns": "204,110,117", "endOffsets": "255,366,484"}, "to": {"startLines": "60,61,62", "startColumns": "4,4,4", "startOffsets": "6209,6414,6525", "endColumns": "204,110,117", "endOffsets": "6409,6520,6638"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\26a7be6aa46fc6df32cf278684c813b2\\transformed\\material3-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,284,391,505,605,700,812,956,1078,1227,1311,1411,1500,1594,1708,1826,1931,2056,2176,2312,2485,2615,2732,2854,2973,3063,3161,3280,3416,3514,3632,3734,3860,3993,4098,4196,4276,4369,4462,4576,4660,4745,4846,4926,5010,5111,5210,5305,5405,5492,5597,5699,5804,5921,6001,6103", "endColumns": "115,112,106,113,99,94,111,143,121,148,83,99,88,93,113,117,104,124,119,135,172,129,116,121,118,89,97,118,135,97,117,101,125,132,104,97,79,92,92,113,83,84,100,79,83,100,98,94,99,86,104,101,104,116,79,101,98", "endOffsets": "166,279,386,500,600,695,807,951,1073,1222,1306,1406,1495,1589,1703,1821,1926,2051,2171,2307,2480,2610,2727,2849,2968,3058,3156,3275,3411,3509,3627,3729,3855,3988,4093,4191,4271,4364,4457,4571,4655,4740,4841,4921,5005,5106,5205,5300,5400,5487,5592,5694,5799,5916,5996,6098,6197"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6809,6925,7038,7145,7259,7359,7454,7566,7710,7832,7981,8065,8165,8254,8348,8462,8580,8685,8810,8930,9066,9239,9369,9486,9608,9727,9817,9915,10034,10170,10268,10386,10488,10614,10747,10852,10950,11030,11123,11216,11330,11414,11499,11600,11680,11764,11865,11964,12059,12159,12246,12351,12453,12558,12675,12755,12857", "endColumns": "115,112,106,113,99,94,111,143,121,148,83,99,88,93,113,117,104,124,119,135,172,129,116,121,118,89,97,118,135,97,117,101,125,132,104,97,79,92,92,113,83,84,100,79,83,100,98,94,99,86,104,101,104,116,79,101,98", "endOffsets": "6920,7033,7140,7254,7354,7449,7561,7705,7827,7976,8060,8160,8249,8343,8457,8575,8680,8805,8925,9061,9234,9364,9481,9603,9722,9812,9910,10029,10165,10263,10381,10483,10609,10742,10847,10945,11025,11118,11211,11325,11409,11494,11595,11675,11759,11860,11959,12054,12154,12241,12346,12448,12553,12670,12750,12852,12951"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\821e8c1a7433219b3163b17b67af0011\\transformed\\material-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "94", "endOffsets": "145"}, "to": {"startLines": "122", "startColumns": "4", "startOffsets": "12956", "endColumns": "94", "endOffsets": "13046"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\06d808385d814fc5793833c3be2ca602\\transformed\\foundation-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,220", "endColumns": "74,89,86", "endOffsets": "125,215,302"}, "to": {"startLines": "29,137,138", "startColumns": "4,4,4", "startOffsets": "2757,14211,14301", "endColumns": "74,89,86", "endOffsets": "2827,14296,14383"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\dcea319fdd3ad3c36ece65fd708a73da\\transformed\\ui-release\\res\\values-da\\values-da.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "181,273,353,448,547,629,706,795,884,966,1047,1131,1201,1292,1366,1438,1509,1587,1654", "endColumns": "91,79,94,98,81,76,88,88,81,80,83,69,90,73,71,70,77,66,119", "endOffsets": "268,348,443,542,624,701,790,879,961,1042,1126,1196,1287,1361,1433,1504,1582,1649,1769"}, "to": {"startLines": "37,38,57,58,59,63,64,123,124,125,126,128,129,130,131,132,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3559,3651,5933,6028,6127,6643,6720,13051,13140,13222,13303,13467,13537,13628,13702,13774,13946,14024,14091", "endColumns": "91,79,94,98,81,76,88,88,81,80,83,69,90,73,71,70,77,66,119", "endOffsets": "3646,3726,6023,6122,6204,6715,6804,13135,13217,13298,13382,13532,13623,13697,13769,13840,14019,14086,14206"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\6bdbe06209508b26c4e38693a2ab85d8\\transformed\\play-services-basement-18.4.0\\res\\values-da\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "122", "endOffsets": "317"}, "to": {"startLines": "47", "startColumns": "4", "startOffsets": "4726", "endColumns": "126", "endOffsets": "4848"}}]}]}