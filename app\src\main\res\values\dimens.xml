<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Default screen margins, per the Android Design guidelines. -->
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    
    <!-- Text sizes -->
    <dimen name="text_size_small">12sp</dimen>
    <dimen name="text_size_medium">14sp</dimen>
    <dimen name="text_size_large">16sp</dimen>
    <dimen name="text_size_xlarge">20sp</dimen>
    <dimen name="text_size_xxlarge">24sp</dimen>
    
    <!-- Widget specific dimensions -->
    <dimen name="widget_margin">8dp</dimen>
    <dimen name="widget_corner_radius">16dp</dimen>
    <dimen name="widget_padding">12dp</dimen>
    <dimen name="widget_icon_size">40dp</dimen>
    <dimen name="widget_city_text_size">18sp</dimen>
    <dimen name="widget_temp_text_size">24sp</dimen>
    <dimen name="widget_desc_text_size">14sp</dimen>
    <dimen name="widget_details_text_size">12sp</dimen>
    
    <!-- List item dimensions -->
    <dimen name="list_item_padding">16dp</dimen>
    <dimen name="list_item_height">72dp</dimen>
    <dimen name="list_item_icon_size">48dp</dimen>
    <dimen name="list_item_spacing">8dp</dimen>
    
    <!-- Button dimensions -->
    <dimen name="button_height">48dp</dimen>
    <dimen name="button_min_width">88dp</dimen>
    <dimen name="button_padding">16dp</dimen>
    <dimen name="button_corner_radius">4dp</dimen>
    
    <!-- Card dimensions -->
    <dimen name="card_corner_radius">8dp</dimen>
    <dimen name="card_elevation">4dp</dimen>
    <dimen name="card_margin">8dp</dimen>
</resources>