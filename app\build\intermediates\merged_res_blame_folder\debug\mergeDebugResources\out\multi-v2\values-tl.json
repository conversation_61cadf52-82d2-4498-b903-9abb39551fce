{"logs": [{"outputFile": "com.example.weatherwidget.app-mergeDebugResources-81:/values-tl/values-tl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\30287725489871af330b7aa82a2b9f1f\\transformed\\core-1.16.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,355,452,559,667,789", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "147,249,350,447,554,662,784,885"}, "to": {"startLines": "30,31,32,33,34,35,36,133", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2920,3017,3119,3220,3317,3424,3532,14461", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "3012,3114,3215,3312,3419,3527,3649,14557"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\99eb3aee547f429176c0b3577d528a52\\transformed\\play-services-base-18.5.0\\res\\values-tl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,468,602,707,861,993,1111,1220,1395,1498,1672,1806,1964,2139,2203,2265", "endColumns": "102,171,133,104,153,131,117,108,174,102,173,133,157,174,63,61,76", "endOffsets": "295,467,601,706,860,992,1110,1219,1394,1497,1671,1805,1963,2138,2202,2264,2341"}, "to": {"startLines": "39,40,41,42,43,44,45,46,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3839,3946,4122,4260,4369,4527,4663,4785,5043,5222,5329,5507,5645,5807,5986,6054,6120", "endColumns": "106,175,137,108,157,135,121,112,178,106,177,137,161,178,67,65,80", "endOffsets": "3941,4117,4255,4364,4522,4658,4780,4893,5217,5324,5502,5640,5802,5981,6049,6115,6196"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\26a7be6aa46fc6df32cf278684c813b2\\transformed\\material3-release\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,301,419,540,639,739,856,1003,1130,1280,1365,1464,1559,1657,1778,1916,2020,2167,2315,2462,2632,2770,2893,3018,3143,3239,3338,3463,3598,3705,3809,3922,4067,4216,4332,4438,4514,4614,4711,4821,4910,4999,5106,5186,5270,5370,5474,5574,5680,5768,5880,5985,6095,6214,6294,6401", "endColumns": "123,121,117,120,98,99,116,146,126,149,84,98,94,97,120,137,103,146,147,146,169,137,122,124,124,95,98,124,134,106,103,112,144,148,115,105,75,99,96,109,88,88,106,79,83,99,103,99,105,87,111,104,109,118,79,106,94", "endOffsets": "174,296,414,535,634,734,851,998,1125,1275,1360,1459,1554,1652,1773,1911,2015,2162,2310,2457,2627,2765,2888,3013,3138,3234,3333,3458,3593,3700,3804,3917,4062,4211,4327,4433,4509,4609,4706,4816,4905,4994,5101,5181,5265,5365,5469,5569,5675,5763,5875,5980,6090,6209,6289,6396,6491"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7106,7230,7352,7470,7591,7690,7790,7907,8054,8181,8331,8416,8515,8610,8708,8829,8967,9071,9218,9366,9513,9683,9821,9944,10069,10194,10290,10389,10514,10649,10756,10860,10973,11118,11267,11383,11489,11565,11665,11762,11872,11961,12050,12157,12237,12321,12421,12525,12625,12731,12819,12931,13036,13146,13265,13345,13452", "endColumns": "123,121,117,120,98,99,116,146,126,149,84,98,94,97,120,137,103,146,147,146,169,137,122,124,124,95,98,124,134,106,103,112,144,148,115,105,75,99,96,109,88,88,106,79,83,99,103,99,105,87,111,104,109,118,79,106,94", "endOffsets": "7225,7347,7465,7586,7685,7785,7902,8049,8176,8326,8411,8510,8605,8703,8824,8962,9066,9213,9361,9508,9678,9816,9939,10064,10189,10285,10384,10509,10644,10751,10855,10968,11113,11262,11378,11484,11560,11660,11757,11867,11956,12045,12152,12232,12316,12416,12520,12620,12726,12814,12926,13031,13141,13260,13340,13447,13542"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\821e8c1a7433219b3163b17b67af0011\\transformed\\material-release\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "122", "startColumns": "4", "startOffsets": "13547", "endColumns": "88", "endOffsets": "13631"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\06d808385d814fc5793833c3be2ca602\\transformed\\foundation-release\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,216", "endColumns": "74,85,91", "endOffsets": "125,211,303"}, "to": {"startLines": "29,137,138", "startColumns": "4,4,4", "startOffsets": "2845,14833,14919", "endColumns": "74,85,91", "endOffsets": "2915,14914,15006"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\dcea319fdd3ad3c36ece65fd708a73da\\transformed\\ui-release\\res\\values-tl\\values-tl.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "181,280,366,463,565,655,737,829,921,1005,1092,1178,1249,1334,1417,1494,1569,1647,1713", "endColumns": "98,85,96,101,89,81,91,91,83,86,85,70,84,82,76,74,77,65,126", "endOffsets": "275,361,458,560,650,732,824,916,1000,1087,1173,1244,1329,1412,1489,1564,1642,1708,1835"}, "to": {"startLines": "37,38,57,58,59,63,64,123,124,125,126,128,129,130,131,132,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3654,3753,6201,6298,6400,6932,7014,13636,13728,13812,13899,14070,14141,14226,14309,14386,14562,14640,14706", "endColumns": "98,85,96,101,89,81,91,91,83,86,85,70,84,82,76,74,77,65,126", "endOffsets": "3748,3834,6293,6395,6485,7009,7101,13723,13807,13894,13980,14136,14221,14304,14381,14456,14635,14701,14828"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\6bdbe06209508b26c4e38693a2ab85d8\\transformed\\play-services-basement-18.4.0\\res\\values-tl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "47", "startColumns": "4", "startOffsets": "4898", "endColumns": "144", "endOffsets": "5038"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\a6d2b7e5ecb18270abb575bfe8bf3041\\transformed\\appcompat-1.7.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,324,437,525,631,746,826,903,994,1087,1182,1276,1376,1469,1564,1658,1749,1840,1924,2033,2143,2244,2354,2472,2580,2743,2845", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "211,319,432,520,626,741,821,898,989,1082,1177,1271,1371,1464,1559,1653,1744,1835,1919,2028,2138,2239,2349,2467,2575,2738,2840,2925"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,324,437,525,631,746,826,903,994,1087,1182,1276,1376,1469,1564,1658,1749,1840,1924,2033,2143,2244,2354,2472,2580,2743,13985", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "211,319,432,520,626,741,821,898,989,1082,1177,1271,1371,1464,1559,1653,1744,1835,1919,2028,2138,2239,2349,2467,2575,2738,2840,14065"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\f958aeb604e7c143e9bc08e83e2ba828\\transformed\\glance-appwidget-1.1.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,259,376", "endColumns": "203,116,120", "endOffsets": "254,371,492"}, "to": {"startLines": "60,61,62", "startColumns": "4,4,4", "startOffsets": "6490,6694,6811", "endColumns": "203,116,120", "endOffsets": "6689,6806,6927"}}]}]}