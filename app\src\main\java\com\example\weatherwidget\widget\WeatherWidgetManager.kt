package com.example.weatherwidget.widget

import android.content.Context
import androidx.glance.appwidget.updateAll
import androidx.work.Constraints
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.NetworkType
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkManager
import com.example.weatherwidget.data.repository.WeatherRepository
import com.example.weatherwidget.worker.WeatherUpdateWorker
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.concurrent.TimeUnit

class WeatherWidgetManager(
    private val context: Context,
    private val weatherRepository: WeatherRepository
) {
    
    companion object {
        private const val WEATHER_UPDATE_WORK = "weather_update_work"
    }
    
    // Reference to the Glance widget
    val weatherWidget = WeatherWidget()
    
    // Coroutine scope for widget operations
    private val widgetScope = CoroutineScope(Dispatchers.IO)
    
    /**
     * Schedule periodic updates for the weather widget
     */
    fun scheduleWidgetUpdates() {
        widgetScope.launch {
            val updateFrequencyHours = weatherRepository.getWidgetUpdateFrequencyHours()
            
            // Create work constraints
            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED)
                .build()
            
            // Create periodic work request
            val updateRequest = PeriodicWorkRequestBuilder<WeatherUpdateWorker>(
                updateFrequencyHours.toLong(), TimeUnit.HOURS
            )
                .setConstraints(constraints)
                .build()
            
            // Enqueue the work request
            WorkManager.getInstance(context).enqueueUniquePeriodicWork(
                WEATHER_UPDATE_WORK,
                ExistingPeriodicWorkPolicy.UPDATE,
                updateRequest
            )
        }
    }
    
    /**
     * Cancel scheduled widget updates
     */
    fun cancelWidgetUpdates() {
        WorkManager.getInstance(context).cancelUniqueWork(WEATHER_UPDATE_WORK)
    }
    
    /**
     * Update all widgets immediately
     */
    fun updateWidgetsNow() {
        widgetScope.launch {
            weatherWidget.updateAll(context)
        }
    }
    
    /**
     * Set the city for the widget and update immediately
     */
    suspend fun setWidgetCity(cityId: Long) {
        weatherRepository.setSelectedWidgetCityId(cityId)
        weatherRepository.refreshWeatherById(cityId)
        updateWidgetsNow()
    }
}