package com.example.weatherwidget.widget

import android.app.Activity
import android.appwidget.AppWidgetManager
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.Card
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Divider
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Scaffold
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TopAppBar
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.FavoriteBorder
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.weatherwidget.data.model.WeatherInfo
import kotlinx.coroutines.launch
import org.koin.androidx.compose.koinViewModel

class WeatherWidgetConfigActivity : ComponentActivity() {

    private var appWidgetId = AppWidgetManager.INVALID_APPWIDGET_ID

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Set the result to CANCELED in case the user backs out
        setResult(Activity.RESULT_CANCELED)

        // Find the widget id from the intent
        appWidgetId = intent?.extras?.getInt(
            AppWidgetManager.EXTRA_APPWIDGET_ID,
            AppWidgetManager.INVALID_APPWIDGET_ID
        ) ?: AppWidgetManager.INVALID_APPWIDGET_ID

        // If the intent doesn't have a widget ID, finish the activity
        if (appWidgetId == AppWidgetManager.INVALID_APPWIDGET_ID) {
            finish()
            return
        }

        setContent {
            MaterialTheme {
                Surface(color = MaterialTheme.colors.background) {
                    WidgetConfigScreen(onCitySelected = { cityId ->
                        // Set the result for the widget manager
                        val resultValue = Intent()
                        resultValue.putExtra(AppWidgetManager.EXTRA_APPWIDGET_ID, appWidgetId)
                        setResult(Activity.RESULT_OK, resultValue)

                        // Finish the activity
                        finish()
                    })
                }
            }
        }
    }
}

@Composable
fun WidgetConfigScreen(
    viewModel: WeatherWidgetViewModel = koinViewModel(),
    onCitySelected: (Long) -> Unit
) {
    val weatherList by viewModel.allWeatherInfo.collectAsState(initial = emptyList())
    val coroutineScope = rememberCoroutineScope()

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Select City for Widget") }
            )
        }
    ) { paddingValues ->
        if (weatherList.isEmpty()) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentAlignment = Alignment.Center
            ) {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    CircularProgressIndicator()
                    Spacer(modifier = Modifier.height(16.dp))
                    Text("Loading cities...")
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "No cities found. Please add cities in the main app first.",
                        style = MaterialTheme.typography.body2
                    )
                }
            }
        } else {
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
            ) {
                items(weatherList) { weather ->
                    CityItem(
                        weatherInfo = weather,
                        onCityClicked = {
                            coroutineScope.launch {
                                viewModel.setWidgetCity(weather.cityId)
                                onCitySelected(weather.cityId)
                            }
                        },
                        onFavoriteToggled = {
                            coroutineScope.launch {
                                viewModel.toggleFavorite(weather.cityId, !weather.isFavorite)
                            }
                        }
                    )
                    Divider()
                }
            }
        }
    }
}

@Composable
fun CityItem(
    weatherInfo: WeatherInfo,
    onCityClicked: () -> Unit,
    onFavoriteToggled: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp)
            .clickable { onCityClicked() },
        elevation = 2.dp
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "${weatherInfo.cityName}, ${weatherInfo.country}",
                    fontWeight = FontWeight.Bold,
                    fontSize = 18.sp
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "${weatherInfo.temperature.toInt()}°C, ${weatherInfo.weatherDescription}",
                    fontSize = 14.sp
                )
            }
            
            Icon(
                imageVector = if (weatherInfo.isFavorite) Icons.Filled.Favorite else Icons.Filled.FavoriteBorder,
                contentDescription = "Favorite",
                modifier = Modifier.clickable { onFavoriteToggled() }
            )
        }
    }
}