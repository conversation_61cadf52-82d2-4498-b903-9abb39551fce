package com.example.weatherwidget.widget

import android.app.Activity
import android.appwidget.AppWidgetManager
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.weatherwidget.data.model.*
import com.example.weatherwidget.domain.usecase.WidgetConfigurationUseCase
import com.example.weatherwidget.widget.*
import kotlinx.coroutines.launch
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject

class WeatherWidgetConfigActivity : ComponentActivity() {

    private var appWidgetId = AppWidgetManager.INVALID_APPWIDGET_ID

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Set the result to CANCELED in case the user backs out
        setResult(Activity.RESULT_CANCELED)

        // Find the widget id from the intent
        appWidgetId = intent?.extras?.getInt(
            AppWidgetManager.EXTRA_APPWIDGET_ID,
            AppWidgetManager.INVALID_APPWIDGET_ID
        ) ?: AppWidgetManager.INVALID_APPWIDGET_ID

        // If the intent doesn't have a widget ID, finish the activity
        if (appWidgetId == AppWidgetManager.INVALID_APPWIDGET_ID) {
            finish()
            return
        }

        setContent {
            MaterialTheme {
                Surface(color = MaterialTheme.colorScheme.background) {
                    WidgetConfigScreen(
                        widgetId = appWidgetId,
                        onConfigurationSaved = {
                            // Set the result for the widget manager
                            val resultValue = Intent()
                            resultValue.putExtra(AppWidgetManager.EXTRA_APPWIDGET_ID, appWidgetId)
                            setResult(Activity.RESULT_OK, resultValue)

                            // Finish the activity
                            finish()
                        },
                        onCancel = {
                            setResult(Activity.RESULT_CANCELED)
                            finish()
                        }
                    )
                }
            }
        }
    }
}

@Composable
fun WidgetConfigScreen(
    widgetId: Int,
    onConfigurationSaved: () -> Unit,
    onCancel: () -> Unit,
    viewModel: WeatherWidgetViewModel = koinViewModel(),
    configUseCase: WidgetConfigurationUseCase = koinInject()
) {
    val weatherList by viewModel.allWeatherInfo.collectAsState(initial = emptyList())
    val coroutineScope = rememberCoroutineScope()

    // Widget configuration state
    var configuration by remember {
        mutableStateOf(DefaultWidgetConfiguration.create(widgetId))
    }

    // Load existing configuration
    LaunchedEffect(widgetId) {
        configUseCase.getWidgetConfiguration(widgetId).collect { config ->
            configuration = config
        }
    }

    // Selected weather for preview
    val selectedWeather = weatherList.find { it.cityId == configuration.locationDisplay.selectedCityId }
    val isSystemDark = isSystemInDarkTheme()

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
    ) {
        // Top App Bar
        TopAppBar(
            title = { Text("Configure Widget") },
            navigationIcon = {
                IconButton(onClick = onCancel) {
                    Icon(Icons.Default.Close, contentDescription = "Cancel")
                }
            },
            actions = {
                TextButton(
                    onClick = {
                        coroutineScope.launch {
                            configUseCase.saveWidgetConfiguration(configuration)
                            onConfigurationSaved()
                        }
                    }
                ) {
                    Text("Apply")
                }
            }
        )

        // Widget Preview (Fixed at top)
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Widget Preview",
                    style = MaterialTheme.typography.titleMedium,
                    modifier = Modifier.padding(bottom = 8.dp)
                )

                WeatherWidgetPreview(
                    weatherInfo = selectedWeather,
                    configuration = configuration,
                    isSystemDark = isSystemDark,
                    modifier = Modifier.padding(8.dp)
                )
            }
        }

        // Configuration Options (Scrollable)
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // City Selection Section
            item {
                ConfigurationSection(
                    title = "Location",
                    icon = Icons.Default.LocationOn
                ) {
                    CitySelectionSection(
                        weatherList = weatherList,
                        selectedCityId = configuration.locationDisplay.selectedCityId,
                        showCityName = configuration.locationDisplay.showCityName,
                        onCitySelected = { cityId ->
                            configuration = configuration.copy(
                                locationDisplay = configuration.locationDisplay.copy(
                                    selectedCityId = cityId
                                )
                            )
                        },
                        onShowCityNameChanged = { show ->
                            configuration = configuration.copy(
                                locationDisplay = configuration.locationDisplay.copy(
                                    showCityName = show
                                )
                            )
                        }
                    )
                }

            // Theme Section
            item {
                ConfigurationSection(
                    title = "Theme & Appearance",
                    icon = Icons.Default.Palette
                ) {
                    ThemeSection(
                        theme = configuration.theme,
                        transparency = configuration.transparency,
                        onThemeChanged = { theme ->
                            configuration = configuration.copy(theme = theme)
                        },
                        onTransparencyChanged = { transparency ->
                            configuration = configuration.copy(transparency = transparency)
                        }
                    )
                }
            }

            // Weather Elements Section
            item {
                ConfigurationSection(
                    title = "Weather Elements",
                    icon = Icons.Default.Cloud
                ) {
                    WeatherElementsSection(
                        elements = configuration.weatherElements,
                        timeDisplay = configuration.timeDisplay,
                        onElementsChanged = { elements ->
                            configuration = configuration.copy(weatherElements = elements)
                        },
                        onTimeDisplayChanged = { timeDisplay ->
                            configuration = configuration.copy(timeDisplay = timeDisplay)
                        }
                    )
                }
            }

            // Visual Customization Section
            item {
                ConfigurationSection(
                    title = "Visual Customization",
                    icon = Icons.Default.Tune
                ) {
                    VisualCustomizationSection(
                        visual = configuration.visualCustomization,
                        onVisualChanged = { visual ->
                            configuration = configuration.copy(visualCustomization = visual)
                        }
                    )
                }
            }

            // Update Settings Section
            item {
                ConfigurationSection(
                    title = "Update Settings",
                    icon = Icons.Default.Sync
                ) {
                    UpdateSettingsSection(
                        updateSettings = configuration.updateSettings,
                        onUpdateSettingsChanged = { settings ->
                            configuration = configuration.copy(updateSettings = settings)
                        }
                    )
                }
            }

            // Reset Button
            item {
                Spacer(modifier = Modifier.height(16.dp))
                OutlinedButton(
                    onClick = {
                        configuration = DefaultWidgetConfiguration.create(widgetId)
                    },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(Icons.Default.RestartAlt, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Reset to Defaults")
                }
                Spacer(modifier = Modifier.height(16.dp))
            }
        }
    }
}

// Configuration Section Components

@Composable
fun ConfigurationSection(
    title: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    content: @Composable () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(bottom = 12.dp)
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
            }
            content()
        }
    }
}

@Composable
fun CitySelectionSection(
    weatherList: List<WeatherInfo>,
    selectedCityId: Long?,
    showCityName: Boolean,
    onCitySelected: (Long) -> Unit,
    onShowCityNameChanged: (Boolean) -> Unit
) {
    Column {
        // Show city name toggle
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text("Show city name")
            Switch(
                checked = showCityName,
                onCheckedChange = onShowCityNameChanged
            )
        }

        Spacer(modifier = Modifier.height(12.dp))

        // City selection
        Text(
            text = "Select City",
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        if (weatherList.isEmpty()) {
            Text(
                text = "No cities available. Add cities in the main app first.",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        } else {
            LazyColumn(
                modifier = Modifier.heightIn(max = 200.dp)
            ) {
                items(weatherList) { weather ->
                    CitySelectionItem(
                        weatherInfo = weather,
                        isSelected = weather.cityId == selectedCityId,
                        onSelected = { onCitySelected(weather.cityId) }
                    )
                }
            }
        }
    }
}

@Composable
fun CitySelectionItem(
    weatherInfo: WeatherInfo,
    isSelected: Boolean,
    onSelected: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 2.dp)
            .clickable { onSelected() },
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected)
                MaterialTheme.colorScheme.primaryContainer
            else
                MaterialTheme.colorScheme.surface
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "${weatherInfo.cityName}, ${weatherInfo.country}",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = "${weatherInfo.temperature.toInt()}°C, ${weatherInfo.weatherDescription}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            if (isSelected) {
                Icon(
                    imageVector = Icons.Default.Check,
                    contentDescription = "Selected",
                    tint = MaterialTheme.colorScheme.primary
                )
            }
        }
    }
}