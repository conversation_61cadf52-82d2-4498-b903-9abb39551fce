{"logs": [{"outputFile": "com.example.weatherwidget.app-mergeDebugResources-81:/values-et/values-et.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\f958aeb604e7c143e9bc08e83e2ba828\\transformed\\glance-appwidget-1.1.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,264,369", "endColumns": "208,104,121", "endOffsets": "259,364,486"}, "to": {"startLines": "60,61,62", "startColumns": "4,4,4", "startOffsets": "6300,6509,6614", "endColumns": "208,104,121", "endOffsets": "6504,6609,6731"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\26a7be6aa46fc6df32cf278684c813b2\\transformed\\material3-release\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,297,411,530,629,730,848,981,1101,1249,1336,1437,1531,1630,1746,1873,1979,2114,2247,2378,2553,2679,2798,2919,3041,3136,3233,3353,3487,3592,3695,3800,3931,4066,4174,4277,4354,4450,4546,4650,4737,4822,4928,5008,5094,5195,5299,5393,5497,5584,5693,5794,5901,6018,6098,6202", "endColumns": "119,121,113,118,98,100,117,132,119,147,86,100,93,98,115,126,105,134,132,130,174,125,118,120,121,94,96,119,133,104,102,104,130,134,107,102,76,95,95,103,86,84,105,79,85,100,103,93,103,86,108,100,106,116,79,103,98", "endOffsets": "170,292,406,525,624,725,843,976,1096,1244,1331,1432,1526,1625,1741,1868,1974,2109,2242,2373,2548,2674,2793,2914,3036,3131,3228,3348,3482,3587,3690,3795,3926,4061,4169,4272,4349,4445,4541,4645,4732,4817,4923,5003,5089,5190,5294,5388,5492,5579,5688,5789,5896,6013,6093,6197,6296"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6905,7025,7147,7261,7380,7479,7580,7698,7831,7951,8099,8186,8287,8381,8480,8596,8723,8829,8964,9097,9228,9403,9529,9648,9769,9891,9986,10083,10203,10337,10442,10545,10650,10781,10916,11024,11127,11204,11300,11396,11500,11587,11672,11778,11858,11944,12045,12149,12243,12347,12434,12543,12644,12751,12868,12948,13052", "endColumns": "119,121,113,118,98,100,117,132,119,147,86,100,93,98,115,126,105,134,132,130,174,125,118,120,121,94,96,119,133,104,102,104,130,134,107,102,76,95,95,103,86,84,105,79,85,100,103,93,103,86,108,100,106,116,79,103,98", "endOffsets": "7020,7142,7256,7375,7474,7575,7693,7826,7946,8094,8181,8282,8376,8475,8591,8718,8824,8959,9092,9223,9398,9524,9643,9764,9886,9981,10078,10198,10332,10437,10540,10645,10776,10911,11019,11122,11199,11295,11391,11495,11582,11667,11773,11853,11939,12040,12144,12238,12342,12429,12538,12639,12746,12863,12943,13047,13146"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\821e8c1a7433219b3163b17b67af0011\\transformed\\material-release\\res\\values-et\\values-et.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "89", "endOffsets": "140"}, "to": {"startLines": "122", "startColumns": "4", "startOffsets": "13151", "endColumns": "89", "endOffsets": "13236"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\30287725489871af330b7aa82a2b9f1f\\transformed\\core-1.16.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,453,559,664,784", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "145,247,345,448,554,659,779,880"}, "to": {"startLines": "30,31,32,33,34,35,36,133", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2907,3002,3104,3202,3305,3411,3516,14046", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "2997,3099,3197,3300,3406,3511,3631,14142"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\99eb3aee547f429176c0b3577d528a52\\transformed\\play-services-base-18.5.0\\res\\values-et\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,450,573,677,823,948,1060,1159,1315,1419,1579,1707,1858,1999,2058,2119", "endColumns": "98,157,122,103,145,124,111,98,155,103,159,127,150,140,58,60,83", "endOffsets": "291,449,572,676,822,947,1059,1158,1314,1418,1578,1706,1857,1998,2057,2118,2202"}, "to": {"startLines": "39,40,41,42,43,44,45,46,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3809,3912,4074,4201,4309,4459,4588,4704,4947,5107,5215,5379,5511,5666,5811,5874,5939", "endColumns": "102,161,126,107,149,128,115,102,159,107,163,131,154,144,62,64,87", "endOffsets": "3907,4069,4196,4304,4454,4583,4699,4802,5102,5210,5374,5506,5661,5806,5869,5934,6022"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\dcea319fdd3ad3c36ece65fd708a73da\\transformed\\ui-release\\res\\values-et\\values-et.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "186,279,359,455,550,632,710,801,892,976,1058,1143,1215,1301,1376,1451,1523,1600,1671", "endColumns": "92,79,95,94,81,77,90,90,83,81,84,71,85,74,74,71,76,70,121", "endOffsets": "274,354,450,545,627,705,796,887,971,1053,1138,1210,1296,1371,1446,1518,1595,1666,1788"}, "to": {"startLines": "37,38,57,58,59,63,64,123,124,125,126,128,129,130,131,132,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3636,3729,6027,6123,6218,6736,6814,13241,13332,13416,13498,13666,13738,13824,13899,13974,14147,14224,14295", "endColumns": "92,79,95,94,81,77,90,90,83,81,84,71,85,74,74,71,76,70,121", "endOffsets": "3724,3804,6118,6213,6295,6809,6900,13327,13411,13493,13578,13733,13819,13894,13969,14041,14219,14290,14412"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\a6d2b7e5ecb18270abb575bfe8bf3041\\transformed\\appcompat-1.7.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,310,421,507,609,726,807,884,976,1070,1166,1268,1377,1471,1572,1666,1758,1851,1934,2045,2149,2248,2358,2460,2559,2725,2827", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "206,305,416,502,604,721,802,879,971,1065,1161,1263,1372,1466,1567,1661,1753,1846,1929,2040,2144,2243,2353,2455,2554,2720,2822,2905"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,310,421,507,609,726,807,884,976,1070,1166,1268,1377,1471,1572,1666,1758,1851,1934,2045,2149,2248,2358,2460,2559,2725,13583", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "206,305,416,502,604,721,802,879,971,1065,1161,1263,1372,1466,1567,1661,1753,1846,1929,2040,2144,2243,2353,2455,2554,2720,2822,13661"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\06d808385d814fc5793833c3be2ca602\\transformed\\foundation-release\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,225", "endColumns": "79,89,88", "endOffsets": "130,220,309"}, "to": {"startLines": "29,137,138", "startColumns": "4,4,4", "startOffsets": "2827,14417,14507", "endColumns": "79,89,88", "endOffsets": "2902,14502,14591"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\6bdbe06209508b26c4e38693a2ab85d8\\transformed\\play-services-basement-18.4.0\\res\\values-et\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "47", "startColumns": "4", "startOffsets": "4807", "endColumns": "139", "endOffsets": "4942"}}]}]}