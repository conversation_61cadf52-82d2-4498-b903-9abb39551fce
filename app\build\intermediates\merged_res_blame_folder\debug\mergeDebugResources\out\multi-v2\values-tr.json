{"logs": [{"outputFile": "com.example.weatherwidget.app-mergeDebugResources-81:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\06d808385d814fc5793833c3be2ca602\\transformed\\foundation-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,139,223", "endColumns": "83,83,86", "endOffsets": "134,218,305"}, "to": {"startLines": "29,137,138", "startColumns": "4,4,4", "startOffsets": "2797,14339,14423", "endColumns": "83,83,86", "endOffsets": "2876,14418,14505"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\f958aeb604e7c143e9bc08e83e2ba828\\transformed\\glance-appwidget-1.1.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,277,385", "endColumns": "221,107,115", "endOffsets": "272,380,496"}, "to": {"startLines": "60,61,62", "startColumns": "4,4,4", "startOffsets": "6301,6523,6631", "endColumns": "221,107,115", "endOffsets": "6518,6626,6742"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\30287725489871af330b7aa82a2b9f1f\\transformed\\core-1.16.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "30,31,32,33,34,35,36,133", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2881,2978,3080,3178,3275,3377,3483,13974", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "2973,3075,3173,3270,3372,3478,3589,14070"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\6bdbe06209508b26c4e38693a2ab85d8\\transformed\\play-services-basement-18.4.0\\res\\values-tr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "47", "startColumns": "4", "startOffsets": "4787", "endColumns": "146", "endOffsets": "4929"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\26a7be6aa46fc6df32cf278684c813b2\\transformed\\material3-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,283,401,516,612,708,821,954,1076,1216,1301,1399,1488,1585,1700,1821,1924,2061,2197,2319,2490,2608,2724,2842,2957,3047,3145,3269,3398,3499,3601,3707,3843,3983,4095,4197,4273,4370,4468,4578,4664,4749,4866,4946,5030,5130,5230,5326,5421,5509,5615,5715,5814,5935,6015,6122", "endColumns": "114,112,117,114,95,95,112,132,121,139,84,97,88,96,114,120,102,136,135,121,170,117,115,117,114,89,97,123,128,100,101,105,135,139,111,101,75,96,97,109,85,84,116,79,83,99,99,95,94,87,105,99,98,120,79,106,92", "endOffsets": "165,278,396,511,607,703,816,949,1071,1211,1296,1394,1483,1580,1695,1816,1919,2056,2192,2314,2485,2603,2719,2837,2952,3042,3140,3264,3393,3494,3596,3702,3838,3978,4090,4192,4268,4365,4463,4573,4659,4744,4861,4941,5025,5125,5225,5321,5416,5504,5610,5710,5809,5930,6010,6117,6210"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6930,7045,7158,7276,7391,7487,7583,7696,7829,7951,8091,8176,8274,8363,8460,8575,8696,8799,8936,9072,9194,9365,9483,9599,9717,9832,9922,10020,10144,10273,10374,10476,10582,10718,10858,10970,11072,11148,11245,11343,11453,11539,11624,11741,11821,11905,12005,12105,12201,12296,12384,12490,12590,12689,12810,12890,12997", "endColumns": "114,112,117,114,95,95,112,132,121,139,84,97,88,96,114,120,102,136,135,121,170,117,115,117,114,89,97,123,128,100,101,105,135,139,111,101,75,96,97,109,85,84,116,79,83,99,99,95,94,87,105,99,98,120,79,106,92", "endOffsets": "7040,7153,7271,7386,7482,7578,7691,7824,7946,8086,8171,8269,8358,8455,8570,8691,8794,8931,9067,9189,9360,9478,9594,9712,9827,9917,10015,10139,10268,10369,10471,10577,10713,10853,10965,11067,11143,11240,11338,11448,11534,11619,11736,11816,11900,12000,12100,12196,12291,12379,12485,12585,12684,12805,12885,12992,13085"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\821e8c1a7433219b3163b17b67af0011\\transformed\\material-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "122", "startColumns": "4", "startOffsets": "13090", "endColumns": "87", "endOffsets": "13173"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\a6d2b7e5ecb18270abb575bfe8bf3041\\transformed\\appcompat-1.7.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,13518", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,13593"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\dcea319fdd3ad3c36ece65fd708a73da\\transformed\\ui-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "190,283,367,462,562,646,729,829,917,1001,1081,1169,1240,1324,1398,1473,1545,1623,1691", "endColumns": "92,83,94,99,83,82,99,87,83,79,87,70,83,73,74,71,77,67,117", "endOffsets": "278,362,457,557,641,724,824,912,996,1076,1164,1235,1319,1393,1468,1540,1618,1686,1804"}, "to": {"startLines": "37,38,57,58,59,63,64,123,124,125,126,128,129,130,131,132,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3594,3687,6022,6117,6217,6747,6830,13178,13266,13350,13430,13598,13669,13753,13827,13902,14075,14153,14221", "endColumns": "92,83,94,99,83,82,99,87,83,79,87,70,83,73,74,71,77,67,117", "endOffsets": "3682,3766,6112,6212,6296,6825,6925,13261,13345,13425,13513,13664,13748,13822,13897,13969,14148,14216,14334"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\99eb3aee547f429176c0b3577d528a52\\transformed\\play-services-base-18.5.0\\res\\values-tr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,450,582,683,826,952,1075,1177,1345,1448,1601,1731,1872,2035,2093,2153", "endColumns": "105,150,131,100,142,125,122,101,167,102,152,129,140,162,57,59,75", "endOffsets": "298,449,581,682,825,951,1074,1176,1344,1447,1600,1730,1871,2034,2092,2152,2228"}, "to": {"startLines": "39,40,41,42,43,44,45,46,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3771,3881,4036,4172,4277,4424,4554,4681,4934,5106,5213,5370,5504,5649,5816,5878,5942", "endColumns": "109,154,135,104,146,129,126,105,171,106,156,133,144,166,61,63,79", "endOffsets": "3876,4031,4167,4272,4419,4549,4676,4782,5101,5208,5365,5499,5644,5811,5873,5937,6017"}}]}]}