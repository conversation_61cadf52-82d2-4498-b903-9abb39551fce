package com.example.weatherwidget

import android.app.Application
import androidx.work.Configuration
import androidx.work.WorkManager
import com.example.weatherwidget.di.appModule
import com.example.weatherwidget.di.databaseModule
import com.example.weatherwidget.di.networkModule
import com.example.weatherwidget.di.repositoryModule
import com.example.weatherwidget.di.testRepositoryModule
import com.example.weatherwidget.di.viewModelModule
import com.example.weatherwidget.di.widgetModule
import com.example.weatherwidget.di.workerModule
import org.koin.android.ext.koin.androidContext
import org.koin.android.ext.koin.androidLogger
import org.koin.androidx.workmanager.koin.workManagerFactory
import org.koin.core.context.startKoin
import org.koin.core.logger.Level


class WeatherApp : Application(), Configuration.Provider {

    override fun onCreate() {
        super.onCreate()

        // Initialize Koin
        startKoin {
            androidLogger(Level.ERROR) // Set to ERROR to avoid Koin issues on Android
            androidContext(this@WeatherApp)
            workManagerFactory()
            modules(
                listOf(
                    appModule,
                    databaseModule,
                    networkModule,
                    repositoryModule,
                    viewModelModule,
                    widgetModule,
                    workerModule,
                    testRepositoryModule
                )
            )
        }

        // Initialize WorkManager

        // WorkManager will be automatically initialized with workManagerConfiguration
        // No need to call WorkManager.initialize() manually
    }

    override val workManagerConfiguration: Configuration
        get() = Configuration.Builder()
            .setMinimumLoggingLevel(android.util.Log.INFO).build()
}