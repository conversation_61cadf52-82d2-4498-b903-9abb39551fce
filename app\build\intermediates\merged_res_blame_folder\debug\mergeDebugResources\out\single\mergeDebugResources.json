[{"merged": "com.example.weatherwidget.app-debug-82:/raw_weather_widget_flow_diagram.svg.flat", "source": "com.example.weatherwidget.app-main-84:/raw/weather_widget_flow_diagram.svg"}, {"merged": "com.example.weatherwidget.app-debug-82:/drawable_ic_partly_cloudy_day.xml.flat", "source": "com.example.weatherwidget.app-main-84:/drawable/ic_partly_cloudy_day.xml"}, {"merged": "com.example.weatherwidget.app-debug-82:/drawable_ic_snow.xml.flat", "source": "com.example.weatherwidget.app-main-84:/drawable/ic_snow.xml"}, {"merged": "com.example.weatherwidget.app-debug-82:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.example.weatherwidget.app-main-84:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.example.weatherwidget.app-debug-82:/drawable_ic_rain_day.xml.flat", "source": "com.example.weatherwidget.app-main-84:/drawable/ic_rain_day.xml"}, {"merged": "com.example.weatherwidget.app-debug-82:/drawable_widget_preview.xml.flat", "source": "com.example.weatherwidget.app-main-84:/drawable/widget_preview.xml"}, {"merged": "com.example.weatherwidget.app-debug-82:/drawable_ic_fog.xml.flat", "source": "com.example.weatherwidget.app-main-84:/drawable/ic_fog.xml"}, {"merged": "com.example.weatherwidget.app-debug-82:/mipmap-anydpi_ic_launcher_round.xml.flat", "source": "com.example.weatherwidget.app-main-84:/mipmap-anydpi/ic_launcher_round.xml"}, {"merged": "com.example.weatherwidget.app-debug-82:/mipmap-anydpi_ic_launcher.xml.flat", "source": "com.example.weatherwidget.app-main-84:/mipmap-anydpi/ic_launcher.xml"}, {"merged": "com.example.weatherwidget.app-debug-82:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.example.weatherwidget.app-main-84:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.example.weatherwidget.app-debug-82:/drawable_ic_thunderstorm.xml.flat", "source": "com.example.weatherwidget.app-main-84:/drawable/ic_thunderstorm.xml"}, {"merged": "com.example.weatherwidget.app-debug-82:/xml_backup_rules.xml.flat", "source": "com.example.weatherwidget.app-main-84:/xml/backup_rules.xml"}, {"merged": "com.example.weatherwidget.app-debug-82:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.example.weatherwidget.app-main-84:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.weatherwidget.app-debug-82:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.example.weatherwidget.app-main-84:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.example.weatherwidget.app-debug-82:/layout_widget_loading.xml.flat", "source": "com.example.weatherwidget.app-main-84:/layout/widget_loading.xml"}, {"merged": "com.example.weatherwidget.app-debug-82:/drawable_ic_rain.xml.flat", "source": "com.example.weatherwidget.app-main-84:/drawable/ic_rain.xml"}, {"merged": "com.example.weatherwidget.app-debug-82:/drawable_ic_clear_day.xml.flat", "source": "com.example.weatherwidget.app-main-84:/drawable/ic_clear_day.xml"}, {"merged": "com.example.weatherwidget.app-debug-82:/drawable_ic_cloudy.xml.flat", "source": "com.example.weatherwidget.app-main-84:/drawable/ic_cloudy.xml"}, {"merged": "com.example.weatherwidget.app-debug-82:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.example.weatherwidget.app-main-84:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.weatherwidget.app-debug-82:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.example.weatherwidget.app-main-84:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.example.weatherwidget.app-debug-82:/xml_data_extraction_rules.xml.flat", "source": "com.example.weatherwidget.app-main-84:/xml/data_extraction_rules.xml"}, {"merged": "com.example.weatherwidget.app-debug-82:/drawable_ic_launcher_background.xml.flat", "source": "com.example.weatherwidget.app-main-84:/drawable/ic_launcher_background.xml"}, {"merged": "com.example.weatherwidget.app-debug-82:/drawable_ic_rain_night.xml.flat", "source": "com.example.weatherwidget.app-main-84:/drawable/ic_rain_night.xml"}, {"merged": "com.example.weatherwidget.app-debug-82:/drawable_widget_background.xml.flat", "source": "com.example.weatherwidget.app-main-84:/drawable/widget_background.xml"}, {"merged": "com.example.weatherwidget.app-debug-82:/drawable_ic_partly_cloudy_night.xml.flat", "source": "com.example.weatherwidget.app-main-84:/drawable/ic_partly_cloudy_night.xml"}, {"merged": "com.example.weatherwidget.app-debug-82:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.example.weatherwidget.app-main-84:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.example.weatherwidget.app-debug-82:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.example.weatherwidget.app-main-84:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.example.weatherwidget.app-debug-82:/drawable_ic_clear_night.xml.flat", "source": "com.example.weatherwidget.app-main-84:/drawable/ic_clear_night.xml"}, {"merged": "com.example.weatherwidget.app-debug-82:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.example.weatherwidget.app-main-84:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.example.weatherwidget.app-debug-82:/drawable_ic_launcher_foreground.xml.flat", "source": "com.example.weatherwidget.app-main-84:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.example.weatherwidget.app-debug-82:/raw_aboutlibraries.json.flat", "source": "com.example.weatherwidget.app-debug-77:/raw/aboutlibraries.json"}, {"merged": "com.example.weatherwidget.app-debug-82:/xml_weather_widget_info.xml.flat", "source": "com.example.weatherwidget.app-main-84:/xml/weather_widget_info.xml"}, {"merged": "com.example.weatherwidget.app-debug-82:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.example.weatherwidget.app-main-84:/mipmap-hdpi/ic_launcher_round.webp"}]