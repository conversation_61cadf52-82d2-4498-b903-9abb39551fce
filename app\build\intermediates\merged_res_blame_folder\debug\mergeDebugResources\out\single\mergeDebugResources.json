[{"merged": "com.example.weatherwidget.app-debug-83:/mipmap-anydpi_ic_launcher.xml.flat", "source": "com.example.weatherwidget.app-main-85:/mipmap-anydpi/ic_launcher.xml"}, {"merged": "com.example.weatherwidget.app-debug-83:/drawable_ic_rain.xml.flat", "source": "com.example.weatherwidget.app-main-85:/drawable/ic_rain.xml"}, {"merged": "com.example.weatherwidget.app-debug-83:/drawable_ic_cloudy.xml.flat", "source": "com.example.weatherwidget.app-main-85:/drawable/ic_cloudy.xml"}, {"merged": "com.example.weatherwidget.app-debug-83:/drawable_ic_partly_cloudy_day.xml.flat", "source": "com.example.weatherwidget.app-main-85:/drawable/ic_partly_cloudy_day.xml"}, {"merged": "com.example.weatherwidget.app-debug-83:/drawable_ic_launcher_foreground.xml.flat", "source": "com.example.weatherwidget.app-main-85:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.example.weatherwidget.app-debug-83:/drawable_ic_launcher_background.xml.flat", "source": "com.example.weatherwidget.app-main-85:/drawable/ic_launcher_background.xml"}, {"merged": "com.example.weatherwidget.app-debug-83:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.example.weatherwidget.app-main-85:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.example.weatherwidget.app-debug-83:/raw_weather_widget_flow_diagram.svg.flat", "source": "com.example.weatherwidget.app-main-85:/raw/weather_widget_flow_diagram.svg"}, {"merged": "com.example.weatherwidget.app-debug-83:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.example.weatherwidget.app-main-85:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.example.weatherwidget.app-debug-83:/drawable_ic_snow.xml.flat", "source": "com.example.weatherwidget.app-main-85:/drawable/ic_snow.xml"}, {"merged": "com.example.weatherwidget.app-debug-83:/drawable_ic_clear_night.xml.flat", "source": "com.example.weatherwidget.app-main-85:/drawable/ic_clear_night.xml"}, {"merged": "com.example.weatherwidget.app-debug-83:/layout_widget_loading.xml.flat", "source": "com.example.weatherwidget.app-main-85:/layout/widget_loading.xml"}, {"merged": "com.example.weatherwidget.app-debug-83:/drawable_ic_clear_day.xml.flat", "source": "com.example.weatherwidget.app-main-85:/drawable/ic_clear_day.xml"}, {"merged": "com.example.weatherwidget.app-debug-83:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.example.weatherwidget.app-main-85:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.example.weatherwidget.app-debug-83:/drawable_ic_partly_cloudy_night.xml.flat", "source": "com.example.weatherwidget.app-main-85:/drawable/ic_partly_cloudy_night.xml"}, {"merged": "com.example.weatherwidget.app-debug-83:/drawable_ic_rain_night.xml.flat", "source": "com.example.weatherwidget.app-main-85:/drawable/ic_rain_night.xml"}, {"merged": "com.example.weatherwidget.app-debug-83:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.example.weatherwidget.app-main-85:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.example.weatherwidget.app-debug-83:/xml_weather_widget_info.xml.flat", "source": "com.example.weatherwidget.app-main-85:/xml/weather_widget_info.xml"}, {"merged": "com.example.weatherwidget.app-debug-83:/drawable_widget_preview.xml.flat", "source": "com.example.weatherwidget.app-main-85:/drawable/widget_preview.xml"}, {"merged": "com.example.weatherwidget.app-debug-83:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.example.weatherwidget.app-main-85:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.example.weatherwidget.app-debug-83:/drawable_ic_thunderstorm.xml.flat", "source": "com.example.weatherwidget.app-main-85:/drawable/ic_thunderstorm.xml"}, {"merged": "com.example.weatherwidget.app-debug-83:/xml_backup_rules.xml.flat", "source": "com.example.weatherwidget.app-main-85:/xml/backup_rules.xml"}, {"merged": "com.example.weatherwidget.app-debug-83:/raw_aboutlibraries.json.flat", "source": "com.example.weatherwidget.app-debug-78:/raw/aboutlibraries.json"}, {"merged": "com.example.weatherwidget.app-debug-83:/drawable_widget_background.xml.flat", "source": "com.example.weatherwidget.app-main-85:/drawable/widget_background.xml"}, {"merged": "com.example.weatherwidget.app-debug-83:/xml_data_extraction_rules.xml.flat", "source": "com.example.weatherwidget.app-main-85:/xml/data_extraction_rules.xml"}, {"merged": "com.example.weatherwidget.app-debug-83:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.example.weatherwidget.app-main-85:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.example.weatherwidget.app-debug-83:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.example.weatherwidget.app-main-85:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.example.weatherwidget.app-debug-83:/mipmap-anydpi_ic_launcher_round.xml.flat", "source": "com.example.weatherwidget.app-main-85:/mipmap-anydpi/ic_launcher_round.xml"}, {"merged": "com.example.weatherwidget.app-debug-83:/drawable_ic_fog.xml.flat", "source": "com.example.weatherwidget.app-main-85:/drawable/ic_fog.xml"}, {"merged": "com.example.weatherwidget.app-debug-83:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.example.weatherwidget.app-main-85:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.weatherwidget.app-debug-83:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.example.weatherwidget.app-main-85:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.example.weatherwidget.app-debug-83:/drawable_ic_rain_day.xml.flat", "source": "com.example.weatherwidget.app-main-85:/drawable/ic_rain_day.xml"}, {"merged": "com.example.weatherwidget.app-debug-83:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.example.weatherwidget.app-main-85:/mipmap-xxxhdpi/ic_launcher_round.webp"}]