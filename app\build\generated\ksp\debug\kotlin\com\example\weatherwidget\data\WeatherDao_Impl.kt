package com.example.weatherwidget.`data`

import androidx.room.EntityDeleteOrUpdateAdapter
import androidx.room.EntityInsertAdapter
import androidx.room.RoomDatabase
import androidx.room.coroutines.createFlow
import androidx.room.util.getColumnIndexOrThrow
import androidx.room.util.performSuspending
import androidx.sqlite.SQLiteStatement
import com.example.weatherwidget.`data`.model.WeatherInfo
import javax.`annotation`.processing.Generated
import kotlin.Boolean
import kotlin.Double
import kotlin.Int
import kotlin.Long
import kotlin.String
import kotlin.Suppress
import kotlin.Unit
import kotlin.collections.List
import kotlin.collections.MutableList
import kotlin.collections.mutableListOf
import kotlin.reflect.KClass
import kotlinx.coroutines.flow.Flow

@Generated(value = ["androidx.room.RoomProcessor"])
@Suppress(names = ["UNCHECKED_CAST", "DEPRECATION", "REDUNDANT_PROJECTION", "REMOVAL"])
public class WeatherDao_Impl(
  __db: RoomDatabase,
) : WeatherDao {
  private val __db: RoomDatabase

  private val __insertAdapterOfWeatherInfo: EntityInsertAdapter<WeatherInfo>

  private val __updateAdapterOfWeatherInfo: EntityDeleteOrUpdateAdapter<WeatherInfo>
  init {
    this.__db = __db
    this.__insertAdapterOfWeatherInfo = object : EntityInsertAdapter<WeatherInfo>() {
      protected override fun createQuery(): String =
          "INSERT OR REPLACE INTO `weather` (`cityId`,`cityName`,`country`,`temperature`,`feelsLike`,`humidity`,`pressure`,`windSpeed`,`windDirection`,`weatherCondition`,`weatherDescription`,`weatherIconCode`,`lastUpdated`,`isFavorite`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)"

      protected override fun bind(statement: SQLiteStatement, entity: WeatherInfo) {
        statement.bindLong(1, entity.cityId)
        statement.bindText(2, entity.cityName)
        statement.bindText(3, entity.country)
        statement.bindDouble(4, entity.temperature)
        statement.bindDouble(5, entity.feelsLike)
        statement.bindLong(6, entity.humidity.toLong())
        statement.bindLong(7, entity.pressure.toLong())
        statement.bindDouble(8, entity.windSpeed)
        statement.bindLong(9, entity.windDirection.toLong())
        statement.bindText(10, entity.weatherCondition)
        statement.bindText(11, entity.weatherDescription)
        statement.bindText(12, entity.weatherIconCode)
        statement.bindLong(13, entity.lastUpdated)
        val _tmp: Int = if (entity.isFavorite) 1 else 0
        statement.bindLong(14, _tmp.toLong())
      }
    }
    this.__updateAdapterOfWeatherInfo = object : EntityDeleteOrUpdateAdapter<WeatherInfo>() {
      protected override fun createQuery(): String =
          "UPDATE OR ABORT `weather` SET `cityId` = ?,`cityName` = ?,`country` = ?,`temperature` = ?,`feelsLike` = ?,`humidity` = ?,`pressure` = ?,`windSpeed` = ?,`windDirection` = ?,`weatherCondition` = ?,`weatherDescription` = ?,`weatherIconCode` = ?,`lastUpdated` = ?,`isFavorite` = ? WHERE `cityId` = ?"

      protected override fun bind(statement: SQLiteStatement, entity: WeatherInfo) {
        statement.bindLong(1, entity.cityId)
        statement.bindText(2, entity.cityName)
        statement.bindText(3, entity.country)
        statement.bindDouble(4, entity.temperature)
        statement.bindDouble(5, entity.feelsLike)
        statement.bindLong(6, entity.humidity.toLong())
        statement.bindLong(7, entity.pressure.toLong())
        statement.bindDouble(8, entity.windSpeed)
        statement.bindLong(9, entity.windDirection.toLong())
        statement.bindText(10, entity.weatherCondition)
        statement.bindText(11, entity.weatherDescription)
        statement.bindText(12, entity.weatherIconCode)
        statement.bindLong(13, entity.lastUpdated)
        val _tmp: Int = if (entity.isFavorite) 1 else 0
        statement.bindLong(14, _tmp.toLong())
        statement.bindLong(15, entity.cityId)
      }
    }
  }

  public override suspend fun insertWeatherInfo(weatherInfo: WeatherInfo): Unit =
      performSuspending(__db, false, true) { _connection ->
    __insertAdapterOfWeatherInfo.insert(_connection, weatherInfo)
  }

  public override suspend fun updateWeatherInfo(weatherInfo: WeatherInfo): Unit =
      performSuspending(__db, false, true) { _connection ->
    __updateAdapterOfWeatherInfo.handle(_connection, weatherInfo)
  }

  public override fun getAllWeatherInfo(): Flow<List<WeatherInfo>> {
    val _sql: String = "SELECT * FROM weather ORDER BY cityName ASC"
    return createFlow(__db, false, arrayOf("weather")) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        val _columnIndexOfCityId: Int = getColumnIndexOrThrow(_stmt, "cityId")
        val _columnIndexOfCityName: Int = getColumnIndexOrThrow(_stmt, "cityName")
        val _columnIndexOfCountry: Int = getColumnIndexOrThrow(_stmt, "country")
        val _columnIndexOfTemperature: Int = getColumnIndexOrThrow(_stmt, "temperature")
        val _columnIndexOfFeelsLike: Int = getColumnIndexOrThrow(_stmt, "feelsLike")
        val _columnIndexOfHumidity: Int = getColumnIndexOrThrow(_stmt, "humidity")
        val _columnIndexOfPressure: Int = getColumnIndexOrThrow(_stmt, "pressure")
        val _columnIndexOfWindSpeed: Int = getColumnIndexOrThrow(_stmt, "windSpeed")
        val _columnIndexOfWindDirection: Int = getColumnIndexOrThrow(_stmt, "windDirection")
        val _columnIndexOfWeatherCondition: Int = getColumnIndexOrThrow(_stmt, "weatherCondition")
        val _columnIndexOfWeatherDescription: Int = getColumnIndexOrThrow(_stmt,
            "weatherDescription")
        val _columnIndexOfWeatherIconCode: Int = getColumnIndexOrThrow(_stmt, "weatherIconCode")
        val _columnIndexOfLastUpdated: Int = getColumnIndexOrThrow(_stmt, "lastUpdated")
        val _columnIndexOfIsFavorite: Int = getColumnIndexOrThrow(_stmt, "isFavorite")
        val _result: MutableList<WeatherInfo> = mutableListOf()
        while (_stmt.step()) {
          val _item: WeatherInfo
          val _tmpCityId: Long
          _tmpCityId = _stmt.getLong(_columnIndexOfCityId)
          val _tmpCityName: String
          _tmpCityName = _stmt.getText(_columnIndexOfCityName)
          val _tmpCountry: String
          _tmpCountry = _stmt.getText(_columnIndexOfCountry)
          val _tmpTemperature: Double
          _tmpTemperature = _stmt.getDouble(_columnIndexOfTemperature)
          val _tmpFeelsLike: Double
          _tmpFeelsLike = _stmt.getDouble(_columnIndexOfFeelsLike)
          val _tmpHumidity: Int
          _tmpHumidity = _stmt.getLong(_columnIndexOfHumidity).toInt()
          val _tmpPressure: Int
          _tmpPressure = _stmt.getLong(_columnIndexOfPressure).toInt()
          val _tmpWindSpeed: Double
          _tmpWindSpeed = _stmt.getDouble(_columnIndexOfWindSpeed)
          val _tmpWindDirection: Int
          _tmpWindDirection = _stmt.getLong(_columnIndexOfWindDirection).toInt()
          val _tmpWeatherCondition: String
          _tmpWeatherCondition = _stmt.getText(_columnIndexOfWeatherCondition)
          val _tmpWeatherDescription: String
          _tmpWeatherDescription = _stmt.getText(_columnIndexOfWeatherDescription)
          val _tmpWeatherIconCode: String
          _tmpWeatherIconCode = _stmt.getText(_columnIndexOfWeatherIconCode)
          val _tmpLastUpdated: Long
          _tmpLastUpdated = _stmt.getLong(_columnIndexOfLastUpdated)
          val _tmpIsFavorite: Boolean
          val _tmp: Int
          _tmp = _stmt.getLong(_columnIndexOfIsFavorite).toInt()
          _tmpIsFavorite = _tmp != 0
          _item =
              WeatherInfo(_tmpCityId,_tmpCityName,_tmpCountry,_tmpTemperature,_tmpFeelsLike,_tmpHumidity,_tmpPressure,_tmpWindSpeed,_tmpWindDirection,_tmpWeatherCondition,_tmpWeatherDescription,_tmpWeatherIconCode,_tmpLastUpdated,_tmpIsFavorite)
          _result.add(_item)
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override fun getFavoriteWeatherInfo(): Flow<List<WeatherInfo>> {
    val _sql: String = "SELECT * FROM weather WHERE isFavorite = 1 ORDER BY cityName ASC"
    return createFlow(__db, false, arrayOf("weather")) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        val _columnIndexOfCityId: Int = getColumnIndexOrThrow(_stmt, "cityId")
        val _columnIndexOfCityName: Int = getColumnIndexOrThrow(_stmt, "cityName")
        val _columnIndexOfCountry: Int = getColumnIndexOrThrow(_stmt, "country")
        val _columnIndexOfTemperature: Int = getColumnIndexOrThrow(_stmt, "temperature")
        val _columnIndexOfFeelsLike: Int = getColumnIndexOrThrow(_stmt, "feelsLike")
        val _columnIndexOfHumidity: Int = getColumnIndexOrThrow(_stmt, "humidity")
        val _columnIndexOfPressure: Int = getColumnIndexOrThrow(_stmt, "pressure")
        val _columnIndexOfWindSpeed: Int = getColumnIndexOrThrow(_stmt, "windSpeed")
        val _columnIndexOfWindDirection: Int = getColumnIndexOrThrow(_stmt, "windDirection")
        val _columnIndexOfWeatherCondition: Int = getColumnIndexOrThrow(_stmt, "weatherCondition")
        val _columnIndexOfWeatherDescription: Int = getColumnIndexOrThrow(_stmt,
            "weatherDescription")
        val _columnIndexOfWeatherIconCode: Int = getColumnIndexOrThrow(_stmt, "weatherIconCode")
        val _columnIndexOfLastUpdated: Int = getColumnIndexOrThrow(_stmt, "lastUpdated")
        val _columnIndexOfIsFavorite: Int = getColumnIndexOrThrow(_stmt, "isFavorite")
        val _result: MutableList<WeatherInfo> = mutableListOf()
        while (_stmt.step()) {
          val _item: WeatherInfo
          val _tmpCityId: Long
          _tmpCityId = _stmt.getLong(_columnIndexOfCityId)
          val _tmpCityName: String
          _tmpCityName = _stmt.getText(_columnIndexOfCityName)
          val _tmpCountry: String
          _tmpCountry = _stmt.getText(_columnIndexOfCountry)
          val _tmpTemperature: Double
          _tmpTemperature = _stmt.getDouble(_columnIndexOfTemperature)
          val _tmpFeelsLike: Double
          _tmpFeelsLike = _stmt.getDouble(_columnIndexOfFeelsLike)
          val _tmpHumidity: Int
          _tmpHumidity = _stmt.getLong(_columnIndexOfHumidity).toInt()
          val _tmpPressure: Int
          _tmpPressure = _stmt.getLong(_columnIndexOfPressure).toInt()
          val _tmpWindSpeed: Double
          _tmpWindSpeed = _stmt.getDouble(_columnIndexOfWindSpeed)
          val _tmpWindDirection: Int
          _tmpWindDirection = _stmt.getLong(_columnIndexOfWindDirection).toInt()
          val _tmpWeatherCondition: String
          _tmpWeatherCondition = _stmt.getText(_columnIndexOfWeatherCondition)
          val _tmpWeatherDescription: String
          _tmpWeatherDescription = _stmt.getText(_columnIndexOfWeatherDescription)
          val _tmpWeatherIconCode: String
          _tmpWeatherIconCode = _stmt.getText(_columnIndexOfWeatherIconCode)
          val _tmpLastUpdated: Long
          _tmpLastUpdated = _stmt.getLong(_columnIndexOfLastUpdated)
          val _tmpIsFavorite: Boolean
          val _tmp: Int
          _tmp = _stmt.getLong(_columnIndexOfIsFavorite).toInt()
          _tmpIsFavorite = _tmp != 0
          _item =
              WeatherInfo(_tmpCityId,_tmpCityName,_tmpCountry,_tmpTemperature,_tmpFeelsLike,_tmpHumidity,_tmpPressure,_tmpWindSpeed,_tmpWindDirection,_tmpWeatherCondition,_tmpWeatherDescription,_tmpWeatherIconCode,_tmpLastUpdated,_tmpIsFavorite)
          _result.add(_item)
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun getWeatherInfoById(cityId: Long): WeatherInfo? {
    val _sql: String = "SELECT * FROM weather WHERE cityId = ?"
    return performSuspending(__db, true, false) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindLong(_argIndex, cityId)
        val _columnIndexOfCityId: Int = getColumnIndexOrThrow(_stmt, "cityId")
        val _columnIndexOfCityName: Int = getColumnIndexOrThrow(_stmt, "cityName")
        val _columnIndexOfCountry: Int = getColumnIndexOrThrow(_stmt, "country")
        val _columnIndexOfTemperature: Int = getColumnIndexOrThrow(_stmt, "temperature")
        val _columnIndexOfFeelsLike: Int = getColumnIndexOrThrow(_stmt, "feelsLike")
        val _columnIndexOfHumidity: Int = getColumnIndexOrThrow(_stmt, "humidity")
        val _columnIndexOfPressure: Int = getColumnIndexOrThrow(_stmt, "pressure")
        val _columnIndexOfWindSpeed: Int = getColumnIndexOrThrow(_stmt, "windSpeed")
        val _columnIndexOfWindDirection: Int = getColumnIndexOrThrow(_stmt, "windDirection")
        val _columnIndexOfWeatherCondition: Int = getColumnIndexOrThrow(_stmt, "weatherCondition")
        val _columnIndexOfWeatherDescription: Int = getColumnIndexOrThrow(_stmt,
            "weatherDescription")
        val _columnIndexOfWeatherIconCode: Int = getColumnIndexOrThrow(_stmt, "weatherIconCode")
        val _columnIndexOfLastUpdated: Int = getColumnIndexOrThrow(_stmt, "lastUpdated")
        val _columnIndexOfIsFavorite: Int = getColumnIndexOrThrow(_stmt, "isFavorite")
        val _result: WeatherInfo?
        if (_stmt.step()) {
          val _tmpCityId: Long
          _tmpCityId = _stmt.getLong(_columnIndexOfCityId)
          val _tmpCityName: String
          _tmpCityName = _stmt.getText(_columnIndexOfCityName)
          val _tmpCountry: String
          _tmpCountry = _stmt.getText(_columnIndexOfCountry)
          val _tmpTemperature: Double
          _tmpTemperature = _stmt.getDouble(_columnIndexOfTemperature)
          val _tmpFeelsLike: Double
          _tmpFeelsLike = _stmt.getDouble(_columnIndexOfFeelsLike)
          val _tmpHumidity: Int
          _tmpHumidity = _stmt.getLong(_columnIndexOfHumidity).toInt()
          val _tmpPressure: Int
          _tmpPressure = _stmt.getLong(_columnIndexOfPressure).toInt()
          val _tmpWindSpeed: Double
          _tmpWindSpeed = _stmt.getDouble(_columnIndexOfWindSpeed)
          val _tmpWindDirection: Int
          _tmpWindDirection = _stmt.getLong(_columnIndexOfWindDirection).toInt()
          val _tmpWeatherCondition: String
          _tmpWeatherCondition = _stmt.getText(_columnIndexOfWeatherCondition)
          val _tmpWeatherDescription: String
          _tmpWeatherDescription = _stmt.getText(_columnIndexOfWeatherDescription)
          val _tmpWeatherIconCode: String
          _tmpWeatherIconCode = _stmt.getText(_columnIndexOfWeatherIconCode)
          val _tmpLastUpdated: Long
          _tmpLastUpdated = _stmt.getLong(_columnIndexOfLastUpdated)
          val _tmpIsFavorite: Boolean
          val _tmp: Int
          _tmp = _stmt.getLong(_columnIndexOfIsFavorite).toInt()
          _tmpIsFavorite = _tmp != 0
          _result =
              WeatherInfo(_tmpCityId,_tmpCityName,_tmpCountry,_tmpTemperature,_tmpFeelsLike,_tmpHumidity,_tmpPressure,_tmpWindSpeed,_tmpWindDirection,_tmpWeatherCondition,_tmpWeatherDescription,_tmpWeatherIconCode,_tmpLastUpdated,_tmpIsFavorite)
        } else {
          _result = null
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override fun getWeatherInfoByIdAsFlow(cityId: Long): Flow<WeatherInfo?> {
    val _sql: String = "SELECT * FROM weather WHERE cityId = ?"
    return createFlow(__db, false, arrayOf("weather")) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindLong(_argIndex, cityId)
        val _columnIndexOfCityId: Int = getColumnIndexOrThrow(_stmt, "cityId")
        val _columnIndexOfCityName: Int = getColumnIndexOrThrow(_stmt, "cityName")
        val _columnIndexOfCountry: Int = getColumnIndexOrThrow(_stmt, "country")
        val _columnIndexOfTemperature: Int = getColumnIndexOrThrow(_stmt, "temperature")
        val _columnIndexOfFeelsLike: Int = getColumnIndexOrThrow(_stmt, "feelsLike")
        val _columnIndexOfHumidity: Int = getColumnIndexOrThrow(_stmt, "humidity")
        val _columnIndexOfPressure: Int = getColumnIndexOrThrow(_stmt, "pressure")
        val _columnIndexOfWindSpeed: Int = getColumnIndexOrThrow(_stmt, "windSpeed")
        val _columnIndexOfWindDirection: Int = getColumnIndexOrThrow(_stmt, "windDirection")
        val _columnIndexOfWeatherCondition: Int = getColumnIndexOrThrow(_stmt, "weatherCondition")
        val _columnIndexOfWeatherDescription: Int = getColumnIndexOrThrow(_stmt,
            "weatherDescription")
        val _columnIndexOfWeatherIconCode: Int = getColumnIndexOrThrow(_stmt, "weatherIconCode")
        val _columnIndexOfLastUpdated: Int = getColumnIndexOrThrow(_stmt, "lastUpdated")
        val _columnIndexOfIsFavorite: Int = getColumnIndexOrThrow(_stmt, "isFavorite")
        val _result: WeatherInfo?
        if (_stmt.step()) {
          val _tmpCityId: Long
          _tmpCityId = _stmt.getLong(_columnIndexOfCityId)
          val _tmpCityName: String
          _tmpCityName = _stmt.getText(_columnIndexOfCityName)
          val _tmpCountry: String
          _tmpCountry = _stmt.getText(_columnIndexOfCountry)
          val _tmpTemperature: Double
          _tmpTemperature = _stmt.getDouble(_columnIndexOfTemperature)
          val _tmpFeelsLike: Double
          _tmpFeelsLike = _stmt.getDouble(_columnIndexOfFeelsLike)
          val _tmpHumidity: Int
          _tmpHumidity = _stmt.getLong(_columnIndexOfHumidity).toInt()
          val _tmpPressure: Int
          _tmpPressure = _stmt.getLong(_columnIndexOfPressure).toInt()
          val _tmpWindSpeed: Double
          _tmpWindSpeed = _stmt.getDouble(_columnIndexOfWindSpeed)
          val _tmpWindDirection: Int
          _tmpWindDirection = _stmt.getLong(_columnIndexOfWindDirection).toInt()
          val _tmpWeatherCondition: String
          _tmpWeatherCondition = _stmt.getText(_columnIndexOfWeatherCondition)
          val _tmpWeatherDescription: String
          _tmpWeatherDescription = _stmt.getText(_columnIndexOfWeatherDescription)
          val _tmpWeatherIconCode: String
          _tmpWeatherIconCode = _stmt.getText(_columnIndexOfWeatherIconCode)
          val _tmpLastUpdated: Long
          _tmpLastUpdated = _stmt.getLong(_columnIndexOfLastUpdated)
          val _tmpIsFavorite: Boolean
          val _tmp: Int
          _tmp = _stmt.getLong(_columnIndexOfIsFavorite).toInt()
          _tmpIsFavorite = _tmp != 0
          _result =
              WeatherInfo(_tmpCityId,_tmpCityName,_tmpCountry,_tmpTemperature,_tmpFeelsLike,_tmpHumidity,_tmpPressure,_tmpWindSpeed,_tmpWindDirection,_tmpWeatherCondition,_tmpWeatherDescription,_tmpWeatherIconCode,_tmpLastUpdated,_tmpIsFavorite)
        } else {
          _result = null
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun getWeatherCount(): Int {
    val _sql: String = "SELECT COUNT(*) FROM weather"
    return performSuspending(__db, true, false) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        val _result: Int
        if (_stmt.step()) {
          val _tmp: Int
          _tmp = _stmt.getLong(0).toInt()
          _result = _tmp
        } else {
          _result = 0
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun updateFavoriteStatus(cityId: Long, isFavorite: Boolean) {
    val _sql: String = "UPDATE weather SET isFavorite = ? WHERE cityId = ?"
    return performSuspending(__db, false, true) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        val _tmp: Int = if (isFavorite) 1 else 0
        _stmt.bindLong(_argIndex, _tmp.toLong())
        _argIndex = 2
        _stmt.bindLong(_argIndex, cityId)
        _stmt.step()
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun deleteWeatherInfo(cityId: Long) {
    val _sql: String = "DELETE FROM weather WHERE cityId = ?"
    return performSuspending(__db, false, true) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindLong(_argIndex, cityId)
        _stmt.step()
      } finally {
        _stmt.close()
      }
    }
  }

  public companion object {
    public fun getRequiredConverters(): List<KClass<*>> = emptyList()
  }
}
