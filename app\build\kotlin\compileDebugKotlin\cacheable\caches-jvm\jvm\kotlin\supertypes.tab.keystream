.com.example.weatherwidget.data.WeatherDao_Impl3com.example.weatherwidget.data.WeatherDatabase_Impl$com.example.weatherwidget.WeatherApp.com.example.weatherwidget.data.WeatherDatabase?com.example.weatherwidget.data.repository.WeatherRepositoryImpl)com.example.weatherwidget.ui.MainActivity*com.example.weatherwidget.ui.MainViewModel.com.example.weatherwidget.widget.WeatherWidget.com.example.weatherwidget.widget.OpenAppAction<com.example.weatherwidget.widget.WeatherWidgetConfigActivity6com.example.weatherwidget.widget.WeatherWidgetReceiver7com.example.weatherwidget.widget.WeatherWidgetViewModel2com.example.weatherwidget.worker.KoinWorkerFactory4com.example.weatherwidget.worker.WeatherUpdateWorker?com.example.weatherwidget.data.repository.MockWeatherRepository                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  