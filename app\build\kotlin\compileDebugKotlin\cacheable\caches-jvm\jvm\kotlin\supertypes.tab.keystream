.com.example.weatherwidget.data.WeatherDao_Impl3com.example.weatherwidget.data.WeatherDatabase_Impl$com.example.weatherwidget.WeatherApp.com.example.weatherwidget.data.WeatherDatabaseDcom.example.weatherwidget.data.model.WidgetConfiguration.$serializer0com.example.weatherwidget.data.model.WidgetThemeFcom.example.weatherwidget.data.model.LocationDisplayConfig.$serializerBcom.example.weatherwidget.data.model.TimeDisplayConfig.$serializerFcom.example.weatherwidget.data.model.WeatherElementsConfig.$serializerJcom.example.weatherwidget.data.model.VisualCustomizationConfig.$serializer-com.example.weatherwidget.data.model.IconSize-com.example.weatherwidget.data.model.FontSize1com.example.weatherwidget.data.model.CornerRadiusEcom.example.weatherwidget.data.model.UpdateSettingsConfig.$serializer0com.example.weatherwidget.data.model.RefreshRate6com.example.weatherwidget.data.model.NetworkConstraint?com.example.weatherwidget.data.repository.MockWeatherRepository?com.example.weatherwidget.data.repository.WeatherRepositoryImpl)com.example.weatherwidget.ui.MainActivity*com.example.weatherwidget.ui.MainViewModel.com.example.weatherwidget.widget.WeatherWidget.com.example.weatherwidget.widget.OpenAppAction<com.example.weatherwidget.widget.WeatherWidgetConfigActivity6com.example.weatherwidget.widget.WeatherWidgetReceiver7com.example.weatherwidget.widget.WeatherWidgetViewModel2com.example.weatherwidget.worker.KoinWorkerFactory4com.example.weatherwidget.worker.WeatherUpdateWorker                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    