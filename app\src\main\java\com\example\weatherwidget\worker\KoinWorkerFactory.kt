package com.example.weatherwidget.worker

import android.content.Context
import androidx.work.ListenableWorker
import androidx.work.WorkerFactory
import androidx.work.WorkerParameters
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import org.koin.core.parameter.parametersOf

/**
 * Factory class that enables Koin dependency injection for WorkManager workers
 */
class KoinWorkerFactory : WorkerFactory(), KoinComponent {

    override fun createWorker(
        appContext: Context,
        workerClassName: String,
        workerParameters: WorkerParameters
    ): ListenableWorker? {
        return when (workerClassName) {
            WeatherUpdateWorker::class.java.name -> {
                val worker: WeatherUpdateWorker by inject { parametersOf(appContext, workerParameters) }
                worker
            }
            else -> null
        }
    }
}