-- Merging decision tree log ---
provider#androidx.startup.InitializationProvider
INJECTED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:23:9-32:20
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9e9496b1403961c155e46b481d6b657e\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9e9496b1403961c155e46b481d6b657e\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\070431985ac0ce73e7537b3ab66df7b1\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\070431985ac0ce73e7537b3ab66df7b1\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\15e33be53af35534a537a00acc2d325c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\15e33be53af35534a537a00acc2d325c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:27:13-31
	android:authorities
		INJECTED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:25:13-68
	android:exported
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:26:13-37
	android:name
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:24:13-67
manifest
ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:2:1-69:12
INJECTED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:2:1-69:12
INJECTED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:2:1-69:12
INJECTED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:2:1-69:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3201fe8c5c05f79d61b7d9bc23a9b124\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\20bd3fbd4ea453d0de06bb060369c40b\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\554ea434be603d81f0fb2f0bdd5ecc35\transformed\navigation-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2acc0916e8403ec08af08abcba915883\transformed\navigation-common-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\bf6c8f26d51e8b78fd191bd9550b0dcd\transformed\navigation-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.dagger:hilt-android:2.49] C:\Users\<USER>\.gradle\caches\8.14\transforms\36d52cec0b72d76dae0e5ec3fdc95e39\transformed\hilt-android-2.49\AndroidManifest.xml:16:1-19:12
MERGED from [io.insert-koin:koin-androidx-workmanager:4.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8af6384b9f562d155eba0a0a4b4fdf31\transformed\koin-androidx-workmanager-4.0.0\AndroidManifest.xml:2:1-9:12
MERGED from [io.insert-koin:koin-androidx-compose:4.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\25c1db80fa772b689f7bbdc01a9f0bb3\transformed\koin-androidx-compose-4.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.insert-koin:koin-android:4.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6a5daaf45193126a90eb7e9ba8bccf76\transformed\koin-android-4.0.0\AndroidManifest.xml:2:1-9:12
MERGED from [io.coil-kt:coil-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d9488b5797500079aad9252059fdaf28\transformed\coil-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6f94e38624252bf0fdd94460639ea8de\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil:2.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\77db559cd5a7dd6cc0fae49a7eb73857\transformed\coil-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8fb7dc59e95cf1c12bc84f8770ca52cb\transformed\coil-base-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6d561069ffee61f592209e5e368cf66\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a6d2b7e5ecb18270abb575bfe8bf3041\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3935190af281271db3f88b4c0a2d6cac\transformed\play-services-location-21.3.0\AndroidManifest.xml:2:1-8:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\99eb3aee547f429176c0b3577d528a52\transformed\play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\bfd6da2430e71879973b9be975dbc0b6\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6bdbe06209508b26c4e38693a2ab85d8\transformed\play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment-ktx:1.8.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\4521559dd9e0a4c8692ced4182a0c8c5\transformed\fragment-ktx-1.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.8.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\1fd9d3b4b3ad55377417080a97e270c2\transformed\fragment-1.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\c26fa102c23d3286c53c26e88c6db80d\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.glance:glance:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\07a29fdb86ef710e85f00096aa41bc4b\transformed\glance-1.1.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.glance:glance-material3:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b55c0a07b07157672b36c9d2d6386a83\transformed\glance-material3-1.1.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:17:1-61:12
MERGED from [com.mikepenz:aboutlibraries-compose-android:12.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\2670663386c92ed613916b152d4d36f9\transformed\aboutlibraries-compose-m2-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.mikepenz:aboutlibraries-compose-core-android:12.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\daa3374995f4428583fdb9dce24dce95\transformed\aboutlibraries-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\26a7be6aa46fc6df32cf278684c813b2\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\264c68dfc538be59325a60f9fa4ef3dc\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\a610e101ac4af780cc066bc1fcdcf49f\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\06d808385d814fc5793833c3be2ca602\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b4f7794ef3aff0e3b932108f77727cec\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\14da59c6b840cb0adf3028eb4dafb62b\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\0ec793b9525bc2c9df0a8f9fb86ba63f\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b163c0281da8e7125caa796aca357870\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\ebd1e386d662d88fbd627812d66cec73\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\1e6b22b4b9f7c927a5cbb74285dcf12e\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\37715cebd1c1507481f33c81e6bb71d6\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\5ffa6cb7250c4176508453e781f097db\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\edefb81d0fd702a290e7a6aa35560d0b\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\9d7fa17d87fa8eff4ec49e9b1333f9cb\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.material:material-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\821e8c1a7433219b3163b17b67af0011\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3045c0102cd635326ed28615030ea577\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.work:work-runtime-ktx:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\d45a9bd51944eb6b6265328066ddbed1\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.core:core-remoteviews:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\190d4b6fe45ede2433fdba7f25f72b62\transformed\core-remoteviews-1.1.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1d4cd3ea24c174f95b3281e0743d703b\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\824996bb31f9e6334e6df8cc03375312\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\abd9f1e06f2d3a321251084c1e235ad5\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a8b7c59f19597e8a49907a49b2461092\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9e9496b1403961c155e46b481d6b657e\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\764fb432bc0e78dfc405b0db9bf2cf54\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\84d9f2e21461d29688356ae77d671292\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ae9d8d78dbceddb3bb9f980a46f3e11\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7fbc94c17f682a8ca1d0e42af990aa4e\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\35c12f7b4c3422481193778ef319e435\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\30287725489871af330b7aa82a2b9f1f\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\070431985ac0ce73e7537b3ab66df7b1\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5f338c64ac7b413fcc35944bd4f9706f\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e5e26c53321c6d9b939559da31cc8ab4\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4231f0f310e792daaf1f4bc443933423\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\065a0dc922676cb32ebf946a7443da1f\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8648a0a15cd473e8924f829cb8f4dadb\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3cbac0ac1c57f5294183717eb3060b6e\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\687a7ab236997bdae1f83e4213516f3b\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\577c3c8ed27311a40cbd2cf2cf9356c2\transformed\savedstate-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\853221b3e22986c07128ae191e7cb964\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\cd949c0c700901654b232af64fc9a9c9\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7a92e9997de252473292ff3ca6f25465\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7cbe36f686be85653c4e06fdc53d9988\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b550d5f08c9cf0fdcd9c622defde1834\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\468a178052a16f98b2c320ddb44fb56d\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f92514d07876b6d24620f2c91c94164\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.14\transforms\ce613b733a7f5a86639ee86f28f0cc82\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.14\transforms\6a239ad1552904d2ee95202744711a7e\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\dcea319fdd3ad3c36ece65fd708a73da\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\a24a067677dbc9670fe5924cce484add\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\513bf5c5e31f5fefbc08cc78ed6f0fea\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5062aba79baa1c7b03e20d2e3d4079f3\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [org.jetbrains.androidx.core:core-bundle-android-debug:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\82096d24bd091dd39b03f37c07ac245f\transformed\core-bundle-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\53eaea0159b68d9404aab169cfe918ca\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\0d3deac4716d283862aeb386c4e6aa32\transformed\room-runtime-release\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\82556edd7305424b71db938537a0678b\transformed\room-ktx-2.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\43ef54042a4a5617b6e570bf9425eae0\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.datastore:datastore-core-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\2c58e80f0eee518ec1a72bceb39c0598\transformed\datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-core-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\201f48b8ed70760ad7e076d89f3988a4\transformed\datastore-preferences-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\6238a15f16e8be80a05dfb60ebc3c7b3\transformed\datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\ebede9844bbf09770ff0c1a13b811fda\transformed\datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\57f9d18a699affdd5f847f3d2fe3d12d\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b70ef4d49178d6b2b210743933121cba\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.mikepenz:aboutlibraries-core-android:12.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\14e6e5743b532b0c55cbff61b6548387\transformed\aboutlibraries-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b07ed81c6c8ea5ae7c6927d6160d18db\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\0efd6ddfbb17a7f08ddafb44a4eb7f00\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\15e33be53af35534a537a00acc2d325c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6f862d21a4d1169096cf686437efc7a7\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\bc65b0abf7f80e915620651d6fc4d6c3\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\fc78b92b38712461106e97a00d5929a0\transformed\sqlite-framework-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\74168d796d2460918b03c53c5971a659\transformed\sqlite-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\470bb5cac46e6f55581823ad3e45b948\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\39178bdc16e3e403dc62e2a74ca6dcd9\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1740800ee2be3400dab50a76708113c6\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\52039766a6c0ffaa40f7dfb1b7773efd\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5ed3a2fa6e5b4271a3b1a003a7a0adc0\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:dagger-lint-aar:2.49] C:\Users\<USER>\.gradle\caches\8.14\transforms\7dc5a78b99295a5bc31be1a835d2282a\transformed\dagger-lint-aar-2.49\AndroidManifest.xml:16:1-19:12
	package
		INJECTED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:5-79
	android:name
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:8:5-81
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:5-81
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:5-81
	android:name
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:8:22-78
application
ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:10:5-67:19
INJECTED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:10:5-67:19
MERGED from [io.insert-koin:koin-androidx-workmanager:4.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8af6384b9f562d155eba0a0a4b4fdf31\transformed\koin-androidx-workmanager-4.0.0\AndroidManifest.xml:7:5-20
MERGED from [io.insert-koin:koin-androidx-workmanager:4.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8af6384b9f562d155eba0a0a4b4fdf31\transformed\koin-androidx-workmanager-4.0.0\AndroidManifest.xml:7:5-20
MERGED from [io.insert-koin:koin-android:4.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6a5daaf45193126a90eb7e9ba8bccf76\transformed\koin-android-4.0.0\AndroidManifest.xml:7:5-20
MERGED from [io.insert-koin:koin-android:4.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6a5daaf45193126a90eb7e9ba8bccf76\transformed\koin-android-4.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3935190af281271db3f88b4c0a2d6cac\transformed\play-services-location-21.3.0\AndroidManifest.xml:6:5-20
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3935190af281271db3f88b4c0a2d6cac\transformed\play-services-location-21.3.0\AndroidManifest.xml:6:5-20
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\99eb3aee547f429176c0b3577d528a52\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\99eb3aee547f429176c0b3577d528a52\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\bfd6da2430e71879973b9be975dbc0b6\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\bfd6da2430e71879973b9be975dbc0b6\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6bdbe06209508b26c4e38693a2ab85d8\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6bdbe06209508b26c4e38693a2ab85d8\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:22:5-59:19
MERGED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:22:5-59:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\9d7fa17d87fa8eff4ec49e9b1333f9cb\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\9d7fa17d87fa8eff4ec49e9b1333f9cb\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.core:core-remoteviews:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\190d4b6fe45ede2433fdba7f25f72b62\transformed\core-remoteviews-1.1.0\AndroidManifest.xml:23:5-28:19
MERGED from [androidx.core:core-remoteviews:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\190d4b6fe45ede2433fdba7f25f72b62\transformed\core-remoteviews-1.1.0\AndroidManifest.xml:23:5-28:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9e9496b1403961c155e46b481d6b657e\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9e9496b1403961c155e46b481d6b657e\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\30287725489871af330b7aa82a2b9f1f\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\30287725489871af330b7aa82a2b9f1f\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\070431985ac0ce73e7537b3ab66df7b1\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\070431985ac0ce73e7537b3ab66df7b1\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\0d3deac4716d283862aeb386c4e6aa32\transformed\room-runtime-release\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\0d3deac4716d283862aeb386c4e6aa32\transformed\room-runtime-release\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\15e33be53af35534a537a00acc2d325c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\15e33be53af35534a537a00acc2d325c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\470bb5cac46e6f55581823ad3e45b948\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\470bb5cac46e6f55581823ad3e45b948\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\30287725489871af330b7aa82a2b9f1f\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:18:9-35
	android:label
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:16:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:14:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:17:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:20:9-29
	android:icon
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:15:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:12:9-35
	android:theme
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:19:9-50
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:13:9-65
	android:name
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:11:9-35
activity#com.example.weatherwidget.ui.MainActivity
ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:35:9-43:20
	android:exported
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:37:13-36
	android:theme
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:38:13-54
	android:name
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:36:13-44
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:39:13-42:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:40:17-69
	android:name
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:40:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:41:17-77
	android:name
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:41:27-74
receiver#com.example.weatherwidget.widget.WeatherWidgetReceiver
ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:46:9-55:20
	android:exported
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:48:13-36
	android:name
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:47:13-57
intent-filter#action:name:android.appwidget.action.APPWIDGET_UPDATE
ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:49:13-51:29
action#android.appwidget.action.APPWIDGET_UPDATE
ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:50:17-84
	android:name
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:50:25-81
meta-data#android.appwidget.provider
ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:52:13-54:63
	android:resource
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:54:17-60
	android:name
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:53:17-58
activity#com.example.weatherwidget.widget.WeatherWidgetConfigActivity
ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:58:9-65:20
	android:exported
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:60:13-37
	android:theme
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:61:13-54
	android:name
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:59:13-63
intent-filter#action:name:android.appwidget.action.APPWIDGET_CONFIGURE
ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:62:13-64:29
action#android.appwidget.action.APPWIDGET_CONFIGURE
ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:63:17-87
	android:name
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:63:25-84
meta-data#androidx.work.WorkManagerInitializer
ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:28:13-31:39
REJECTED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:34:13-36:52
	tools:node
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:31:17-36
	android:value
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:30:17-49
	android:name
		ADDED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml:29:17-68
uses-sdk
INJECTED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3201fe8c5c05f79d61b7d9bc23a9b124\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3201fe8c5c05f79d61b7d9bc23a9b124\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\20bd3fbd4ea453d0de06bb060369c40b\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\20bd3fbd4ea453d0de06bb060369c40b\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\554ea434be603d81f0fb2f0bdd5ecc35\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\554ea434be603d81f0fb2f0bdd5ecc35\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2acc0916e8403ec08af08abcba915883\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2acc0916e8403ec08af08abcba915883\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\bf6c8f26d51e8b78fd191bd9550b0dcd\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\bf6c8f26d51e8b78fd191bd9550b0dcd\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:hilt-android:2.49] C:\Users\<USER>\.gradle\caches\8.14\transforms\36d52cec0b72d76dae0e5ec3fdc95e39\transformed\hilt-android-2.49\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.49] C:\Users\<USER>\.gradle\caches\8.14\transforms\36d52cec0b72d76dae0e5ec3fdc95e39\transformed\hilt-android-2.49\AndroidManifest.xml:18:3-42
MERGED from [io.insert-koin:koin-androidx-workmanager:4.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8af6384b9f562d155eba0a0a4b4fdf31\transformed\koin-androidx-workmanager-4.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.insert-koin:koin-androidx-workmanager:4.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8af6384b9f562d155eba0a0a4b4fdf31\transformed\koin-androidx-workmanager-4.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.insert-koin:koin-androidx-compose:4.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\25c1db80fa772b689f7bbdc01a9f0bb3\transformed\koin-androidx-compose-4.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.insert-koin:koin-androidx-compose:4.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\25c1db80fa772b689f7bbdc01a9f0bb3\transformed\koin-androidx-compose-4.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.insert-koin:koin-android:4.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6a5daaf45193126a90eb7e9ba8bccf76\transformed\koin-android-4.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.insert-koin:koin-android:4.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6a5daaf45193126a90eb7e9ba8bccf76\transformed\koin-android-4.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d9488b5797500079aad9252059fdaf28\transformed\coil-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d9488b5797500079aad9252059fdaf28\transformed\coil-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6f94e38624252bf0fdd94460639ea8de\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6f94e38624252bf0fdd94460639ea8de\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\77db559cd5a7dd6cc0fae49a7eb73857\transformed\coil-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\77db559cd5a7dd6cc0fae49a7eb73857\transformed\coil-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8fb7dc59e95cf1c12bc84f8770ca52cb\transformed\coil-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8fb7dc59e95cf1c12bc84f8770ca52cb\transformed\coil-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6d561069ffee61f592209e5e368cf66\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6d561069ffee61f592209e5e368cf66\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a6d2b7e5ecb18270abb575bfe8bf3041\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a6d2b7e5ecb18270abb575bfe8bf3041\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3935190af281271db3f88b4c0a2d6cac\transformed\play-services-location-21.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3935190af281271db3f88b4c0a2d6cac\transformed\play-services-location-21.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\99eb3aee547f429176c0b3577d528a52\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\99eb3aee547f429176c0b3577d528a52\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\bfd6da2430e71879973b9be975dbc0b6\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\bfd6da2430e71879973b9be975dbc0b6\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6bdbe06209508b26c4e38693a2ab85d8\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6bdbe06209508b26c4e38693a2ab85d8\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\4521559dd9e0a4c8692ced4182a0c8c5\transformed\fragment-ktx-1.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\4521559dd9e0a4c8692ced4182a0c8c5\transformed\fragment-ktx-1.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\1fd9d3b4b3ad55377417080a97e270c2\transformed\fragment-1.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\1fd9d3b4b3ad55377417080a97e270c2\transformed\fragment-1.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\c26fa102c23d3286c53c26e88c6db80d\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\c26fa102c23d3286c53c26e88c6db80d\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.glance:glance:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\07a29fdb86ef710e85f00096aa41bc4b\transformed\glance-1.1.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.glance:glance:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\07a29fdb86ef710e85f00096aa41bc4b\transformed\glance-1.1.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.glance:glance-material3:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b55c0a07b07157672b36c9d2d6386a83\transformed\glance-material3-1.1.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.glance:glance-material3:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b55c0a07b07157672b36c9d2d6386a83\transformed\glance-material3-1.1.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [com.mikepenz:aboutlibraries-compose-android:12.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\2670663386c92ed613916b152d4d36f9\transformed\aboutlibraries-compose-m2-release\AndroidManifest.xml:5:5-44
MERGED from [com.mikepenz:aboutlibraries-compose-android:12.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\2670663386c92ed613916b152d4d36f9\transformed\aboutlibraries-compose-m2-release\AndroidManifest.xml:5:5-44
MERGED from [com.mikepenz:aboutlibraries-compose-core-android:12.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\daa3374995f4428583fdb9dce24dce95\transformed\aboutlibraries-compose-release\AndroidManifest.xml:5:5-44
MERGED from [com.mikepenz:aboutlibraries-compose-core-android:12.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\daa3374995f4428583fdb9dce24dce95\transformed\aboutlibraries-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\26a7be6aa46fc6df32cf278684c813b2\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\26a7be6aa46fc6df32cf278684c813b2\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\264c68dfc538be59325a60f9fa4ef3dc\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\264c68dfc538be59325a60f9fa4ef3dc\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\a610e101ac4af780cc066bc1fcdcf49f\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\a610e101ac4af780cc066bc1fcdcf49f\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\06d808385d814fc5793833c3be2ca602\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\06d808385d814fc5793833c3be2ca602\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b4f7794ef3aff0e3b932108f77727cec\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b4f7794ef3aff0e3b932108f77727cec\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\14da59c6b840cb0adf3028eb4dafb62b\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\14da59c6b840cb0adf3028eb4dafb62b\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\0ec793b9525bc2c9df0a8f9fb86ba63f\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\0ec793b9525bc2c9df0a8f9fb86ba63f\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b163c0281da8e7125caa796aca357870\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b163c0281da8e7125caa796aca357870\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\ebd1e386d662d88fbd627812d66cec73\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\ebd1e386d662d88fbd627812d66cec73\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\1e6b22b4b9f7c927a5cbb74285dcf12e\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\1e6b22b4b9f7c927a5cbb74285dcf12e\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\37715cebd1c1507481f33c81e6bb71d6\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\37715cebd1c1507481f33c81e6bb71d6\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\5ffa6cb7250c4176508453e781f097db\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\5ffa6cb7250c4176508453e781f097db\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\edefb81d0fd702a290e7a6aa35560d0b\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\edefb81d0fd702a290e7a6aa35560d0b\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\9d7fa17d87fa8eff4ec49e9b1333f9cb\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\9d7fa17d87fa8eff4ec49e9b1333f9cb\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\821e8c1a7433219b3163b17b67af0011\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\821e8c1a7433219b3163b17b67af0011\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3045c0102cd635326ed28615030ea577\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3045c0102cd635326ed28615030ea577\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime-ktx:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\d45a9bd51944eb6b6265328066ddbed1\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\d45a9bd51944eb6b6265328066ddbed1\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core-remoteviews:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\190d4b6fe45ede2433fdba7f25f72b62\transformed\core-remoteviews-1.1.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core-remoteviews:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\190d4b6fe45ede2433fdba7f25f72b62\transformed\core-remoteviews-1.1.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1d4cd3ea24c174f95b3281e0743d703b\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1d4cd3ea24c174f95b3281e0743d703b\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\824996bb31f9e6334e6df8cc03375312\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\824996bb31f9e6334e6df8cc03375312\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\abd9f1e06f2d3a321251084c1e235ad5\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\abd9f1e06f2d3a321251084c1e235ad5\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a8b7c59f19597e8a49907a49b2461092\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a8b7c59f19597e8a49907a49b2461092\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9e9496b1403961c155e46b481d6b657e\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9e9496b1403961c155e46b481d6b657e\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\764fb432bc0e78dfc405b0db9bf2cf54\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\764fb432bc0e78dfc405b0db9bf2cf54\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\84d9f2e21461d29688356ae77d671292\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\84d9f2e21461d29688356ae77d671292\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ae9d8d78dbceddb3bb9f980a46f3e11\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4ae9d8d78dbceddb3bb9f980a46f3e11\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7fbc94c17f682a8ca1d0e42af990aa4e\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7fbc94c17f682a8ca1d0e42af990aa4e\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\35c12f7b4c3422481193778ef319e435\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\35c12f7b4c3422481193778ef319e435\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\30287725489871af330b7aa82a2b9f1f\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\30287725489871af330b7aa82a2b9f1f\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\070431985ac0ce73e7537b3ab66df7b1\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\070431985ac0ce73e7537b3ab66df7b1\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5f338c64ac7b413fcc35944bd4f9706f\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5f338c64ac7b413fcc35944bd4f9706f\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e5e26c53321c6d9b939559da31cc8ab4\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e5e26c53321c6d9b939559da31cc8ab4\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4231f0f310e792daaf1f4bc443933423\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4231f0f310e792daaf1f4bc443933423\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\065a0dc922676cb32ebf946a7443da1f\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\065a0dc922676cb32ebf946a7443da1f\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8648a0a15cd473e8924f829cb8f4dadb\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8648a0a15cd473e8924f829cb8f4dadb\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3cbac0ac1c57f5294183717eb3060b6e\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3cbac0ac1c57f5294183717eb3060b6e\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\687a7ab236997bdae1f83e4213516f3b\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\687a7ab236997bdae1f83e4213516f3b\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\577c3c8ed27311a40cbd2cf2cf9356c2\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\577c3c8ed27311a40cbd2cf2cf9356c2\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\853221b3e22986c07128ae191e7cb964\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\853221b3e22986c07128ae191e7cb964\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\cd949c0c700901654b232af64fc9a9c9\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\cd949c0c700901654b232af64fc9a9c9\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7a92e9997de252473292ff3ca6f25465\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7a92e9997de252473292ff3ca6f25465\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7cbe36f686be85653c4e06fdc53d9988\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7cbe36f686be85653c4e06fdc53d9988\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b550d5f08c9cf0fdcd9c622defde1834\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b550d5f08c9cf0fdcd9c622defde1834\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\468a178052a16f98b2c320ddb44fb56d\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\468a178052a16f98b2c320ddb44fb56d\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f92514d07876b6d24620f2c91c94164\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2f92514d07876b6d24620f2c91c94164\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.14\transforms\ce613b733a7f5a86639ee86f28f0cc82\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.14\transforms\ce613b733a7f5a86639ee86f28f0cc82\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.14\transforms\6a239ad1552904d2ee95202744711a7e\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.14\transforms\6a239ad1552904d2ee95202744711a7e\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\dcea319fdd3ad3c36ece65fd708a73da\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\dcea319fdd3ad3c36ece65fd708a73da\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\a24a067677dbc9670fe5924cce484add\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\a24a067677dbc9670fe5924cce484add\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\513bf5c5e31f5fefbc08cc78ed6f0fea\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\513bf5c5e31f5fefbc08cc78ed6f0fea\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5062aba79baa1c7b03e20d2e3d4079f3\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5062aba79baa1c7b03e20d2e3d4079f3\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [org.jetbrains.androidx.core:core-bundle-android-debug:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\82096d24bd091dd39b03f37c07ac245f\transformed\core-bundle-debug\AndroidManifest.xml:5:5-44
MERGED from [org.jetbrains.androidx.core:core-bundle-android-debug:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\82096d24bd091dd39b03f37c07ac245f\transformed\core-bundle-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\53eaea0159b68d9404aab169cfe918ca\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\53eaea0159b68d9404aab169cfe918ca\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\0d3deac4716d283862aeb386c4e6aa32\transformed\room-runtime-release\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\0d3deac4716d283862aeb386c4e6aa32\transformed\room-runtime-release\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\82556edd7305424b71db938537a0678b\transformed\room-ktx-2.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\82556edd7305424b71db938537a0678b\transformed\room-ktx-2.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\43ef54042a4a5617b6e570bf9425eae0\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\43ef54042a4a5617b6e570bf9425eae0\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-core-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\2c58e80f0eee518ec1a72bceb39c0598\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\2c58e80f0eee518ec1a72bceb39c0598\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-core-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\201f48b8ed70760ad7e076d89f3988a4\transformed\datastore-preferences-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-core-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\201f48b8ed70760ad7e076d89f3988a4\transformed\datastore-preferences-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\6238a15f16e8be80a05dfb60ebc3c7b3\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\6238a15f16e8be80a05dfb60ebc3c7b3\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\ebede9844bbf09770ff0c1a13b811fda\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\ebede9844bbf09770ff0c1a13b811fda\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\57f9d18a699affdd5f847f3d2fe3d12d\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\57f9d18a699affdd5f847f3d2fe3d12d\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b70ef4d49178d6b2b210743933121cba\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b70ef4d49178d6b2b210743933121cba\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [com.mikepenz:aboutlibraries-core-android:12.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\14e6e5743b532b0c55cbff61b6548387\transformed\aboutlibraries-core-release\AndroidManifest.xml:5:5-44
MERGED from [com.mikepenz:aboutlibraries-core-android:12.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\14e6e5743b532b0c55cbff61b6548387\transformed\aboutlibraries-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b07ed81c6c8ea5ae7c6927d6160d18db\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b07ed81c6c8ea5ae7c6927d6160d18db\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\0efd6ddfbb17a7f08ddafb44a4eb7f00\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\0efd6ddfbb17a7f08ddafb44a4eb7f00\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\15e33be53af35534a537a00acc2d325c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\15e33be53af35534a537a00acc2d325c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6f862d21a4d1169096cf686437efc7a7\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6f862d21a4d1169096cf686437efc7a7\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\bc65b0abf7f80e915620651d6fc4d6c3\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\bc65b0abf7f80e915620651d6fc4d6c3\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\fc78b92b38712461106e97a00d5929a0\transformed\sqlite-framework-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\fc78b92b38712461106e97a00d5929a0\transformed\sqlite-framework-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\74168d796d2460918b03c53c5971a659\transformed\sqlite-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\74168d796d2460918b03c53c5971a659\transformed\sqlite-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\470bb5cac46e6f55581823ad3e45b948\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\470bb5cac46e6f55581823ad3e45b948\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\39178bdc16e3e403dc62e2a74ca6dcd9\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\39178bdc16e3e403dc62e2a74ca6dcd9\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1740800ee2be3400dab50a76708113c6\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1740800ee2be3400dab50a76708113c6\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\52039766a6c0ffaa40f7dfb1b7773efd\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.14\transforms\52039766a6c0ffaa40f7dfb1b7773efd\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5ed3a2fa6e5b4271a3b1a003a7a0adc0\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5ed3a2fa6e5b4271a3b1a003a7a0adc0\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:dagger-lint-aar:2.49] C:\Users\<USER>\.gradle\caches\8.14\transforms\7dc5a78b99295a5bc31be1a835d2282a\transformed\dagger-lint-aar-2.49\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.49] C:\Users\<USER>\.gradle\caches\8.14\transforms\7dc5a78b99295a5bc31be1a835d2282a\transformed\dagger-lint-aar-2.49\AndroidManifest.xml:18:3-42
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Pictures\widget\app\src\main\AndroidManifest.xml
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\99eb3aee547f429176c0b3577d528a52\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\99eb3aee547f429176c0b3577d528a52\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\99eb3aee547f429176c0b3577d528a52\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\99eb3aee547f429176c0b3577d528a52\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6bdbe06209508b26c4e38693a2ab85d8\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6bdbe06209508b26c4e38693a2ab85d8\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6bdbe06209508b26c4e38693a2ab85d8\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
activity#androidx.glance.appwidget.action.ActionTrampolineActivity
ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:23:9-28:62
	android:enabled
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:25:13-35
	android:excludeFromRecents
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:26:13-46
	android:exported
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:27:13-37
	android:theme
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:28:13-59
	android:name
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:24:13-85
activity#androidx.glance.appwidget.action.InvisibleActionTrampolineActivity
ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:29:9-36:81
	android:enabled
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:31:13-35
	android:launchMode
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:33:13-48
	android:noHistory
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:34:13-37
	android:exported
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:32:13-37
	android:theme
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:36:13-78
	android:taskAffinity
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:35:13-91
	android:name
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:30:13-94
receiver#androidx.glance.appwidget.action.ActionCallbackBroadcastReceiver
ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:38:9-41:40
	android:enabled
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:40:13-35
	android:exported
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:39:13-92
receiver#androidx.glance.appwidget.UnmanagedSessionReceiver
ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:42:9-45:40
	android:enabled
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:44:13-35
	android:exported
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:45:13-37
	android:name
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:43:13-78
receiver#androidx.glance.appwidget.MyPackageReplacedReceiver
ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:46:9-53:20
	android:enabled
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:48:13-35
	android:exported
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:49:13-37
	android:name
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:47:13-79
intent-filter#action:name:android.intent.action.MY_PACKAGE_REPLACED
ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:50:13-52:29
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:51:17-84
	android:name
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:51:25-81
service#androidx.glance.appwidget.GlanceRemoteViewsService
ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:55:9-58:72
	android:exported
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:57:13-36
	android:permission
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:58:13-69
	android:name
		ADDED from [androidx.glance:glance-appwidget:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\f958aeb604e7c143e9bc08e83e2ba828\transformed\glance-appwidget-1.1.1\AndroidManifest.xml:56:13-78
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\9d7fa17d87fa8eff4ec49e9b1333f9cb\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\9d7fa17d87fa8eff4ec49e9b1333f9cb\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\9d7fa17d87fa8eff4ec49e9b1333f9cb\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
uses-permission#android.permission.WAKE_LOCK
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:5-68
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:22-65
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:22-74
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea6cdc6a4f354f494fb001d5b96313da\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
service#androidx.core.widget.RemoteViewsCompatService
ADDED from [androidx.core:core-remoteviews:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\190d4b6fe45ede2433fdba7f25f72b62\transformed\core-remoteviews-1.1.0\AndroidManifest.xml:24:9-27:63
	tools:ignore
		ADDED from [androidx.core:core-remoteviews:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\190d4b6fe45ede2433fdba7f25f72b62\transformed\core-remoteviews-1.1.0\AndroidManifest.xml:27:13-60
	android:permission
		ADDED from [androidx.core:core-remoteviews:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\190d4b6fe45ede2433fdba7f25f72b62\transformed\core-remoteviews-1.1.0\AndroidManifest.xml:26:13-69
	android:name
		ADDED from [androidx.core:core-remoteviews:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\190d4b6fe45ede2433fdba7f25f72b62\transformed\core-remoteviews-1.1.0\AndroidManifest.xml:25:13-73
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9e9496b1403961c155e46b481d6b657e\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9e9496b1403961c155e46b481d6b657e\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9e9496b1403961c155e46b481d6b657e\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\30287725489871af330b7aa82a2b9f1f\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\30287725489871af330b7aa82a2b9f1f\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\30287725489871af330b7aa82a2b9f1f\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.example.weatherwidget.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\30287725489871af330b7aa82a2b9f1f\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\30287725489871af330b7aa82a2b9f1f\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\30287725489871af330b7aa82a2b9f1f\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\30287725489871af330b7aa82a2b9f1f\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\30287725489871af330b7aa82a2b9f1f\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.weatherwidget.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\30287725489871af330b7aa82a2b9f1f\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\30287725489871af330b7aa82a2b9f1f\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\070431985ac0ce73e7537b3ab66df7b1\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\070431985ac0ce73e7537b3ab66df7b1\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\070431985ac0ce73e7537b3ab66df7b1\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\0d3deac4716d283862aeb386c4e6aa32\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\0d3deac4716d283862aeb386c4e6aa32\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\0d3deac4716d283862aeb386c4e6aa32\transformed\room-runtime-release\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\0d3deac4716d283862aeb386c4e6aa32\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\0d3deac4716d283862aeb386c4e6aa32\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\071ab51ccb07f7ad417d66b988373926\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
