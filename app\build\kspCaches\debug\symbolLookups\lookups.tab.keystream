  Activity android.app  AppWidgetManager android.appwidget  Context android.content  Intent android.content  Bundle 
android.os  ComponentActivity androidx.activity  
setContent androidx.activity.compose  	clickable androidx.compose.foundation  Box "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  
LazyColumn  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  KeyboardActions  androidx.compose.foundation.text  KeyboardOptions  androidx.compose.foundation.text  Button androidx.compose.material  Card androidx.compose.material  CircularProgressIndicator androidx.compose.material  Divider androidx.compose.material  Icon androidx.compose.material  
IconButton androidx.compose.material  
MaterialTheme androidx.compose.material  OutlinedTextField androidx.compose.material  Scaffold androidx.compose.material  Surface androidx.compose.material  Text androidx.compose.material  	TopAppBar androidx.compose.material  Icons androidx.compose.material.icons  Add &androidx.compose.material.icons.filled  Delete &androidx.compose.material.icons.filled  Favorite &androidx.compose.material.icons.filled  FavoriteBorder &androidx.compose.material.icons.filled  Refresh &androidx.compose.material.icons.filled  Search &androidx.compose.material.icons.filled  
Composable androidx.compose.runtime  collectAsState androidx.compose.runtime  getValue androidx.compose.runtime  mutableStateOf androidx.compose.runtime  remember androidx.compose.runtime  rememberCoroutineScope androidx.compose.runtime  setValue androidx.compose.runtime  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Color androidx.compose.ui.graphics  
FontWeight androidx.compose.ui.text.font  	ImeAction androidx.compose.ui.text.input  dp androidx.compose.ui.unit  sp androidx.compose.ui.unit  	DataStore androidx.datastore.core  preferencesDataStore androidx.datastore.preferences  Context #androidx.datastore.preferences.core  Flow #androidx.datastore.preferences.core  Int #androidx.datastore.preferences.core  Long #androidx.datastore.preferences.core  Preferences #androidx.datastore.preferences.core  String #androidx.datastore.preferences.core  edit #androidx.datastore.preferences.core  intPreferencesKey #androidx.datastore.preferences.core  longPreferencesKey #androidx.datastore.preferences.core  stringPreferencesKey #androidx.datastore.preferences.core  GlanceId androidx.glance  GlanceModifier androidx.glance  Image androidx.glance  
ImageProvider androidx.glance  
background androidx.glance  ActionParameters androidx.glance.action  	clickable androidx.glance.action  GlanceAppWidget androidx.glance.appwidget  GlanceAppWidgetManager androidx.glance.appwidget  provideContent androidx.glance.appwidget  	updateAll androidx.glance.appwidget  ActionCallback  androidx.glance.appwidget.action  actionRunCallback  androidx.glance.appwidget.action  updateAppWidgetState androidx.glance.appwidget.state  	Alignment androidx.glance.layout  Column androidx.glance.layout  Row androidx.glance.layout  Spacer androidx.glance.layout  fillMaxSize androidx.glance.layout  fillMaxWidth androidx.glance.layout  height androidx.glance.layout  padding androidx.glance.layout  size androidx.glance.layout  width androidx.glance.layout  
FontWeight androidx.glance.text  Text androidx.glance.text  	TextAlign androidx.glance.text  	TextStyle androidx.glance.text  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  Dao 
androidx.room  Database 
androidx.room  Entity 
androidx.room  EntityDeleteOrUpdateAdapter 
androidx.room  EntityInsertAdapter 
androidx.room  Insert 
androidx.room  InvalidationTracker 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  RoomDatabase 
androidx.room  RoomOpenDelegate 
androidx.room  Update 
androidx.room  	Companion  androidx.room.OnConflictStrategy  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  
WeatherDao androidx.room.RoomDatabase  
createFlow androidx.room.coroutines  AutoMigrationSpec androidx.room.migration  	Migration androidx.room.migration  	TableInfo androidx.room.util  dropFtsSyncTriggers androidx.room.util  getColumnIndexOrThrow androidx.room.util  performSuspending androidx.room.util  read &androidx.room.util.TableInfo.Companion  SQLiteConnection androidx.sqlite  SQLiteStatement androidx.sqlite  execSQL androidx.sqlite  Constraints 
androidx.work  CoroutineWorker 
androidx.work  ExistingPeriodicWorkPolicy 
androidx.work  NetworkType 
androidx.work  PeriodicWorkRequestBuilder 
androidx.work  WorkManager 
androidx.work  WorkerParameters 
androidx.work  Context androidx.work.CoroutineWorker  Result androidx.work.CoroutineWorker  WeatherRepository androidx.work.CoroutineWorker  WeatherWidgetManager androidx.work.CoroutineWorker  WorkerParameters androidx.work.CoroutineWorker  Result androidx.work.ListenableWorker  
AsyncImage coil.compose  R com.example.weatherwidget  Boolean com.example.weatherwidget.data  Context com.example.weatherwidget.data  Dao com.example.weatherwidget.data  Database com.example.weatherwidget.data  Flow com.example.weatherwidget.data  	Generated com.example.weatherwidget.data  Insert com.example.weatherwidget.data  Int com.example.weatherwidget.data  List com.example.weatherwidget.data  Long com.example.weatherwidget.data  OnConflictStrategy com.example.weatherwidget.data  Query com.example.weatherwidget.data  RoomDatabase com.example.weatherwidget.data  String com.example.weatherwidget.data  Suppress com.example.weatherwidget.data  Update com.example.weatherwidget.data  
WeatherDao com.example.weatherwidget.data  WeatherDatabase com.example.weatherwidget.data  WeatherInfo com.example.weatherwidget.data  OnConflictStrategy )com.example.weatherwidget.data.WeatherDao  Context 7com.example.weatherwidget.data.WidgetPreferencesManager  Flow 7com.example.weatherwidget.data.WidgetPreferencesManager  Int 7com.example.weatherwidget.data.WidgetPreferencesManager  Long 7com.example.weatherwidget.data.WidgetPreferencesManager  String 7com.example.weatherwidget.data.WidgetPreferencesManager  GET "com.example.weatherwidget.data.api  Long "com.example.weatherwidget.data.api  Query "com.example.weatherwidget.data.api  String "com.example.weatherwidget.data.api  WeatherApiService "com.example.weatherwidget.data.api  WeatherResponse "com.example.weatherwidget.data.api  Boolean $com.example.weatherwidget.data.model  Double $com.example.weatherwidget.data.model  Entity $com.example.weatherwidget.data.model  Int $com.example.weatherwidget.data.model  List $com.example.weatherwidget.data.model  Long $com.example.weatherwidget.data.model  Main $com.example.weatherwidget.data.model  
PrimaryKey $com.example.weatherwidget.data.model  SerializedName $com.example.weatherwidget.data.model  String $com.example.weatherwidget.data.model  Sys $com.example.weatherwidget.data.model  Weather $com.example.weatherwidget.data.model  WeatherInfo $com.example.weatherwidget.data.model  WeatherResponse $com.example.weatherwidget.data.model  Wind $com.example.weatherwidget.data.model  
toWeatherInfo $com.example.weatherwidget.data.model  Boolean )com.example.weatherwidget.data.repository  	DataStore )com.example.weatherwidget.data.repository  Flow )com.example.weatherwidget.data.repository  Int )com.example.weatherwidget.data.repository  List )com.example.weatherwidget.data.repository  Long )com.example.weatherwidget.data.repository  Preferences )com.example.weatherwidget.data.repository  Result )com.example.weatherwidget.data.repository  String )com.example.weatherwidget.data.repository  WeatherApiService )com.example.weatherwidget.data.repository  
WeatherDao )com.example.weatherwidget.data.repository  WeatherInfo )com.example.weatherwidget.data.repository  WeatherRepository )com.example.weatherwidget.data.repository  Boolean ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  	DataStore ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  Flow ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  Int ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  List ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  Long ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  Preferences ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  Result ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  String ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  WeatherApiService ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  
WeatherDao ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  WeatherInfo ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  Boolean com.example.weatherwidget.ui  
Composable com.example.weatherwidget.ui  Flow com.example.weatherwidget.ui  List com.example.weatherwidget.ui  Long com.example.weatherwidget.ui  MainActivity com.example.weatherwidget.ui  
MainViewModel com.example.weatherwidget.ui  	StateFlow com.example.weatherwidget.ui  String com.example.weatherwidget.ui  Unit com.example.weatherwidget.ui  	ViewModel com.example.weatherwidget.ui  WeatherInfo com.example.weatherwidget.ui  WeatherRepository com.example.weatherwidget.ui  Boolean  com.example.weatherwidget.widget  
Composable  com.example.weatherwidget.widget  Context  com.example.weatherwidget.widget  Flow  com.example.weatherwidget.widget  GlanceAppWidget  com.example.weatherwidget.widget  Int  com.example.weatherwidget.widget  
KoinComponent  com.example.weatherwidget.widget  List  com.example.weatherwidget.widget  Long  com.example.weatherwidget.widget  Unit  com.example.weatherwidget.widget  	ViewModel  com.example.weatherwidget.widget  WeatherInfo  com.example.weatherwidget.widget  WeatherRepository  com.example.weatherwidget.widget  WeatherWidgetManager  com.example.weatherwidget.widget  WeatherWidgetViewModel  com.example.weatherwidget.widget  Context 5com.example.weatherwidget.widget.WeatherWidgetManager  Long 5com.example.weatherwidget.widget.WeatherWidgetManager  WeatherRepository 5com.example.weatherwidget.widget.WeatherWidgetManager  Context  com.example.weatherwidget.worker  CoroutineWorker  com.example.weatherwidget.worker  
KoinComponent  com.example.weatherwidget.worker  Result  com.example.weatherwidget.worker  WeatherRepository  com.example.weatherwidget.worker  WeatherUpdateWorker  com.example.weatherwidget.worker  WeatherWidgetManager  com.example.weatherwidget.worker  WorkerParameters  com.example.weatherwidget.worker  SerializedName com.google.gson.annotations  IOException java.io  SimpleDateFormat 	java.text  Date 	java.util  Locale 	java.util  TimeUnit java.util.concurrent  	Generated javax.annotation.processing  Any kotlin  Array kotlin  Boolean kotlin  Double kotlin  	Function0 kotlin  	Function1 kotlin  Int kotlin  Lazy kotlin  Long kotlin  Result kotlin  String kotlin  Suppress kotlin  Unit kotlin  List kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  
MutableSet kotlin.collections  Set kotlin.collections  
mutableListOf kotlin.collections  mutableMapOf kotlin.collections  mutableSetOf kotlin.collections  KClass kotlin.reflect  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  launch kotlinx.coroutines  withContext kotlinx.coroutines  Flow kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  catch kotlinx.coroutines.flow  firstOrNull kotlinx.coroutines.flow  map kotlinx.coroutines.flow  
koinViewModel org.koin.androidx.compose  
KoinComponent org.koin.core.component  inject org.koin.core.component  GET retrofit2.http  Query retrofit2.http  BuildConfig com.example.weatherwidget  SampleWeatherData )com.example.weatherwidget.data.repository  WeatherUiState com.example.weatherwidget.ui  SharingStarted kotlinx.coroutines.flow  combine kotlinx.coroutines.flow  first kotlinx.coroutines.flow  stateIn kotlinx.coroutines.flow                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            