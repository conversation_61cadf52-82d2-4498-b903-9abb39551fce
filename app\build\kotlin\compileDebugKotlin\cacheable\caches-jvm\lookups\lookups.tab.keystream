  Activity android.app  Application android.app  Activity android.app.Activity  AppWidgetManager android.app.Activity  Intent android.app.Activity  
MainScreen android.app.Activity  
MaterialTheme android.app.Activity  RESULT_CANCELED android.app.Activity  	RESULT_OK android.app.Activity  Surface android.app.Activity  WidgetConfigScreen android.app.Activity  finish android.app.Activity  intent android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  	setResult android.app.Activity  
Configuration android.app.Application  Level android.app.Application  android android.app.Application  androidContext android.app.Application  
androidLogger android.app.Application  	appModule android.app.Application  databaseModule android.app.Application  listOf android.app.Application  
networkModule android.app.Application  onCreate android.app.Application  repositoryModule android.app.Application  	startKoin android.app.Application  testRepositoryModule android.app.Application  
useCaseModule android.app.Application  viewModelModule android.app.Application  widgetModule android.app.Application  workManagerFactory android.app.Application  workerModule android.app.Application  AppWidgetManager android.appwidget  EXTRA_APPWIDGET_ID "android.appwidget.AppWidgetManager  INVALID_APPWIDGET_ID "android.appwidget.AppWidgetManager  getAppWidgetIds "android.appwidget.AppWidgetManager  getInstance "android.appwidget.AppWidgetManager  AppWidgetManager #android.appwidget.AppWidgetProvider  
ComponentName #android.appwidget.AppWidgetProvider  CoroutineScope #android.appwidget.AppWidgetProvider  Dispatchers #android.appwidget.AppWidgetProvider  WeatherWidgetReceiver #android.appwidget.AppWidgetProvider  configurationUseCase #android.appwidget.AppWidgetProvider  forEach #android.appwidget.AppWidgetProvider  getValue #android.appwidget.AppWidgetProvider  inject #android.appwidget.AppWidgetProvider  isEmpty #android.appwidget.AppWidgetProvider  java #android.appwidget.AppWidgetProvider  launch #android.appwidget.AppWidgetProvider  
onDisabled #android.appwidget.AppWidgetProvider  	onEnabled #android.appwidget.AppWidgetProvider  provideDelegate #android.appwidget.AppWidgetProvider  
ComponentName android.content  Context android.content  Intent android.content  AppWidgetManager !android.content.BroadcastReceiver  
ComponentName !android.content.BroadcastReceiver  CoroutineScope !android.content.BroadcastReceiver  Dispatchers !android.content.BroadcastReceiver  WeatherWidgetReceiver !android.content.BroadcastReceiver  configurationUseCase !android.content.BroadcastReceiver  forEach !android.content.BroadcastReceiver  getValue !android.content.BroadcastReceiver  inject !android.content.BroadcastReceiver  isEmpty !android.content.BroadcastReceiver  java !android.content.BroadcastReceiver  launch !android.content.BroadcastReceiver  provideDelegate !android.content.BroadcastReceiver  Activity android.content.Context  AppWidgetManager android.content.Context  
Configuration android.content.Context  Intent android.content.Context  Level android.content.Context  
MainScreen android.content.Context  
MaterialTheme android.content.Context  Surface android.content.Context  WidgetConfigScreen android.content.Context  android android.content.Context  androidContext android.content.Context  
androidLogger android.content.Context  	appModule android.content.Context  	dataStore android.content.Context  databaseModule android.content.Context  listOf android.content.Context  
networkModule android.content.Context  repositoryModule android.content.Context  	resources android.content.Context  
setContent android.content.Context  
startActivity android.content.Context  	startKoin android.content.Context  testRepositoryModule android.content.Context  
useCaseModule android.content.Context  viewModelModule android.content.Context  widgetModule android.content.Context  workManagerFactory android.content.Context  workerModule android.content.Context  Activity android.content.ContextWrapper  AppWidgetManager android.content.ContextWrapper  
Configuration android.content.ContextWrapper  Intent android.content.ContextWrapper  Level android.content.ContextWrapper  
MainScreen android.content.ContextWrapper  
MaterialTheme android.content.ContextWrapper  Surface android.content.ContextWrapper  WidgetConfigScreen android.content.ContextWrapper  android android.content.ContextWrapper  androidContext android.content.ContextWrapper  
androidLogger android.content.ContextWrapper  	appModule android.content.ContextWrapper  databaseModule android.content.ContextWrapper  listOf android.content.ContextWrapper  
networkModule android.content.ContextWrapper  repositoryModule android.content.ContextWrapper  
setContent android.content.ContextWrapper  	startKoin android.content.ContextWrapper  testRepositoryModule android.content.ContextWrapper  
useCaseModule android.content.ContextWrapper  viewModelModule android.content.ContextWrapper  widgetModule android.content.ContextWrapper  workManagerFactory android.content.ContextWrapper  workerModule android.content.ContextWrapper  FLAG_ACTIVITY_CLEAR_TOP android.content.Intent  FLAG_ACTIVITY_NEW_TASK android.content.Intent  Intent android.content.Intent  apply android.content.Intent  extras android.content.Intent  flags android.content.Intent  putExtra android.content.Intent  
Configuration android.content.res  	Resources android.content.res  UI_MODE_NIGHT_MASK !android.content.res.Configuration  UI_MODE_NIGHT_YES !android.content.res.Configuration  uiMode !android.content.res.Configuration  
configuration android.content.res.Resources  Bundle 
android.os  getInt android.os.BaseBundle  getInt android.os.Bundle  INFO android.util.Log  Activity  android.view.ContextThemeWrapper  AppWidgetManager  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  
MainScreen  android.view.ContextThemeWrapper  
MaterialTheme  android.view.ContextThemeWrapper  Surface  android.view.ContextThemeWrapper  WidgetConfigScreen  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  Activity #androidx.activity.ComponentActivity  AppWidgetManager #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  
MainScreen #androidx.activity.ComponentActivity  
MaterialTheme #androidx.activity.ComponentActivity  Surface #androidx.activity.ComponentActivity  WidgetConfigScreen #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  Activity -androidx.activity.ComponentActivity.Companion  AppWidgetManager -androidx.activity.ComponentActivity.Companion  Intent -androidx.activity.ComponentActivity.Companion  
MainScreen -androidx.activity.ComponentActivity.Companion  
MaterialTheme -androidx.activity.ComponentActivity.Companion  Surface -androidx.activity.ComponentActivity.Companion  WidgetConfigScreen -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  
setContent androidx.activity.compose  
background androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  Boolean "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  CornerRadius "androidx.compose.foundation.layout  Float "androidx.compose.foundation.layout  FontSize "androidx.compose.foundation.layout  IconSize "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  NetworkConstraint "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  RadioButton "androidx.compose.foundation.layout  RefreshRate "androidx.compose.foundation.layout  Role "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Slider "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  Switch "androidx.compose.foundation.layout  	SwitchRow "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  TimeDisplayConfig "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  UpdateSettingsConfig "androidx.compose.foundation.layout  VisualCustomizationConfig "androidx.compose.foundation.layout  WeatherElementsConfig "androidx.compose.foundation.layout  WidgetTheme "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  forEach "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  heightIn "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  rangeTo "androidx.compose.foundation.layout  
selectable "androidx.compose.foundation.layout  selectableGroup "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  SpaceEvenly .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  Box +androidx.compose.foundation.layout.BoxScope  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  	TextAlign +androidx.compose.foundation.layout.BoxScope  WeatherContent +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  sp +androidx.compose.foundation.layout.BoxScope  Add .androidx.compose.foundation.layout.ColumnScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  
AsyncImage .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  Check .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  CitySelectionItem .androidx.compose.foundation.layout.ColumnScope  CitySelectionSection .androidx.compose.foundation.layout.ColumnScope  Close .androidx.compose.foundation.layout.ColumnScope  Cloud .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  ConfigurationSection .androidx.compose.foundation.layout.ColumnScope  CornerRadius .androidx.compose.foundation.layout.ColumnScope  DefaultWidgetConfiguration .androidx.compose.foundation.layout.ColumnScope  Delete .androidx.compose.foundation.layout.ColumnScope  Divider .androidx.compose.foundation.layout.ColumnScope  Favorite .androidx.compose.foundation.layout.ColumnScope  FavoriteBorder .androidx.compose.foundation.layout.ColumnScope  FontSize .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  IconSize .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  	ImeAction .androidx.compose.foundation.layout.ColumnScope  KeyboardActions .androidx.compose.foundation.layout.ColumnScope  KeyboardOptions .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  
LocationOn .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  NetworkConstraint .androidx.compose.foundation.layout.ColumnScope  OutlinedButton .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  Palette .androidx.compose.foundation.layout.ColumnScope  RadioButton .androidx.compose.foundation.layout.ColumnScope  Refresh .androidx.compose.foundation.layout.ColumnScope  RefreshRate .androidx.compose.foundation.layout.ColumnScope  
RestartAlt .androidx.compose.foundation.layout.ColumnScope  Role .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Search .androidx.compose.foundation.layout.ColumnScope  Slider .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Switch .androidx.compose.foundation.layout.ColumnScope  	SwitchRow .androidx.compose.foundation.layout.ColumnScope  Sync .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  
TextButton .androidx.compose.foundation.layout.ColumnScope  ThemeSection .androidx.compose.foundation.layout.ColumnScope  	TopAppBar .androidx.compose.foundation.layout.ColumnScope  Tune .androidx.compose.foundation.layout.ColumnScope  UpdateSettingsSection .androidx.compose.foundation.layout.ColumnScope  VisualCustomizationSection .androidx.compose.foundation.layout.ColumnScope  WeatherElementsSection .androidx.compose.foundation.layout.ColumnScope  WeatherIconUtils .androidx.compose.foundation.layout.ColumnScope  WeatherItem .androidx.compose.foundation.layout.ColumnScope  WeatherWidgetPreview .androidx.compose.foundation.layout.ColumnScope  WidgetTheme .androidx.compose.foundation.layout.ColumnScope  align .androidx.compose.foundation.layout.ColumnScope  
capitalize .androidx.compose.foundation.layout.ColumnScope  
cardElevation .androidx.compose.foundation.layout.ColumnScope  create .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  forEach .androidx.compose.foundation.layout.ColumnScope  formatLastUpdated .androidx.compose.foundation.layout.ColumnScope  getWeatherIconResource .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  heightIn .androidx.compose.foundation.layout.ColumnScope  
isNotBlank .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  launch .androidx.compose.foundation.layout.ColumnScope  let .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  painterResource .androidx.compose.foundation.layout.ColumnScope  rangeTo .androidx.compose.foundation.layout.ColumnScope  
selectable .androidx.compose.foundation.layout.ColumnScope  selectableGroup .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  toInt .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  Add +androidx.compose.foundation.layout.RowScope  
AsyncImage +androidx.compose.foundation.layout.RowScope  Button +androidx.compose.foundation.layout.RowScope  Check +androidx.compose.foundation.layout.RowScope  Color +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  CornerRadius +androidx.compose.foundation.layout.RowScope  Delete +androidx.compose.foundation.layout.RowScope  Favorite +androidx.compose.foundation.layout.RowScope  FavoriteBorder +androidx.compose.foundation.layout.RowScope  FontSize +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  IconSize +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  	ImeAction +androidx.compose.foundation.layout.RowScope  KeyboardActions +androidx.compose.foundation.layout.RowScope  KeyboardOptions +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  OutlinedTextField +androidx.compose.foundation.layout.RowScope  RadioButton +androidx.compose.foundation.layout.RowScope  Refresh +androidx.compose.foundation.layout.RowScope  
RestartAlt +androidx.compose.foundation.layout.RowScope  Search +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  Switch +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  
TextButton +androidx.compose.foundation.layout.RowScope  WeatherIconUtils +androidx.compose.foundation.layout.RowScope  WidgetTheme +androidx.compose.foundation.layout.RowScope  
capitalize +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  getWeatherIconResource +androidx.compose.foundation.layout.RowScope  height +androidx.compose.foundation.layout.RowScope  
isNotBlank +androidx.compose.foundation.layout.RowScope  launch +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  painterResource +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  sp +androidx.compose.foundation.layout.RowScope  toInt +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  CitySelectionItem .androidx.compose.foundation.lazy.LazyItemScope  CitySelectionSection .androidx.compose.foundation.lazy.LazyItemScope  Cloud .androidx.compose.foundation.lazy.LazyItemScope  ConfigurationSection .androidx.compose.foundation.lazy.LazyItemScope  DefaultWidgetConfiguration .androidx.compose.foundation.lazy.LazyItemScope  Divider .androidx.compose.foundation.lazy.LazyItemScope  Icon .androidx.compose.foundation.lazy.LazyItemScope  Icons .androidx.compose.foundation.lazy.LazyItemScope  
LocationOn .androidx.compose.foundation.lazy.LazyItemScope  Modifier .androidx.compose.foundation.lazy.LazyItemScope  OutlinedButton .androidx.compose.foundation.lazy.LazyItemScope  Palette .androidx.compose.foundation.lazy.LazyItemScope  
RestartAlt .androidx.compose.foundation.lazy.LazyItemScope  Spacer .androidx.compose.foundation.lazy.LazyItemScope  Sync .androidx.compose.foundation.lazy.LazyItemScope  Text .androidx.compose.foundation.lazy.LazyItemScope  ThemeSection .androidx.compose.foundation.lazy.LazyItemScope  Tune .androidx.compose.foundation.lazy.LazyItemScope  UpdateSettingsSection .androidx.compose.foundation.lazy.LazyItemScope  VisualCustomizationSection .androidx.compose.foundation.lazy.LazyItemScope  WeatherElementsSection .androidx.compose.foundation.lazy.LazyItemScope  WeatherItem .androidx.compose.foundation.lazy.LazyItemScope  create .androidx.compose.foundation.lazy.LazyItemScope  dp .androidx.compose.foundation.lazy.LazyItemScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyItemScope  height .androidx.compose.foundation.lazy.LazyItemScope  launch .androidx.compose.foundation.lazy.LazyItemScope  width .androidx.compose.foundation.lazy.LazyItemScope  CitySelectionItem .androidx.compose.foundation.lazy.LazyListScope  CitySelectionSection .androidx.compose.foundation.lazy.LazyListScope  Cloud .androidx.compose.foundation.lazy.LazyListScope  ConfigurationSection .androidx.compose.foundation.lazy.LazyListScope  DefaultWidgetConfiguration .androidx.compose.foundation.lazy.LazyListScope  Divider .androidx.compose.foundation.lazy.LazyListScope  Icon .androidx.compose.foundation.lazy.LazyListScope  Icons .androidx.compose.foundation.lazy.LazyListScope  
LocationOn .androidx.compose.foundation.lazy.LazyListScope  Modifier .androidx.compose.foundation.lazy.LazyListScope  OutlinedButton .androidx.compose.foundation.lazy.LazyListScope  Palette .androidx.compose.foundation.lazy.LazyListScope  
RestartAlt .androidx.compose.foundation.lazy.LazyListScope  Spacer .androidx.compose.foundation.lazy.LazyListScope  Sync .androidx.compose.foundation.lazy.LazyListScope  Text .androidx.compose.foundation.lazy.LazyListScope  ThemeSection .androidx.compose.foundation.lazy.LazyListScope  Tune .androidx.compose.foundation.lazy.LazyListScope  UpdateSettingsSection .androidx.compose.foundation.lazy.LazyListScope  VisualCustomizationSection .androidx.compose.foundation.lazy.LazyListScope  WeatherElementsSection .androidx.compose.foundation.lazy.LazyListScope  WeatherItem .androidx.compose.foundation.lazy.LazyListScope  create .androidx.compose.foundation.lazy.LazyListScope  dp .androidx.compose.foundation.lazy.LazyListScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyListScope  height .androidx.compose.foundation.lazy.LazyListScope  item .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  launch .androidx.compose.foundation.lazy.LazyListScope  width .androidx.compose.foundation.lazy.LazyListScope  
selectable %androidx.compose.foundation.selection  selectableGroup %androidx.compose.foundation.selection  RoundedCornerShape !androidx.compose.foundation.shape  KeyboardActionScope  androidx.compose.foundation.text  KeyboardActions  androidx.compose.foundation.text  KeyboardOptions  androidx.compose.foundation.text  
isNotBlank 4androidx.compose.foundation.text.KeyboardActionScope  launch 4androidx.compose.foundation.text.KeyboardActionScope  Button androidx.compose.material  Card androidx.compose.material  CircularProgressIndicator androidx.compose.material  Colors androidx.compose.material  Divider androidx.compose.material  Icon androidx.compose.material  
IconButton androidx.compose.material  
MaterialTheme androidx.compose.material  OutlinedTextField androidx.compose.material  Scaffold androidx.compose.material  Surface androidx.compose.material  Text androidx.compose.material  	TopAppBar androidx.compose.material  
background  androidx.compose.material.Colors  colors 'androidx.compose.material.MaterialTheme  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  Add ,androidx.compose.material.icons.Icons.Filled  Check ,androidx.compose.material.icons.Icons.Filled  Close ,androidx.compose.material.icons.Icons.Filled  Cloud ,androidx.compose.material.icons.Icons.Filled  Delete ,androidx.compose.material.icons.Icons.Filled  Favorite ,androidx.compose.material.icons.Icons.Filled  FavoriteBorder ,androidx.compose.material.icons.Icons.Filled  
LocationOn ,androidx.compose.material.icons.Icons.Filled  Palette ,androidx.compose.material.icons.Icons.Filled  Refresh ,androidx.compose.material.icons.Icons.Filled  
RestartAlt ,androidx.compose.material.icons.Icons.Filled  Search ,androidx.compose.material.icons.Icons.Filled  Sync ,androidx.compose.material.icons.Icons.Filled  Tune ,androidx.compose.material.icons.Icons.Filled  Add &androidx.compose.material.icons.filled  Check &androidx.compose.material.icons.filled  Close &androidx.compose.material.icons.filled  Cloud &androidx.compose.material.icons.filled  Delete &androidx.compose.material.icons.filled  Favorite &androidx.compose.material.icons.filled  FavoriteBorder &androidx.compose.material.icons.filled  
LocationOn &androidx.compose.material.icons.filled  Palette &androidx.compose.material.icons.filled  Refresh &androidx.compose.material.icons.filled  
RestartAlt &androidx.compose.material.icons.filled  Search &androidx.compose.material.icons.filled  Sync &androidx.compose.material.icons.filled  Tune &androidx.compose.material.icons.filled  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  Boolean androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  
CardElevation androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  CornerRadius androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  Float androidx.compose.material3  FontSize androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  IconSize androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  NetworkConstraint androidx.compose.material3  OutlinedButton androidx.compose.material3  RadioButton androidx.compose.material3  RefreshRate androidx.compose.material3  Role androidx.compose.material3  Row androidx.compose.material3  Slider androidx.compose.material3  Spacer androidx.compose.material3  String androidx.compose.material3  Surface androidx.compose.material3  Switch androidx.compose.material3  	SwitchRow androidx.compose.material3  Text androidx.compose.material3  
TextButton androidx.compose.material3  TimeDisplayConfig androidx.compose.material3  	TopAppBar androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  UpdateSettingsConfig androidx.compose.material3  VisualCustomizationConfig androidx.compose.material3  WeatherElementsConfig androidx.compose.material3  WidgetTheme androidx.compose.material3  fillMaxWidth androidx.compose.material3  forEach androidx.compose.material3  height androidx.compose.material3  padding androidx.compose.material3  rangeTo androidx.compose.material3  
selectable androidx.compose.material3  selectableGroup androidx.compose.material3  
cardColors 'androidx.compose.material3.CardDefaults  
cardElevation 'androidx.compose.material3.CardDefaults  
background &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  primaryContainer &androidx.compose.material3.ColorScheme  surface &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  	bodyLarge %androidx.compose.material3.Typography  
bodyMedium %androidx.compose.material3.Typography  	bodySmall %androidx.compose.material3.Typography  titleMedium %androidx.compose.material3.Typography  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  Boolean androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  CornerRadius androidx.compose.runtime  Float androidx.compose.runtime  FontSize androidx.compose.runtime  IconSize androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  NetworkConstraint androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  RadioButton androidx.compose.runtime  RefreshRate androidx.compose.runtime  Role androidx.compose.runtime  Row androidx.compose.runtime  Slider androidx.compose.runtime  Spacer androidx.compose.runtime  State androidx.compose.runtime  String androidx.compose.runtime  Switch androidx.compose.runtime  	SwitchRow androidx.compose.runtime  Text androidx.compose.runtime  TimeDisplayConfig androidx.compose.runtime  Unit androidx.compose.runtime  UpdateSettingsConfig androidx.compose.runtime  VisualCustomizationConfig androidx.compose.runtime  WeatherElementsConfig androidx.compose.runtime  WidgetTheme androidx.compose.runtime  collectAsState androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  forEach androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  rangeTo androidx.compose.runtime  remember androidx.compose.runtime  rememberCoroutineScope androidx.compose.runtime  
selectable androidx.compose.runtime  selectableGroup androidx.compose.runtime  setValue androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  invoke 5androidx.compose.runtime.internal.ComposableFunction0  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  End androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  End 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  align androidx.compose.ui.Modifier  alpha androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  	clickable androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  heightIn androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  
selectable androidx.compose.ui.Modifier  selectableGroup androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  align &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  heightIn &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  selectableGroup &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  alpha androidx.compose.ui.draw  clip androidx.compose.ui.draw  Color androidx.compose.ui.graphics  	Companion "androidx.compose.ui.graphics.Color  Gray "androidx.compose.ui.graphics.Color  Red "androidx.compose.ui.graphics.Color  Gray ,androidx.compose.ui.graphics.Color.Companion  Red ,androidx.compose.ui.graphics.Color.Companion  Painter $androidx.compose.ui.graphics.painter  ImageVector #androidx.compose.ui.graphics.vector  LocalContext androidx.compose.ui.platform  painterResource androidx.compose.ui.res  Role androidx.compose.ui.semantics  	Companion "androidx.compose.ui.semantics.Role  RadioButton "androidx.compose.ui.semantics.Role  RadioButton ,androidx.compose.ui.semantics.Role.Companion  	TextStyle androidx.compose.ui.text  
FontWeight androidx.compose.ui.text.font  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  	ImeAction androidx.compose.ui.text.input  	Companion (androidx.compose.ui.text.input.ImeAction  Search (androidx.compose.ui.text.input.ImeAction  Search 2androidx.compose.ui.text.input.ImeAction.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Activity #androidx.core.app.ComponentActivity  AppWidgetManager #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  
MainScreen #androidx.core.app.ComponentActivity  
MaterialTheme #androidx.core.app.ComponentActivity  Surface #androidx.core.app.ComponentActivity  WidgetConfigScreen #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  	DataStore androidx.datastore.core  data !androidx.datastore.core.DataStore  edit !androidx.datastore.core.DataStore  preferencesDataStore androidx.datastore.preferences  API_KEY #androidx.datastore.preferences.core  Context #androidx.datastore.preferences.core  DEFAULT_CITY_ID #androidx.datastore.preferences.core  DEFAULT_UPDATE_FREQUENCY_HOURS #androidx.datastore.preferences.core  	DataStore #androidx.datastore.preferences.core  DefaultWidgetConfiguration #androidx.datastore.preferences.core  	Exception #androidx.datastore.preferences.core  Flow #androidx.datastore.preferences.core  IOException #androidx.datastore.preferences.core  Int #androidx.datastore.preferences.core  Json #androidx.datastore.preferences.core  LAST_UPDATE_TIME #androidx.datastore.preferences.core  Long #androidx.datastore.preferences.core  Map #androidx.datastore.preferences.core  MutablePreferences #androidx.datastore.preferences.core  Preferences #androidx.datastore.preferences.core  SELECTED_CITY_ID #androidx.datastore.preferences.core  String #androidx.datastore.preferences.core  UPDATE_FREQUENCY_HOURS #androidx.datastore.preferences.core  WidgetConfiguration #androidx.datastore.preferences.core  catch #androidx.datastore.preferences.core  
component1 #androidx.datastore.preferences.core  
component2 #androidx.datastore.preferences.core  create #androidx.datastore.preferences.core  edit #androidx.datastore.preferences.core  emptyPreferences #androidx.datastore.preferences.core  forEach #androidx.datastore.preferences.core  intPreferencesKey #androidx.datastore.preferences.core  json #androidx.datastore.preferences.core  longPreferencesKey #androidx.datastore.preferences.core  map #androidx.datastore.preferences.core  mutableMapOf #androidx.datastore.preferences.core  preferencesDataStore #androidx.datastore.preferences.core  provideDelegate #androidx.datastore.preferences.core  removePrefix #androidx.datastore.preferences.core  set #androidx.datastore.preferences.core  
startsWith #androidx.datastore.preferences.core  stringPreferencesKey #androidx.datastore.preferences.core  toInt #androidx.datastore.preferences.core  widgetConfigKey #androidx.datastore.preferences.core  remove 6androidx.datastore.preferences.core.MutablePreferences  set 6androidx.datastore.preferences.core.MutablePreferences  Key /androidx.datastore.preferences.core.Preferences  asMap /androidx.datastore.preferences.core.Preferences  get /androidx.datastore.preferences.core.Preferences  name 3androidx.datastore.preferences.core.Preferences.Key  GlanceId androidx.glance  GlanceModifier androidx.glance  Image androidx.glance  
ImageProvider androidx.glance  
background androidx.glance  toString androidx.glance.GlanceId  	Companion androidx.glance.GlanceModifier  
background androidx.glance.GlanceModifier  cornerRadius androidx.glance.GlanceModifier  glanceFillMaxSize androidx.glance.GlanceModifier  glanceFillMaxWidth androidx.glance.GlanceModifier  glanceHeight androidx.glance.GlanceModifier  
glancePadding androidx.glance.GlanceModifier  
glanceSize androidx.glance.GlanceModifier  glanceWidth androidx.glance.GlanceModifier  glanceFillMaxSize (androidx.glance.GlanceModifier.Companion  glanceFillMaxWidth (androidx.glance.GlanceModifier.Companion  glanceHeight (androidx.glance.GlanceModifier.Companion  
glanceSize (androidx.glance.GlanceModifier.Companion  glanceWidth (androidx.glance.GlanceModifier.Companion  ActionParameters androidx.glance.action  	clickable androidx.glance.action  GlanceAppWidget androidx.glance.appwidget  GlanceAppWidgetManager androidx.glance.appwidget  GlanceAppWidgetReceiver androidx.glance.appwidget  cornerRadius androidx.glance.appwidget  provideContent androidx.glance.appwidget  	updateAll androidx.glance.appwidget  update )androidx.glance.appwidget.GlanceAppWidget  getGlanceIds 0androidx.glance.appwidget.GlanceAppWidgetManager  AppWidgetManager 1androidx.glance.appwidget.GlanceAppWidgetReceiver  	Companion 1androidx.glance.appwidget.GlanceAppWidgetReceiver  
ComponentName 1androidx.glance.appwidget.GlanceAppWidgetReceiver  Context 1androidx.glance.appwidget.GlanceAppWidgetReceiver  CoroutineScope 1androidx.glance.appwidget.GlanceAppWidgetReceiver  Dispatchers 1androidx.glance.appwidget.GlanceAppWidgetReceiver  GlanceAppWidget 1androidx.glance.appwidget.GlanceAppWidgetReceiver  IntArray 1androidx.glance.appwidget.GlanceAppWidgetReceiver  WeatherWidgetManager 1androidx.glance.appwidget.GlanceAppWidgetReceiver  WeatherWidgetReceiver 1androidx.glance.appwidget.GlanceAppWidgetReceiver  WidgetConfigurationUseCase 1androidx.glance.appwidget.GlanceAppWidgetReceiver  configurationUseCase 1androidx.glance.appwidget.GlanceAppWidgetReceiver  forEach 1androidx.glance.appwidget.GlanceAppWidgetReceiver  getValue 1androidx.glance.appwidget.GlanceAppWidgetReceiver  inject 1androidx.glance.appwidget.GlanceAppWidgetReceiver  isEmpty 1androidx.glance.appwidget.GlanceAppWidgetReceiver  java 1androidx.glance.appwidget.GlanceAppWidgetReceiver  launch 1androidx.glance.appwidget.GlanceAppWidgetReceiver  	onDeleted 1androidx.glance.appwidget.GlanceAppWidgetReceiver  
onDisabled 1androidx.glance.appwidget.GlanceAppWidgetReceiver  	onEnabled 1androidx.glance.appwidget.GlanceAppWidgetReceiver  onUpdate 1androidx.glance.appwidget.GlanceAppWidgetReceiver  provideDelegate 1androidx.glance.appwidget.GlanceAppWidgetReceiver  AppWidgetManager ;androidx.glance.appwidget.GlanceAppWidgetReceiver.Companion  
ComponentName ;androidx.glance.appwidget.GlanceAppWidgetReceiver.Companion  CoroutineScope ;androidx.glance.appwidget.GlanceAppWidgetReceiver.Companion  Dispatchers ;androidx.glance.appwidget.GlanceAppWidgetReceiver.Companion  WeatherWidgetReceiver ;androidx.glance.appwidget.GlanceAppWidgetReceiver.Companion  configurationUseCase ;androidx.glance.appwidget.GlanceAppWidgetReceiver.Companion  forEach ;androidx.glance.appwidget.GlanceAppWidgetReceiver.Companion  getValue ;androidx.glance.appwidget.GlanceAppWidgetReceiver.Companion  inject ;androidx.glance.appwidget.GlanceAppWidgetReceiver.Companion  isEmpty ;androidx.glance.appwidget.GlanceAppWidgetReceiver.Companion  java ;androidx.glance.appwidget.GlanceAppWidgetReceiver.Companion  launch ;androidx.glance.appwidget.GlanceAppWidgetReceiver.Companion  provideDelegate ;androidx.glance.appwidget.GlanceAppWidgetReceiver.Companion  ActionCallback  androidx.glance.appwidget.action  actionRunCallback  androidx.glance.appwidget.action  updateAppWidgetState androidx.glance.appwidget.state  	Alignment androidx.glance.layout  Column androidx.glance.layout  ColumnScope androidx.glance.layout  Row androidx.glance.layout  RowScope androidx.glance.layout  Spacer androidx.glance.layout  fillMaxSize androidx.glance.layout  fillMaxWidth androidx.glance.layout  height androidx.glance.layout  padding androidx.glance.layout  size androidx.glance.layout  width androidx.glance.layout  CenterHorizontally  androidx.glance.layout.Alignment  CenterVertically  androidx.glance.layout.Alignment  	Companion  androidx.glance.layout.Alignment  
Horizontal  androidx.glance.layout.Alignment  Vertical  androidx.glance.layout.Alignment  CenterHorizontally *androidx.glance.layout.Alignment.Companion  CenterVertically *androidx.glance.layout.Alignment.Companion  
GlanceText "androidx.glance.layout.ColumnScope  GlanceTextAlign "androidx.glance.layout.ColumnScope  GlanceTextStyle "androidx.glance.layout.ColumnScope  GlanceWeatherContent "androidx.glance.layout.ColumnScope  sp "androidx.glance.layout.ColumnScope  GlanceFontWeight androidx.glance.layout.RowScope  GlanceModifier androidx.glance.layout.RowScope  GlanceSpacer androidx.glance.layout.RowScope  
GlanceText androidx.glance.layout.RowScope  GlanceTextStyle androidx.glance.layout.RowScope  Image androidx.glance.layout.RowScope  
ImageProvider androidx.glance.layout.RowScope  WeatherIconUtils androidx.glance.layout.RowScope  dp androidx.glance.layout.RowScope  getWeatherIconResource androidx.glance.layout.RowScope  
glanceSize androidx.glance.layout.RowScope  glanceWidth androidx.glance.layout.RowScope  sp androidx.glance.layout.RowScope  toInt androidx.glance.layout.RowScope  
FontWeight androidx.glance.text  Text androidx.glance.text  	TextAlign androidx.glance.text  	TextStyle androidx.glance.text  Bold androidx.glance.text.FontWeight  	Companion androidx.glance.text.FontWeight  Bold )androidx.glance.text.FontWeight.Companion  Center androidx.glance.text.TextAlign  	Companion androidx.glance.text.TextAlign  Center (androidx.glance.text.TextAlign.Companion  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  Dao 
androidx.room  Database 
androidx.room  Entity 
androidx.room  EntityDeleteOrUpdateAdapter 
androidx.room  EntityInsertAdapter 
androidx.room  Insert 
androidx.room  InvalidationTracker 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  RoomOpenDelegate 
androidx.room  Update 
androidx.room  handle )androidx.room.EntityDeleteOrUpdateAdapter  insert !androidx.room.EntityInsertAdapter  	Companion  androidx.room.OnConflictStrategy  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  databaseBuilder androidx.room.Room  AutoMigrationSpec androidx.room.RoomDatabase  Builder androidx.room.RoomDatabase  	Companion androidx.room.RoomDatabase  InvalidationTracker androidx.room.RoomDatabase  KClass androidx.room.RoomDatabase  Lazy androidx.room.RoomDatabase  List androidx.room.RoomDatabase  Map androidx.room.RoomDatabase  	Migration androidx.room.RoomDatabase  MutableList androidx.room.RoomDatabase  
MutableMap androidx.room.RoomDatabase  
MutableSet androidx.room.RoomDatabase  RoomOpenDelegate androidx.room.RoomDatabase  SQLiteConnection androidx.room.RoomDatabase  Set androidx.room.RoomDatabase  String androidx.room.RoomDatabase  	TableInfo androidx.room.RoomDatabase  
WeatherDao androidx.room.RoomDatabase  WeatherDao_Impl androidx.room.RoomDatabase  dropFtsSyncTriggers androidx.room.RoomDatabase  execSQL androidx.room.RoomDatabase  getRequiredConverters androidx.room.RoomDatabase  internalInitInvalidationTracker androidx.room.RoomDatabase  lazy androidx.room.RoomDatabase  
mutableListOf androidx.room.RoomDatabase  mutableMapOf androidx.room.RoomDatabase  mutableSetOf androidx.room.RoomDatabase  performClear androidx.room.RoomDatabase  read androidx.room.RoomDatabase  
trimMargin androidx.room.RoomDatabase  build "androidx.room.RoomDatabase.Builder  InvalidationTracker $androidx.room.RoomDatabase.Companion  RoomOpenDelegate $androidx.room.RoomDatabase.Companion  	TableInfo $androidx.room.RoomDatabase.Companion  
WeatherDao $androidx.room.RoomDatabase.Companion  WeatherDao_Impl $androidx.room.RoomDatabase.Companion  dropFtsSyncTriggers $androidx.room.RoomDatabase.Companion  execSQL $androidx.room.RoomDatabase.Companion  getRequiredConverters $androidx.room.RoomDatabase.Companion  internalInitInvalidationTracker $androidx.room.RoomDatabase.Companion  lazy $androidx.room.RoomDatabase.Companion  
mutableListOf $androidx.room.RoomDatabase.Companion  mutableMapOf $androidx.room.RoomDatabase.Companion  mutableSetOf $androidx.room.RoomDatabase.Companion  read $androidx.room.RoomDatabase.Companion  
trimMargin $androidx.room.RoomDatabase.Companion  ValidationResult +androidx.room.RoomDatabase.RoomOpenDelegate  Column $androidx.room.RoomDatabase.TableInfo  
ForeignKey $androidx.room.RoomDatabase.TableInfo  Index $androidx.room.RoomDatabase.TableInfo  
MutableMap androidx.room.RoomOpenDelegate  
MutableSet androidx.room.RoomOpenDelegate  RoomOpenDelegate androidx.room.RoomOpenDelegate  SQLiteConnection androidx.room.RoomOpenDelegate  String androidx.room.RoomOpenDelegate  	TableInfo androidx.room.RoomOpenDelegate  ValidationResult androidx.room.RoomOpenDelegate  dropFtsSyncTriggers androidx.room.RoomOpenDelegate  execSQL androidx.room.RoomOpenDelegate  internalInitInvalidationTracker androidx.room.RoomOpenDelegate  mutableMapOf androidx.room.RoomOpenDelegate  mutableSetOf androidx.room.RoomOpenDelegate  read androidx.room.RoomOpenDelegate  
trimMargin androidx.room.RoomOpenDelegate  ValidationResult /androidx.room.RoomOpenDelegate.RoomOpenDelegate  Column (androidx.room.RoomOpenDelegate.TableInfo  
ForeignKey (androidx.room.RoomOpenDelegate.TableInfo  Index (androidx.room.RoomOpenDelegate.TableInfo  
createFlow androidx.room.coroutines  AutoMigrationSpec androidx.room.migration  	Migration androidx.room.migration  	TableInfo androidx.room.util  dropFtsSyncTriggers androidx.room.util  getColumnIndexOrThrow androidx.room.util  performSuspending androidx.room.util  CREATED_FROM_ENTITY androidx.room.util.TableInfo  Column androidx.room.util.TableInfo  	Companion androidx.room.util.TableInfo  
ForeignKey androidx.room.util.TableInfo  Index androidx.room.util.TableInfo  equals androidx.room.util.TableInfo  CREATED_FROM_ENTITY &androidx.room.util.TableInfo.Companion  read &androidx.room.util.TableInfo.Companion  SQLiteConnection androidx.sqlite  SQLiteStatement androidx.sqlite  execSQL androidx.sqlite  execSQL  androidx.sqlite.SQLiteConnection  prepare  androidx.sqlite.SQLiteConnection  
bindDouble androidx.sqlite.SQLiteStatement  bindLong androidx.sqlite.SQLiteStatement  bindText androidx.sqlite.SQLiteStatement  close androidx.sqlite.SQLiteStatement  	getDouble androidx.sqlite.SQLiteStatement  getLong androidx.sqlite.SQLiteStatement  getText androidx.sqlite.SQLiteStatement  step androidx.sqlite.SQLiteStatement  
Configuration 
androidx.work  Constraints 
androidx.work  CoroutineWorker 
androidx.work  ExistingPeriodicWorkPolicy 
androidx.work  ListenableWorker 
androidx.work  NetworkType 
androidx.work  	Operation 
androidx.work  PeriodicWorkRequest 
androidx.work  PeriodicWorkRequestBuilder 
androidx.work  WorkManager 
androidx.work  
WorkerFactory 
androidx.work  WorkerParameters 
androidx.work  Builder androidx.work.Configuration  	Companion androidx.work.Configuration  Provider androidx.work.Configuration  build #androidx.work.Configuration.Builder  setMinimumLoggingLevel #androidx.work.Configuration.Builder  Builder androidx.work.Constraints  	Companion androidx.work.Constraints  build !androidx.work.Constraints.Builder  setRequiredNetworkType !androidx.work.Constraints.Builder  Context androidx.work.CoroutineWorker  Dispatchers androidx.work.CoroutineWorker  	Exception androidx.work.CoroutineWorker  GlanceAppWidgetManager androidx.work.CoroutineWorker  Result androidx.work.CoroutineWorker  WeatherRepository androidx.work.CoroutineWorker  WeatherWidgetManager androidx.work.CoroutineWorker  WidgetConfigurationUseCase androidx.work.CoroutineWorker  WorkerParameters androidx.work.CoroutineWorker  applicationContext androidx.work.CoroutineWorker  configurationUseCase androidx.work.CoroutineWorker  first androidx.work.CoroutineWorker  getValue androidx.work.CoroutineWorker  inject androidx.work.CoroutineWorker  	javaClass androidx.work.CoroutineWorker  provideDelegate androidx.work.CoroutineWorker  updateAppWidgetState androidx.work.CoroutineWorker  weatherRepository androidx.work.CoroutineWorker  
widgetManager androidx.work.CoroutineWorker  withContext androidx.work.CoroutineWorker  UPDATE (androidx.work.ExistingPeriodicWorkPolicy  Dispatchers androidx.work.ListenableWorker  	Exception androidx.work.ListenableWorker  GlanceAppWidgetManager androidx.work.ListenableWorker  Result androidx.work.ListenableWorker  applicationContext androidx.work.ListenableWorker  configurationUseCase androidx.work.ListenableWorker  first androidx.work.ListenableWorker  getValue androidx.work.ListenableWorker  inject androidx.work.ListenableWorker  	javaClass androidx.work.ListenableWorker  provideDelegate androidx.work.ListenableWorker  updateAppWidgetState androidx.work.ListenableWorker  weatherRepository androidx.work.ListenableWorker  
widgetManager androidx.work.ListenableWorker  withContext androidx.work.ListenableWorker  failure %androidx.work.ListenableWorker.Result  retry %androidx.work.ListenableWorker.Result  success %androidx.work.ListenableWorker.Result  	CONNECTED androidx.work.NetworkType  Builder !androidx.work.PeriodicWorkRequest  build )androidx.work.PeriodicWorkRequest.Builder  setConstraints )androidx.work.PeriodicWorkRequest.Builder  	Companion androidx.work.WorkManager  cancelUniqueWork androidx.work.WorkManager  enqueueUniquePeriodicWork androidx.work.WorkManager  getInstance androidx.work.WorkManager  getInstance #androidx.work.WorkManager.Companion  
AsyncImage coil.compose  Application com.example.weatherwidget  BuildConfig com.example.weatherwidget  
Configuration com.example.weatherwidget  Level com.example.weatherwidget  R com.example.weatherwidget  
WeatherApp com.example.weatherwidget  android com.example.weatherwidget  	appModule com.example.weatherwidget  databaseModule com.example.weatherwidget  listOf com.example.weatherwidget  
networkModule com.example.weatherwidget  repositoryModule com.example.weatherwidget  	startKoin com.example.weatherwidget  testRepositoryModule com.example.weatherwidget  
useCaseModule com.example.weatherwidget  viewModelModule com.example.weatherwidget  widgetModule com.example.weatherwidget  workerModule com.example.weatherwidget  DEBUG %com.example.weatherwidget.BuildConfig  Provider 'com.example.weatherwidget.Configuration  ic_clear_day $com.example.weatherwidget.R.drawable  ic_clear_night $com.example.weatherwidget.R.drawable  	ic_cloudy $com.example.weatherwidget.R.drawable  ic_fog $com.example.weatherwidget.R.drawable  ic_partly_cloudy_day $com.example.weatherwidget.R.drawable  ic_partly_cloudy_night $com.example.weatherwidget.R.drawable  ic_rain $com.example.weatherwidget.R.drawable  ic_rain_day $com.example.weatherwidget.R.drawable  
ic_rain_night $com.example.weatherwidget.R.drawable  ic_snow $com.example.weatherwidget.R.drawable  ic_thunderstorm $com.example.weatherwidget.R.drawable  
Configuration $com.example.weatherwidget.WeatherApp  Level $com.example.weatherwidget.WeatherApp  android $com.example.weatherwidget.WeatherApp  androidContext $com.example.weatherwidget.WeatherApp  
androidLogger $com.example.weatherwidget.WeatherApp  	appModule $com.example.weatherwidget.WeatherApp  databaseModule $com.example.weatherwidget.WeatherApp  listOf $com.example.weatherwidget.WeatherApp  
networkModule $com.example.weatherwidget.WeatherApp  repositoryModule $com.example.weatherwidget.WeatherApp  	startKoin $com.example.weatherwidget.WeatherApp  testRepositoryModule $com.example.weatherwidget.WeatherApp  
useCaseModule $com.example.weatherwidget.WeatherApp  viewModelModule $com.example.weatherwidget.WeatherApp  widgetModule $com.example.weatherwidget.WeatherApp  workManagerFactory $com.example.weatherwidget.WeatherApp  workerModule $com.example.weatherwidget.WeatherApp  API_KEY com.example.weatherwidget.data  AutoMigrationSpec com.example.weatherwidget.data  Boolean com.example.weatherwidget.data  Context com.example.weatherwidget.data  DEFAULT_CITY_ID com.example.weatherwidget.data  DEFAULT_UPDATE_FREQUENCY_HOURS com.example.weatherwidget.data  Dao com.example.weatherwidget.data  	DataStore com.example.weatherwidget.data  Database com.example.weatherwidget.data  DefaultWidgetConfiguration com.example.weatherwidget.data  Double com.example.weatherwidget.data  EntityDeleteOrUpdateAdapter com.example.weatherwidget.data  EntityInsertAdapter com.example.weatherwidget.data  	Exception com.example.weatherwidget.data  Flow com.example.weatherwidget.data  	Generated com.example.weatherwidget.data  IOException com.example.weatherwidget.data  Insert com.example.weatherwidget.data  Int com.example.weatherwidget.data  InvalidationTracker com.example.weatherwidget.data  Json com.example.weatherwidget.data  KClass com.example.weatherwidget.data  LAST_UPDATE_TIME com.example.weatherwidget.data  Lazy com.example.weatherwidget.data  List com.example.weatherwidget.data  Long com.example.weatherwidget.data  Map com.example.weatherwidget.data  	Migration com.example.weatherwidget.data  MutableList com.example.weatherwidget.data  
MutableMap com.example.weatherwidget.data  
MutableSet com.example.weatherwidget.data  OnConflictStrategy com.example.weatherwidget.data  Preferences com.example.weatherwidget.data  Query com.example.weatherwidget.data  RoomDatabase com.example.weatherwidget.data  RoomOpenDelegate com.example.weatherwidget.data  SELECTED_CITY_ID com.example.weatherwidget.data  SQLiteConnection com.example.weatherwidget.data  SQLiteStatement com.example.weatherwidget.data  Set com.example.weatherwidget.data  String com.example.weatherwidget.data  Suppress com.example.weatherwidget.data  	TableInfo com.example.weatherwidget.data  UPDATE_FREQUENCY_HOURS com.example.weatherwidget.data  Unit com.example.weatherwidget.data  Update com.example.weatherwidget.data  
WeatherDao com.example.weatherwidget.data  WeatherDao_Impl com.example.weatherwidget.data  WeatherDatabase com.example.weatherwidget.data  WeatherDatabase_Impl com.example.weatherwidget.data  WeatherInfo com.example.weatherwidget.data  WidgetConfiguration com.example.weatherwidget.data  WidgetPreferencesManager com.example.weatherwidget.data  arrayOf com.example.weatherwidget.data  catch com.example.weatherwidget.data  
component1 com.example.weatherwidget.data  
component2 com.example.weatherwidget.data  create com.example.weatherwidget.data  
createFlow com.example.weatherwidget.data  dropFtsSyncTriggers com.example.weatherwidget.data  edit com.example.weatherwidget.data  	emptyList com.example.weatherwidget.data  emptyPreferences com.example.weatherwidget.data  execSQL com.example.weatherwidget.data  forEach com.example.weatherwidget.data  getColumnIndexOrThrow com.example.weatherwidget.data  getRequiredConverters com.example.weatherwidget.data  intPreferencesKey com.example.weatherwidget.data  internalInitInvalidationTracker com.example.weatherwidget.data  json com.example.weatherwidget.data  lazy com.example.weatherwidget.data  longPreferencesKey com.example.weatherwidget.data  map com.example.weatherwidget.data  
mutableListOf com.example.weatherwidget.data  mutableMapOf com.example.weatherwidget.data  mutableSetOf com.example.weatherwidget.data  performSuspending com.example.weatherwidget.data  preferencesDataStore com.example.weatherwidget.data  provideDelegate com.example.weatherwidget.data  read com.example.weatherwidget.data  removePrefix com.example.weatherwidget.data  set com.example.weatherwidget.data  
startsWith com.example.weatherwidget.data  stringPreferencesKey com.example.weatherwidget.data  toInt com.example.weatherwidget.data  
trimMargin com.example.weatherwidget.data  widgetConfigKey com.example.weatherwidget.data  ValidationResult /com.example.weatherwidget.data.RoomOpenDelegate  Column (com.example.weatherwidget.data.TableInfo  
ForeignKey (com.example.weatherwidget.data.TableInfo  Index (com.example.weatherwidget.data.TableInfo  OnConflictStrategy )com.example.weatherwidget.data.WeatherDao  deleteWeatherInfo )com.example.weatherwidget.data.WeatherDao  getAllWeatherInfo )com.example.weatherwidget.data.WeatherDao  getFavoriteWeatherInfo )com.example.weatherwidget.data.WeatherDao  getWeatherInfoById )com.example.weatherwidget.data.WeatherDao  getWeatherInfoByIdAsFlow )com.example.weatherwidget.data.WeatherDao  insertWeatherInfo )com.example.weatherwidget.data.WeatherDao  updateFavoriteStatus )com.example.weatherwidget.data.WeatherDao  Boolean .com.example.weatherwidget.data.WeatherDao_Impl  	Companion .com.example.weatherwidget.data.WeatherDao_Impl  Double .com.example.weatherwidget.data.WeatherDao_Impl  EntityDeleteOrUpdateAdapter .com.example.weatherwidget.data.WeatherDao_Impl  EntityInsertAdapter .com.example.weatherwidget.data.WeatherDao_Impl  Flow .com.example.weatherwidget.data.WeatherDao_Impl  Int .com.example.weatherwidget.data.WeatherDao_Impl  KClass .com.example.weatherwidget.data.WeatherDao_Impl  List .com.example.weatherwidget.data.WeatherDao_Impl  Long .com.example.weatherwidget.data.WeatherDao_Impl  MutableList .com.example.weatherwidget.data.WeatherDao_Impl  RoomDatabase .com.example.weatherwidget.data.WeatherDao_Impl  SQLiteStatement .com.example.weatherwidget.data.WeatherDao_Impl  String .com.example.weatherwidget.data.WeatherDao_Impl  Unit .com.example.weatherwidget.data.WeatherDao_Impl  WeatherInfo .com.example.weatherwidget.data.WeatherDao_Impl  __db .com.example.weatherwidget.data.WeatherDao_Impl  __insertAdapterOfWeatherInfo .com.example.weatherwidget.data.WeatherDao_Impl  __updateAdapterOfWeatherInfo .com.example.weatherwidget.data.WeatherDao_Impl  arrayOf .com.example.weatherwidget.data.WeatherDao_Impl  
createFlow .com.example.weatherwidget.data.WeatherDao_Impl  	emptyList .com.example.weatherwidget.data.WeatherDao_Impl  getColumnIndexOrThrow .com.example.weatherwidget.data.WeatherDao_Impl  getRequiredConverters .com.example.weatherwidget.data.WeatherDao_Impl  
mutableListOf .com.example.weatherwidget.data.WeatherDao_Impl  performSuspending .com.example.weatherwidget.data.WeatherDao_Impl  WeatherInfo 8com.example.weatherwidget.data.WeatherDao_Impl.Companion  arrayOf 8com.example.weatherwidget.data.WeatherDao_Impl.Companion  
createFlow 8com.example.weatherwidget.data.WeatherDao_Impl.Companion  	emptyList 8com.example.weatherwidget.data.WeatherDao_Impl.Companion  getColumnIndexOrThrow 8com.example.weatherwidget.data.WeatherDao_Impl.Companion  getRequiredConverters 8com.example.weatherwidget.data.WeatherDao_Impl.Companion  
mutableListOf 8com.example.weatherwidget.data.WeatherDao_Impl.Companion  performSuspending 8com.example.weatherwidget.data.WeatherDao_Impl.Companion  performClear .com.example.weatherwidget.data.WeatherDatabase  
weatherDao .com.example.weatherwidget.data.WeatherDatabase  InvalidationTracker 3com.example.weatherwidget.data.WeatherDatabase_Impl  RoomOpenDelegate 3com.example.weatherwidget.data.WeatherDatabase_Impl  	TableInfo 3com.example.weatherwidget.data.WeatherDatabase_Impl  
WeatherDao 3com.example.weatherwidget.data.WeatherDatabase_Impl  WeatherDao_Impl 3com.example.weatherwidget.data.WeatherDatabase_Impl  _weatherDao 3com.example.weatherwidget.data.WeatherDatabase_Impl  dropFtsSyncTriggers 3com.example.weatherwidget.data.WeatherDatabase_Impl  execSQL 3com.example.weatherwidget.data.WeatherDatabase_Impl  getRequiredConverters 3com.example.weatherwidget.data.WeatherDatabase_Impl  internalInitInvalidationTracker 3com.example.weatherwidget.data.WeatherDatabase_Impl  lazy 3com.example.weatherwidget.data.WeatherDatabase_Impl  
mutableListOf 3com.example.weatherwidget.data.WeatherDatabase_Impl  mutableMapOf 3com.example.weatherwidget.data.WeatherDatabase_Impl  mutableSetOf 3com.example.weatherwidget.data.WeatherDatabase_Impl  read 3com.example.weatherwidget.data.WeatherDatabase_Impl  
trimMargin 3com.example.weatherwidget.data.WeatherDatabase_Impl  API_KEY 7com.example.weatherwidget.data.WidgetPreferencesManager  	Companion 7com.example.weatherwidget.data.WidgetPreferencesManager  Context 7com.example.weatherwidget.data.WidgetPreferencesManager  DEFAULT_CITY_ID 7com.example.weatherwidget.data.WidgetPreferencesManager  DEFAULT_UPDATE_FREQUENCY_HOURS 7com.example.weatherwidget.data.WidgetPreferencesManager  	DataStore 7com.example.weatherwidget.data.WidgetPreferencesManager  DefaultWidgetConfiguration 7com.example.weatherwidget.data.WidgetPreferencesManager  	Exception 7com.example.weatherwidget.data.WidgetPreferencesManager  Flow 7com.example.weatherwidget.data.WidgetPreferencesManager  IOException 7com.example.weatherwidget.data.WidgetPreferencesManager  Int 7com.example.weatherwidget.data.WidgetPreferencesManager  Json 7com.example.weatherwidget.data.WidgetPreferencesManager  LAST_UPDATE_TIME 7com.example.weatherwidget.data.WidgetPreferencesManager  Long 7com.example.weatherwidget.data.WidgetPreferencesManager  Map 7com.example.weatherwidget.data.WidgetPreferencesManager  Preferences 7com.example.weatherwidget.data.WidgetPreferencesManager  SELECTED_CITY_ID 7com.example.weatherwidget.data.WidgetPreferencesManager  String 7com.example.weatherwidget.data.WidgetPreferencesManager  UPDATE_FREQUENCY_HOURS 7com.example.weatherwidget.data.WidgetPreferencesManager  WidgetConfiguration 7com.example.weatherwidget.data.WidgetPreferencesManager  catch 7com.example.weatherwidget.data.WidgetPreferencesManager  
component1 7com.example.weatherwidget.data.WidgetPreferencesManager  
component2 7com.example.weatherwidget.data.WidgetPreferencesManager  context 7com.example.weatherwidget.data.WidgetPreferencesManager  create 7com.example.weatherwidget.data.WidgetPreferencesManager  	dataStore 7com.example.weatherwidget.data.WidgetPreferencesManager  deleteWidgetConfiguration 7com.example.weatherwidget.data.WidgetPreferencesManager  edit 7com.example.weatherwidget.data.WidgetPreferencesManager  emptyPreferences 7com.example.weatherwidget.data.WidgetPreferencesManager  getAllWidgetConfigurations 7com.example.weatherwidget.data.WidgetPreferencesManager  getWidgetConfiguration 7com.example.weatherwidget.data.WidgetPreferencesManager  intPreferencesKey 7com.example.weatherwidget.data.WidgetPreferencesManager  json 7com.example.weatherwidget.data.WidgetPreferencesManager  longPreferencesKey 7com.example.weatherwidget.data.WidgetPreferencesManager  map 7com.example.weatherwidget.data.WidgetPreferencesManager  mutableMapOf 7com.example.weatherwidget.data.WidgetPreferencesManager  preferencesDataStore 7com.example.weatherwidget.data.WidgetPreferencesManager  provideDelegate 7com.example.weatherwidget.data.WidgetPreferencesManager  removePrefix 7com.example.weatherwidget.data.WidgetPreferencesManager  saveWidgetConfiguration 7com.example.weatherwidget.data.WidgetPreferencesManager  set 7com.example.weatherwidget.data.WidgetPreferencesManager  
startsWith 7com.example.weatherwidget.data.WidgetPreferencesManager  stringPreferencesKey 7com.example.weatherwidget.data.WidgetPreferencesManager  toInt 7com.example.weatherwidget.data.WidgetPreferencesManager  widgetConfigKey 7com.example.weatherwidget.data.WidgetPreferencesManager  API_KEY Acom.example.weatherwidget.data.WidgetPreferencesManager.Companion  DEFAULT_CITY_ID Acom.example.weatherwidget.data.WidgetPreferencesManager.Companion  DEFAULT_UPDATE_FREQUENCY_HOURS Acom.example.weatherwidget.data.WidgetPreferencesManager.Companion  DefaultWidgetConfiguration Acom.example.weatherwidget.data.WidgetPreferencesManager.Companion  Json Acom.example.weatherwidget.data.WidgetPreferencesManager.Companion  LAST_UPDATE_TIME Acom.example.weatherwidget.data.WidgetPreferencesManager.Companion  SELECTED_CITY_ID Acom.example.weatherwidget.data.WidgetPreferencesManager.Companion  UPDATE_FREQUENCY_HOURS Acom.example.weatherwidget.data.WidgetPreferencesManager.Companion  catch Acom.example.weatherwidget.data.WidgetPreferencesManager.Companion  
component1 Acom.example.weatherwidget.data.WidgetPreferencesManager.Companion  
component2 Acom.example.weatherwidget.data.WidgetPreferencesManager.Companion  create Acom.example.weatherwidget.data.WidgetPreferencesManager.Companion  	dataStore Acom.example.weatherwidget.data.WidgetPreferencesManager.Companion  edit Acom.example.weatherwidget.data.WidgetPreferencesManager.Companion  emptyPreferences Acom.example.weatherwidget.data.WidgetPreferencesManager.Companion  intPreferencesKey Acom.example.weatherwidget.data.WidgetPreferencesManager.Companion  json Acom.example.weatherwidget.data.WidgetPreferencesManager.Companion  longPreferencesKey Acom.example.weatherwidget.data.WidgetPreferencesManager.Companion  map Acom.example.weatherwidget.data.WidgetPreferencesManager.Companion  mutableMapOf Acom.example.weatherwidget.data.WidgetPreferencesManager.Companion  preferencesDataStore Acom.example.weatherwidget.data.WidgetPreferencesManager.Companion  provideDelegate Acom.example.weatherwidget.data.WidgetPreferencesManager.Companion  removePrefix Acom.example.weatherwidget.data.WidgetPreferencesManager.Companion  set Acom.example.weatherwidget.data.WidgetPreferencesManager.Companion  
startsWith Acom.example.weatherwidget.data.WidgetPreferencesManager.Companion  stringPreferencesKey Acom.example.weatherwidget.data.WidgetPreferencesManager.Companion  toInt Acom.example.weatherwidget.data.WidgetPreferencesManager.Companion  widgetConfigKey Acom.example.weatherwidget.data.WidgetPreferencesManager.Companion  GET "com.example.weatherwidget.data.api  Long "com.example.weatherwidget.data.api  Query "com.example.weatherwidget.data.api  String "com.example.weatherwidget.data.api  WeatherApiService "com.example.weatherwidget.data.api  WeatherResponse "com.example.weatherwidget.data.api  getWeatherByCity 4com.example.weatherwidget.data.api.WeatherApiService  getWeatherByCityId 4com.example.weatherwidget.data.api.WeatherApiService  	Alignment $com.example.weatherwidget.data.model  Arrangement $com.example.weatherwidget.data.model  Boolean $com.example.weatherwidget.data.model  Column $com.example.weatherwidget.data.model  
Composable $com.example.weatherwidget.data.model  CornerRadius $com.example.weatherwidget.data.model  DefaultWidgetConfiguration $com.example.weatherwidget.data.model  Double $com.example.weatherwidget.data.model  Entity $com.example.weatherwidget.data.model  Float $com.example.weatherwidget.data.model  FontSize $com.example.weatherwidget.data.model  IconSize $com.example.weatherwidget.data.model  Int $com.example.weatherwidget.data.model  List $com.example.weatherwidget.data.model  LocationDisplayConfig $com.example.weatherwidget.data.model  Long $com.example.weatherwidget.data.model  Main $com.example.weatherwidget.data.model  
MaterialTheme $com.example.weatherwidget.data.model  Modifier $com.example.weatherwidget.data.model  NetworkConstraint $com.example.weatherwidget.data.model  
PrimaryKey $com.example.weatherwidget.data.model  RadioButton $com.example.weatherwidget.data.model  RefreshRate $com.example.weatherwidget.data.model  Role $com.example.weatherwidget.data.model  Row $com.example.weatherwidget.data.model  Serializable $com.example.weatherwidget.data.model  SerializedName $com.example.weatherwidget.data.model  Slider $com.example.weatherwidget.data.model  Spacer $com.example.weatherwidget.data.model  String $com.example.weatherwidget.data.model  Switch $com.example.weatherwidget.data.model  	SwitchRow $com.example.weatherwidget.data.model  Sys $com.example.weatherwidget.data.model  System $com.example.weatherwidget.data.model  Text $com.example.weatherwidget.data.model  TimeDisplayConfig $com.example.weatherwidget.data.model  Unit $com.example.weatherwidget.data.model  UpdateSettingsConfig $com.example.weatherwidget.data.model  VisualCustomizationConfig $com.example.weatherwidget.data.model  Weather $com.example.weatherwidget.data.model  WeatherElementsConfig $com.example.weatherwidget.data.model  WeatherInfo $com.example.weatherwidget.data.model  WeatherResponse $com.example.weatherwidget.data.model  WidgetConfiguration $com.example.weatherwidget.data.model  WidgetTheme $com.example.weatherwidget.data.model  WidgetThemeColors $com.example.weatherwidget.data.model  WidgetThemes $com.example.weatherwidget.data.model  Wind $com.example.weatherwidget.data.model  fillMaxWidth $com.example.weatherwidget.data.model  firstOrNull $com.example.weatherwidget.data.model  forEach $com.example.weatherwidget.data.model  height $com.example.weatherwidget.data.model  padding $com.example.weatherwidget.data.model  rangeTo $com.example.weatherwidget.data.model  
selectable $com.example.weatherwidget.data.model  selectableGroup $com.example.weatherwidget.data.model  
toWeatherInfo $com.example.weatherwidget.data.model  	Companion 1com.example.weatherwidget.data.model.CornerRadius  Int 1com.example.weatherwidget.data.model.CornerRadius  
ROUNDED_16 1com.example.weatherwidget.data.model.CornerRadius  	ROUNDED_8 1com.example.weatherwidget.data.model.CornerRadius  SQUARE 1com.example.weatherwidget.data.model.CornerRadius  values 1com.example.weatherwidget.data.model.CornerRadius  CornerRadius ?com.example.weatherwidget.data.model.DefaultWidgetConfiguration  FontSize ?com.example.weatherwidget.data.model.DefaultWidgetConfiguration  IconSize ?com.example.weatherwidget.data.model.DefaultWidgetConfiguration  LocationDisplayConfig ?com.example.weatherwidget.data.model.DefaultWidgetConfiguration  NetworkConstraint ?com.example.weatherwidget.data.model.DefaultWidgetConfiguration  RefreshRate ?com.example.weatherwidget.data.model.DefaultWidgetConfiguration  TimeDisplayConfig ?com.example.weatherwidget.data.model.DefaultWidgetConfiguration  UpdateSettingsConfig ?com.example.weatherwidget.data.model.DefaultWidgetConfiguration  VisualCustomizationConfig ?com.example.weatherwidget.data.model.DefaultWidgetConfiguration  WeatherElementsConfig ?com.example.weatherwidget.data.model.DefaultWidgetConfiguration  WidgetConfiguration ?com.example.weatherwidget.data.model.DefaultWidgetConfiguration  WidgetTheme ?com.example.weatherwidget.data.model.DefaultWidgetConfiguration  create ?com.example.weatherwidget.data.model.DefaultWidgetConfiguration  	Companion -com.example.weatherwidget.data.model.FontSize  Float -com.example.weatherwidget.data.model.FontSize  LARGE -com.example.weatherwidget.data.model.FontSize  MEDIUM -com.example.weatherwidget.data.model.FontSize  SMALL -com.example.weatherwidget.data.model.FontSize  scale -com.example.weatherwidget.data.model.FontSize  values -com.example.weatherwidget.data.model.FontSize  	Companion -com.example.weatherwidget.data.model.IconSize  Int -com.example.weatherwidget.data.model.IconSize  LARGE -com.example.weatherwidget.data.model.IconSize  MEDIUM -com.example.weatherwidget.data.model.IconSize  SMALL -com.example.weatherwidget.data.model.IconSize  dp -com.example.weatherwidget.data.model.IconSize  values -com.example.weatherwidget.data.model.IconSize  Boolean :com.example.weatherwidget.data.model.LocationDisplayConfig  Long :com.example.weatherwidget.data.model.LocationDisplayConfig  copy :com.example.weatherwidget.data.model.LocationDisplayConfig  selectedCityId :com.example.weatherwidget.data.model.LocationDisplayConfig  showCityName :com.example.weatherwidget.data.model.LocationDisplayConfig  	feelsLike )com.example.weatherwidget.data.model.Main  humidity )com.example.weatherwidget.data.model.Main  pressure )com.example.weatherwidget.data.model.Main  temp )com.example.weatherwidget.data.model.Main  ANY 6com.example.weatherwidget.data.model.NetworkConstraint  	Companion 6com.example.weatherwidget.data.model.NetworkConstraint  String 6com.example.weatherwidget.data.model.NetworkConstraint  displayName 6com.example.weatherwidget.data.model.NetworkConstraint  values 6com.example.weatherwidget.data.model.NetworkConstraint  	Companion 0com.example.weatherwidget.data.model.RefreshRate  Int 0com.example.weatherwidget.data.model.RefreshRate  ONE_HOUR 0com.example.weatherwidget.data.model.RefreshRate  String 0com.example.weatherwidget.data.model.RefreshRate  displayName 0com.example.weatherwidget.data.model.RefreshRate  minutes 0com.example.weatherwidget.data.model.RefreshRate  values 0com.example.weatherwidget.data.model.RefreshRate  country (com.example.weatherwidget.data.model.Sys  Boolean 6com.example.weatherwidget.data.model.TimeDisplayConfig  copy 6com.example.weatherwidget.data.model.TimeDisplayConfig  showLastUpdated 6com.example.weatherwidget.data.model.TimeDisplayConfig  NetworkConstraint 9com.example.weatherwidget.data.model.UpdateSettingsConfig  RefreshRate 9com.example.weatherwidget.data.model.UpdateSettingsConfig  copy 9com.example.weatherwidget.data.model.UpdateSettingsConfig  networkConstraint 9com.example.weatherwidget.data.model.UpdateSettingsConfig  refreshRate 9com.example.weatherwidget.data.model.UpdateSettingsConfig  NetworkConstraint Ccom.example.weatherwidget.data.model.UpdateSettingsConfig.Companion  RefreshRate Ccom.example.weatherwidget.data.model.UpdateSettingsConfig.Companion  CornerRadius >com.example.weatherwidget.data.model.VisualCustomizationConfig  FontSize >com.example.weatherwidget.data.model.VisualCustomizationConfig  IconSize >com.example.weatherwidget.data.model.VisualCustomizationConfig  copy >com.example.weatherwidget.data.model.VisualCustomizationConfig  cornerRadius >com.example.weatherwidget.data.model.VisualCustomizationConfig  fontSize >com.example.weatherwidget.data.model.VisualCustomizationConfig  iconSize >com.example.weatherwidget.data.model.VisualCustomizationConfig  CornerRadius Hcom.example.weatherwidget.data.model.VisualCustomizationConfig.Companion  FontSize Hcom.example.weatherwidget.data.model.VisualCustomizationConfig.Companion  IconSize Hcom.example.weatherwidget.data.model.VisualCustomizationConfig.Companion  description ,com.example.weatherwidget.data.model.Weather  icon ,com.example.weatherwidget.data.model.Weather  main ,com.example.weatherwidget.data.model.Weather  Boolean :com.example.weatherwidget.data.model.WeatherElementsConfig  copy :com.example.weatherwidget.data.model.WeatherElementsConfig  
showCondition :com.example.weatherwidget.data.model.WeatherElementsConfig  showHumidity :com.example.weatherwidget.data.model.WeatherElementsConfig  showTemperature :com.example.weatherwidget.data.model.WeatherElementsConfig  
showWindSpeed :com.example.weatherwidget.data.model.WeatherElementsConfig  cityId 0com.example.weatherwidget.data.model.WeatherInfo  cityName 0com.example.weatherwidget.data.model.WeatherInfo  copy 0com.example.weatherwidget.data.model.WeatherInfo  country 0com.example.weatherwidget.data.model.WeatherInfo  	feelsLike 0com.example.weatherwidget.data.model.WeatherInfo  humidity 0com.example.weatherwidget.data.model.WeatherInfo  
isFavorite 0com.example.weatherwidget.data.model.WeatherInfo  lastUpdated 0com.example.weatherwidget.data.model.WeatherInfo  pressure 0com.example.weatherwidget.data.model.WeatherInfo  temperature 0com.example.weatherwidget.data.model.WeatherInfo  weatherCondition 0com.example.weatherwidget.data.model.WeatherInfo  weatherDescription 0com.example.weatherwidget.data.model.WeatherInfo  weatherIconCode 0com.example.weatherwidget.data.model.WeatherInfo  
windDirection 0com.example.weatherwidget.data.model.WeatherInfo  	windSpeed 0com.example.weatherwidget.data.model.WeatherInfo  WeatherInfo 4com.example.weatherwidget.data.model.WeatherResponse  firstOrNull 4com.example.weatherwidget.data.model.WeatherResponse  id 4com.example.weatherwidget.data.model.WeatherResponse  main 4com.example.weatherwidget.data.model.WeatherResponse  name 4com.example.weatherwidget.data.model.WeatherResponse  sys 4com.example.weatherwidget.data.model.WeatherResponse  
toWeatherInfo 4com.example.weatherwidget.data.model.WeatherResponse  weather 4com.example.weatherwidget.data.model.WeatherResponse  wind 4com.example.weatherwidget.data.model.WeatherResponse  Float 8com.example.weatherwidget.data.model.WidgetConfiguration  Int 8com.example.weatherwidget.data.model.WidgetConfiguration  LocationDisplayConfig 8com.example.weatherwidget.data.model.WidgetConfiguration  TimeDisplayConfig 8com.example.weatherwidget.data.model.WidgetConfiguration  UpdateSettingsConfig 8com.example.weatherwidget.data.model.WidgetConfiguration  VisualCustomizationConfig 8com.example.weatherwidget.data.model.WidgetConfiguration  WeatherElementsConfig 8com.example.weatherwidget.data.model.WidgetConfiguration  WidgetTheme 8com.example.weatherwidget.data.model.WidgetConfiguration  copy 8com.example.weatherwidget.data.model.WidgetConfiguration  locationDisplay 8com.example.weatherwidget.data.model.WidgetConfiguration  theme 8com.example.weatherwidget.data.model.WidgetConfiguration  timeDisplay 8com.example.weatherwidget.data.model.WidgetConfiguration  transparency 8com.example.weatherwidget.data.model.WidgetConfiguration  updateSettings 8com.example.weatherwidget.data.model.WidgetConfiguration  visualCustomization 8com.example.weatherwidget.data.model.WidgetConfiguration  weatherElements 8com.example.weatherwidget.data.model.WidgetConfiguration  widgetId 8com.example.weatherwidget.data.model.WidgetConfiguration  LocationDisplayConfig Bcom.example.weatherwidget.data.model.WidgetConfiguration.Companion  TimeDisplayConfig Bcom.example.weatherwidget.data.model.WidgetConfiguration.Companion  UpdateSettingsConfig Bcom.example.weatherwidget.data.model.WidgetConfiguration.Companion  VisualCustomizationConfig Bcom.example.weatherwidget.data.model.WidgetConfiguration.Companion  WeatherElementsConfig Bcom.example.weatherwidget.data.model.WidgetConfiguration.Companion  WidgetTheme Bcom.example.weatherwidget.data.model.WidgetConfiguration.Companion  BLUE 0com.example.weatherwidget.data.model.WidgetTheme  	Companion 0com.example.weatherwidget.data.model.WidgetTheme  DARK 0com.example.weatherwidget.data.model.WidgetTheme  LIGHT 0com.example.weatherwidget.data.model.WidgetTheme  SYSTEM 0com.example.weatherwidget.data.model.WidgetTheme  entries 0com.example.weatherwidget.data.model.WidgetTheme  backgroundColor 6com.example.weatherwidget.data.model.WidgetThemeColors  iconTint 6com.example.weatherwidget.data.model.WidgetThemeColors  primaryTextColor 6com.example.weatherwidget.data.model.WidgetThemeColors  secondaryTextColor 6com.example.weatherwidget.data.model.WidgetThemeColors  BLUE 1com.example.weatherwidget.data.model.WidgetThemes  DARK 1com.example.weatherwidget.data.model.WidgetThemes  LIGHT 1com.example.weatherwidget.data.model.WidgetThemes  WidgetTheme 1com.example.weatherwidget.data.model.WidgetThemes  WidgetThemeColors 1com.example.weatherwidget.data.model.WidgetThemes  getThemeColors 1com.example.weatherwidget.data.model.WidgetThemes  deg )com.example.weatherwidget.data.model.Wind  speed )com.example.weatherwidget.data.model.Wind  API_KEY )com.example.weatherwidget.data.repository  Boolean )com.example.weatherwidget.data.repository  DEFAULT_API_KEY )com.example.weatherwidget.data.repository  DEFAULT_UPDATE_FREQUENCY_HOURS )com.example.weatherwidget.data.repository  	DataStore )com.example.weatherwidget.data.repository  	Exception )com.example.weatherwidget.data.repository  Flow )com.example.weatherwidget.data.repository  Int )com.example.weatherwidget.data.repository  List )com.example.weatherwidget.data.repository  Long )com.example.weatherwidget.data.repository  Map )com.example.weatherwidget.data.repository  MockWeatherRepository )com.example.weatherwidget.data.repository  MutableStateFlow )com.example.weatherwidget.data.repository  Preferences )com.example.weatherwidget.data.repository  Result )com.example.weatherwidget.data.repository  SELECTED_WIDGET_CITY_ID )com.example.weatherwidget.data.repository  SampleWeatherData )com.example.weatherwidget.data.repository  String )com.example.weatherwidget.data.repository  System )com.example.weatherwidget.data.repository  TODO )com.example.weatherwidget.data.repository  WIDGET_UPDATE_FREQUENCY )com.example.weatherwidget.data.repository  WeatherApiService )com.example.weatherwidget.data.repository  
WeatherDao )com.example.weatherwidget.data.repository  WeatherInfo )com.example.weatherwidget.data.repository  WeatherRepository )com.example.weatherwidget.data.repository  WeatherRepositoryImpl )com.example.weatherwidget.data.repository  WidgetConfiguration )com.example.weatherwidget.data.repository  WidgetPreferencesManager )com.example.weatherwidget.data.repository  delay )com.example.weatherwidget.data.repository  edit )com.example.weatherwidget.data.repository  failure )com.example.weatherwidget.data.repository  filter )com.example.weatherwidget.data.repository  find )com.example.weatherwidget.data.repository  firstOrNull )com.example.weatherwidget.data.repository  
getSampleData )com.example.weatherwidget.data.repository  intPreferencesKey )com.example.weatherwidget.data.repository  listOf )com.example.weatherwidget.data.repository  longPreferencesKey )com.example.weatherwidget.data.repository  map )com.example.weatherwidget.data.repository  random )com.example.weatherwidget.data.repository  stringPreferencesKey )com.example.weatherwidget.data.repository  success )com.example.weatherwidget.data.repository  
toWeatherInfo )com.example.weatherwidget.data.repository  	Exception ?com.example.weatherwidget.data.repository.MockWeatherRepository  MutableStateFlow ?com.example.weatherwidget.data.repository.MockWeatherRepository  Result ?com.example.weatherwidget.data.repository.MockWeatherRepository  SampleWeatherData ?com.example.weatherwidget.data.repository.MockWeatherRepository  System ?com.example.weatherwidget.data.repository.MockWeatherRepository  TODO ?com.example.weatherwidget.data.repository.MockWeatherRepository  WeatherInfo ?com.example.weatherwidget.data.repository.MockWeatherRepository  _weatherData ?com.example.weatherwidget.data.repository.MockWeatherRepository  apiKey ?com.example.weatherwidget.data.repository.MockWeatherRepository  delay ?com.example.weatherwidget.data.repository.MockWeatherRepository  failure ?com.example.weatherwidget.data.repository.MockWeatherRepository  filter ?com.example.weatherwidget.data.repository.MockWeatherRepository  find ?com.example.weatherwidget.data.repository.MockWeatherRepository  listOf ?com.example.weatherwidget.data.repository.MockWeatherRepository  map ?com.example.weatherwidget.data.repository.MockWeatherRepository  random ?com.example.weatherwidget.data.repository.MockWeatherRepository  selectedWidgetCityId ?com.example.weatherwidget.data.repository.MockWeatherRepository  success ?com.example.weatherwidget.data.repository.MockWeatherRepository  widgetUpdateFrequency ?com.example.weatherwidget.data.repository.MockWeatherRepository  System ;com.example.weatherwidget.data.repository.SampleWeatherData  WeatherInfo ;com.example.weatherwidget.data.repository.SampleWeatherData  currentTime ;com.example.weatherwidget.data.repository.SampleWeatherData  listOf ;com.example.weatherwidget.data.repository.SampleWeatherData  sampleWeatherList ;com.example.weatherwidget.data.repository.SampleWeatherData  SampleWeatherData ;com.example.weatherwidget.data.repository.WeatherRepository  deleteWeatherInfo ;com.example.weatherwidget.data.repository.WeatherRepository  getAllWeatherInfo ;com.example.weatherwidget.data.repository.WeatherRepository  getAllWidgetConfigurations ;com.example.weatherwidget.data.repository.WeatherRepository  getFavoriteWeatherInfo ;com.example.weatherwidget.data.repository.WeatherRepository  getSelectedWidgetCityId ;com.example.weatherwidget.data.repository.WeatherRepository  getWeatherInfoById ;com.example.weatherwidget.data.repository.WeatherRepository  getWidgetConfiguration ;com.example.weatherwidget.data.repository.WeatherRepository  getWidgetUpdateFrequencyHours ;com.example.weatherwidget.data.repository.WeatherRepository  refreshWeatherById ;com.example.weatherwidget.data.repository.WeatherRepository  searchWeatherByCity ;com.example.weatherwidget.data.repository.WeatherRepository  setSelectedWidgetCityId ;com.example.weatherwidget.data.repository.WeatherRepository  setWidgetUpdateFrequencyHours ;com.example.weatherwidget.data.repository.WeatherRepository  updateFavoriteStatus ;com.example.weatherwidget.data.repository.WeatherRepository  API_KEY ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  Boolean ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  DEFAULT_API_KEY ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  DEFAULT_UPDATE_FREQUENCY_HOURS ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  	DataStore ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  	Exception ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  Flow ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  Int ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  List ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  Long ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  Map ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  Preferences ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  Result ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  SELECTED_WIDGET_CITY_ID ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  String ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  WIDGET_UPDATE_FREQUENCY ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  WeatherApiService ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  
WeatherDao ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  WeatherInfo ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  WidgetConfiguration ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  WidgetPreferencesManager ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  	dataStore ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  edit ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  failure ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  firstOrNull ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  	getApiKey ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  intPreferencesKey ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  longPreferencesKey ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  map ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  stringPreferencesKey ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  success ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  
toWeatherInfo ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  weatherApiService ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  
weatherDao ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  widgetPreferencesManager ?com.example.weatherwidget.data.repository.WeatherRepositoryImpl  API_KEY Icom.example.weatherwidget.data.repository.WeatherRepositoryImpl.Companion  DEFAULT_API_KEY Icom.example.weatherwidget.data.repository.WeatherRepositoryImpl.Companion  DEFAULT_UPDATE_FREQUENCY_HOURS Icom.example.weatherwidget.data.repository.WeatherRepositoryImpl.Companion  Result Icom.example.weatherwidget.data.repository.WeatherRepositoryImpl.Companion  SELECTED_WIDGET_CITY_ID Icom.example.weatherwidget.data.repository.WeatherRepositoryImpl.Companion  WIDGET_UPDATE_FREQUENCY Icom.example.weatherwidget.data.repository.WeatherRepositoryImpl.Companion  edit Icom.example.weatherwidget.data.repository.WeatherRepositoryImpl.Companion  failure Icom.example.weatherwidget.data.repository.WeatherRepositoryImpl.Companion  firstOrNull Icom.example.weatherwidget.data.repository.WeatherRepositoryImpl.Companion  intPreferencesKey Icom.example.weatherwidget.data.repository.WeatherRepositoryImpl.Companion  longPreferencesKey Icom.example.weatherwidget.data.repository.WeatherRepositoryImpl.Companion  map Icom.example.weatherwidget.data.repository.WeatherRepositoryImpl.Companion  stringPreferencesKey Icom.example.weatherwidget.data.repository.WeatherRepositoryImpl.Companion  success Icom.example.weatherwidget.data.repository.WeatherRepositoryImpl.Companion  
toWeatherInfo Icom.example.weatherwidget.data.repository.WeatherRepositoryImpl.Companion  Context com.example.weatherwidget.di  	DataStore com.example.weatherwidget.di  GsonConverterFactory com.example.weatherwidget.di  HttpLoggingInterceptor com.example.weatherwidget.di  KoinWorkerFactory com.example.weatherwidget.di  
MainViewModel com.example.weatherwidget.di  MockWeatherRepository com.example.weatherwidget.di  OkHttpClient com.example.weatherwidget.di  Preferences com.example.weatherwidget.di  Retrofit com.example.weatherwidget.di  Room com.example.weatherwidget.di  TimeUnit com.example.weatherwidget.di  WeatherApiService com.example.weatherwidget.di  WeatherDatabase com.example.weatherwidget.di  WeatherRepository com.example.weatherwidget.di  WeatherRepositoryImpl com.example.weatherwidget.di  WeatherUpdateWorker com.example.weatherwidget.di  WeatherWidgetManager com.example.weatherwidget.di  WeatherWidgetViewModel com.example.weatherwidget.di  WidgetConfigurationUseCase com.example.weatherwidget.di  WidgetPreferencesManager com.example.weatherwidget.di  
WorkerFactory com.example.weatherwidget.di  	appModule com.example.weatherwidget.di  apply com.example.weatherwidget.di  	dataStore com.example.weatherwidget.di  databaseBuilder com.example.weatherwidget.di  databaseModule com.example.weatherwidget.di  java com.example.weatherwidget.di  
networkModule com.example.weatherwidget.di  provideDelegate com.example.weatherwidget.di  repositoryModule com.example.weatherwidget.di  testRepositoryModule com.example.weatherwidget.di  
useCaseModule com.example.weatherwidget.di  viewModelModule com.example.weatherwidget.di  widgetModule com.example.weatherwidget.di  workerModule com.example.weatherwidget.di  Boolean (com.example.weatherwidget.domain.usecase  DefaultWidgetConfiguration (com.example.weatherwidget.domain.usecase  Float (com.example.weatherwidget.domain.usecase  Flow (com.example.weatherwidget.domain.usecase  Int (com.example.weatherwidget.domain.usecase  Long (com.example.weatherwidget.domain.usecase  Map (com.example.weatherwidget.domain.usecase  WidgetConfiguration (com.example.weatherwidget.domain.usecase  WidgetConfigurationUseCase (com.example.weatherwidget.domain.usecase  WidgetPreferencesManager (com.example.weatherwidget.domain.usecase  com (com.example.weatherwidget.domain.usecase  create (com.example.weatherwidget.domain.usecase  first (com.example.weatherwidget.domain.usecase  rangeTo (com.example.weatherwidget.domain.usecase  DefaultWidgetConfiguration Ccom.example.weatherwidget.domain.usecase.WidgetConfigurationUseCase  create Ccom.example.weatherwidget.domain.usecase.WidgetConfigurationUseCase  deleteWidgetConfiguration Ccom.example.weatherwidget.domain.usecase.WidgetConfigurationUseCase  first Ccom.example.weatherwidget.domain.usecase.WidgetConfigurationUseCase  getAllWidgetConfigurations Ccom.example.weatherwidget.domain.usecase.WidgetConfigurationUseCase  getWidgetConfiguration Ccom.example.weatherwidget.domain.usecase.WidgetConfigurationUseCase  rangeTo Ccom.example.weatherwidget.domain.usecase.WidgetConfigurationUseCase  resetWidgetConfiguration Ccom.example.weatherwidget.domain.usecase.WidgetConfigurationUseCase  saveWidgetConfiguration Ccom.example.weatherwidget.domain.usecase.WidgetConfigurationUseCase  widgetPreferencesManager Ccom.example.weatherwidget.domain.usecase.WidgetConfigurationUseCase  example ,com.example.weatherwidget.domain.usecase.com  
weatherwidget 4com.example.weatherwidget.domain.usecase.com.example  data Bcom.example.weatherwidget.domain.usecase.com.example.weatherwidget  model Gcom.example.weatherwidget.domain.usecase.com.example.weatherwidget.data  CornerRadius Mcom.example.weatherwidget.domain.usecase.com.example.weatherwidget.data.model  FontSize Mcom.example.weatherwidget.domain.usecase.com.example.weatherwidget.data.model  IconSize Mcom.example.weatherwidget.domain.usecase.com.example.weatherwidget.data.model  NetworkConstraint Mcom.example.weatherwidget.domain.usecase.com.example.weatherwidget.data.model  RefreshRate Mcom.example.weatherwidget.domain.usecase.com.example.weatherwidget.data.model  WidgetTheme Mcom.example.weatherwidget.domain.usecase.com.example.weatherwidget.data.model  	Alignment com.example.weatherwidget.ui  
AsyncImage com.example.weatherwidget.ui  Boolean com.example.weatherwidget.ui  Box com.example.weatherwidget.ui  BuildConfig com.example.weatherwidget.ui  Bundle com.example.weatherwidget.ui  Button com.example.weatherwidget.ui  CircularProgressIndicator com.example.weatherwidget.ui  Color com.example.weatherwidget.ui  Column com.example.weatherwidget.ui  ComponentActivity com.example.weatherwidget.ui  
Composable com.example.weatherwidget.ui  Divider com.example.weatherwidget.ui  	Exception com.example.weatherwidget.ui  Flow com.example.weatherwidget.ui  
FontWeight com.example.weatherwidget.ui  Icon com.example.weatherwidget.ui  
IconButton com.example.weatherwidget.ui  Icons com.example.weatherwidget.ui  	ImeAction com.example.weatherwidget.ui  KeyboardActions com.example.weatherwidget.ui  KeyboardOptions com.example.weatherwidget.ui  
LazyColumn com.example.weatherwidget.ui  List com.example.weatherwidget.ui  Locale com.example.weatherwidget.ui  Long com.example.weatherwidget.ui  MainActivity com.example.weatherwidget.ui  
MainScreen com.example.weatherwidget.ui  
MainViewModel com.example.weatherwidget.ui  
MaterialTheme com.example.weatherwidget.ui  Modifier com.example.weatherwidget.ui  MutableStateFlow com.example.weatherwidget.ui  OutlinedTextField com.example.weatherwidget.ui  Row com.example.weatherwidget.ui  SampleWeatherData com.example.weatherwidget.ui  SharingStarted com.example.weatherwidget.ui  Spacer com.example.weatherwidget.ui  	StateFlow com.example.weatherwidget.ui  String com.example.weatherwidget.ui  Surface com.example.weatherwidget.ui  Text com.example.weatherwidget.ui  Unit com.example.weatherwidget.ui  	ViewModel com.example.weatherwidget.ui  WeatherInfo com.example.weatherwidget.ui  WeatherItem com.example.weatherwidget.ui  WeatherRepository com.example.weatherwidget.ui  WeatherUiState com.example.weatherwidget.ui  WhileSubscribed com.example.weatherwidget.ui  
_isLoading com.example.weatherwidget.ui  _searchError com.example.weatherwidget.ui  align com.example.weatherwidget.ui  
capitalize com.example.weatherwidget.ui  combine com.example.weatherwidget.ui  	emptyList com.example.weatherwidget.ui  fillMaxSize com.example.weatherwidget.ui  fillMaxWidth com.example.weatherwidget.ui  first com.example.weatherwidget.ui  forEach com.example.weatherwidget.ui  formatLastUpdated com.example.weatherwidget.ui  height com.example.weatherwidget.ui  isLowerCase com.example.weatherwidget.ui  
isNotBlank com.example.weatherwidget.ui  launch com.example.weatherwidget.ui  let com.example.weatherwidget.ui  padding com.example.weatherwidget.ui  println com.example.weatherwidget.ui  provideDelegate com.example.weatherwidget.ui  replaceFirstChar com.example.weatherwidget.ui  size com.example.weatherwidget.ui  stateIn com.example.weatherwidget.ui  	titlecase com.example.weatherwidget.ui  weatherRepository com.example.weatherwidget.ui  weight com.example.weatherwidget.ui  width com.example.weatherwidget.ui  
MainScreen )com.example.weatherwidget.ui.MainActivity  
MaterialTheme )com.example.weatherwidget.ui.MainActivity  Surface )com.example.weatherwidget.ui.MainActivity  
setContent )com.example.weatherwidget.ui.MainActivity  BuildConfig *com.example.weatherwidget.ui.MainViewModel  MutableStateFlow *com.example.weatherwidget.ui.MainViewModel  SampleWeatherData *com.example.weatherwidget.ui.MainViewModel  SharingStarted *com.example.weatherwidget.ui.MainViewModel  WeatherUiState *com.example.weatherwidget.ui.MainViewModel  WhileSubscribed *com.example.weatherwidget.ui.MainViewModel  
_isLoading *com.example.weatherwidget.ui.MainViewModel  _searchError *com.example.weatherwidget.ui.MainViewModel  combine *com.example.weatherwidget.ui.MainViewModel  
deleteWeather *com.example.weatherwidget.ui.MainViewModel  first *com.example.weatherwidget.ui.MainViewModel  	isLoading *com.example.weatherwidget.ui.MainViewModel  launch *com.example.weatherwidget.ui.MainViewModel  loadSampleData *com.example.weatherwidget.ui.MainViewModel  println *com.example.weatherwidget.ui.MainViewModel  refreshAllWeather *com.example.weatherwidget.ui.MainViewModel  refreshWeather *com.example.weatherwidget.ui.MainViewModel  
searchCity *com.example.weatherwidget.ui.MainViewModel  searchError *com.example.weatherwidget.ui.MainViewModel  stateIn *com.example.weatherwidget.ui.MainViewModel  toggleFavorite *com.example.weatherwidget.ui.MainViewModel  uiState *com.example.weatherwidget.ui.MainViewModel  viewModelScope *com.example.weatherwidget.ui.MainViewModel  weatherList *com.example.weatherwidget.ui.MainViewModel  weatherRepository *com.example.weatherwidget.ui.MainViewModel  weatherList +com.example.weatherwidget.ui.WeatherUiState  Boolean com.example.weatherwidget.util  Int com.example.weatherwidget.util  R com.example.weatherwidget.util  WeatherIconUtils com.example.weatherwidget.util  R /com.example.weatherwidget.util.WeatherIconUtils  getWeatherIconResource /com.example.weatherwidget.util.WeatherIconUtils  ActionCallback  com.example.weatherwidget.widget  ActionParameters  com.example.weatherwidget.widget  Activity  com.example.weatherwidget.widget  	Alignment  com.example.weatherwidget.widget  AppWidgetManager  com.example.weatherwidget.widget  Arrangement  com.example.weatherwidget.widget  Boolean  com.example.weatherwidget.widget  Box  com.example.weatherwidget.widget  Bundle  com.example.weatherwidget.widget  Card  com.example.weatherwidget.widget  CardDefaults  com.example.weatherwidget.widget  CitySelectionItem  com.example.weatherwidget.widget  CitySelectionSection  com.example.weatherwidget.widget  Color  com.example.weatherwidget.widget  Column  com.example.weatherwidget.widget  ComponentActivity  com.example.weatherwidget.widget  
ComponentName  com.example.weatherwidget.widget  
Composable  com.example.weatherwidget.widget  ConfigurationSection  com.example.weatherwidget.widget  Constraints  com.example.weatherwidget.widget  Context  com.example.weatherwidget.widget  CornerRadius  com.example.weatherwidget.widget  CoroutineScope  com.example.weatherwidget.widget  DefaultWidgetConfiguration  com.example.weatherwidget.widget  Dispatchers  com.example.weatherwidget.widget  ExistingPeriodicWorkPolicy  com.example.weatherwidget.widget  ExperimentalMaterial3Api  com.example.weatherwidget.widget  Float  com.example.weatherwidget.widget  Flow  com.example.weatherwidget.widget  FontSize  com.example.weatherwidget.widget  
FontWeight  com.example.weatherwidget.widget  GlanceAlignment  com.example.weatherwidget.widget  GlanceAppWidget  com.example.weatherwidget.widget  GlanceAppWidgetReceiver  com.example.weatherwidget.widget  GlanceFontWeight  com.example.weatherwidget.widget  GlanceId  com.example.weatherwidget.widget  GlanceModifier  com.example.weatherwidget.widget  GlanceSpacer  com.example.weatherwidget.widget  
GlanceText  com.example.weatherwidget.widget  GlanceTextAlign  com.example.weatherwidget.widget  GlanceTextStyle  com.example.weatherwidget.widget  GlanceWeatherContent  com.example.weatherwidget.widget  GlanceWeatherWidgetContent  com.example.weatherwidget.widget  Icon  com.example.weatherwidget.widget  
IconButton  com.example.weatherwidget.widget  IconSize  com.example.weatherwidget.widget  Icons  com.example.weatherwidget.widget  Image  com.example.weatherwidget.widget  
ImageProvider  com.example.weatherwidget.widget  ImageVector  com.example.weatherwidget.widget  Int  com.example.weatherwidget.widget  IntArray  com.example.weatherwidget.widget  Intent  com.example.weatherwidget.widget  
KoinComponent  com.example.weatherwidget.widget  
LazyColumn  com.example.weatherwidget.widget  List  com.example.weatherwidget.widget  Locale  com.example.weatherwidget.widget  Long  com.example.weatherwidget.widget  MainActivity  com.example.weatherwidget.widget  
MaterialTheme  com.example.weatherwidget.widget  Modifier  com.example.weatherwidget.widget  NetworkConstraint  com.example.weatherwidget.widget  NetworkType  com.example.weatherwidget.widget  
OpenAppAction  com.example.weatherwidget.widget  OptIn  com.example.weatherwidget.widget  OutlinedButton  com.example.weatherwidget.widget  Pair  com.example.weatherwidget.widget  PeriodicWorkRequestBuilder  com.example.weatherwidget.widget  RadioButton  com.example.weatherwidget.widget  RefreshRate  com.example.weatherwidget.widget  Role  com.example.weatherwidget.widget  Row  com.example.weatherwidget.widget  Slider  com.example.weatherwidget.widget  Spacer  com.example.weatherwidget.widget  String  com.example.weatherwidget.widget  Surface  com.example.weatherwidget.widget  Switch  com.example.weatherwidget.widget  	SwitchRow  com.example.weatherwidget.widget  Text  com.example.weatherwidget.widget  	TextAlign  com.example.weatherwidget.widget  
TextButton  com.example.weatherwidget.widget  ThemeSection  com.example.weatherwidget.widget  TimeDisplayConfig  com.example.weatherwidget.widget  TimeUnit  com.example.weatherwidget.widget  	TopAppBar  com.example.weatherwidget.widget  Unit  com.example.weatherwidget.widget  UpdateSettingsConfig  com.example.weatherwidget.widget  UpdateSettingsSection  com.example.weatherwidget.widget  	ViewModel  com.example.weatherwidget.widget  VisualCustomizationConfig  com.example.weatherwidget.widget  VisualCustomizationSection  com.example.weatherwidget.widget  WEATHER_UPDATE_WORK  com.example.weatherwidget.widget  WeatherContent  com.example.weatherwidget.widget  WeatherElementsConfig  com.example.weatherwidget.widget  WeatherElementsSection  com.example.weatherwidget.widget  WeatherIconUtils  com.example.weatherwidget.widget  WeatherInfo  com.example.weatherwidget.widget  WeatherRepository  com.example.weatherwidget.widget  WeatherUpdateWorker  com.example.weatherwidget.widget  
WeatherWidget  com.example.weatherwidget.widget  WeatherWidgetConfigActivity  com.example.weatherwidget.widget  WeatherWidgetManager  com.example.weatherwidget.widget  WeatherWidgetPreview  com.example.weatherwidget.widget  WeatherWidgetReceiver  com.example.weatherwidget.widget  WeatherWidgetViewModel  com.example.weatherwidget.widget  WidgetConfigScreen  com.example.weatherwidget.widget  WidgetConfiguration  com.example.weatherwidget.widget  WidgetConfigurationUseCase  com.example.weatherwidget.widget  WidgetTheme  com.example.weatherwidget.widget  WidgetThemeColors  com.example.weatherwidget.widget  WidgetThemes  com.example.weatherwidget.widget  WorkManager  com.example.weatherwidget.widget  android  com.example.weatherwidget.widget  apply  com.example.weatherwidget.widget  
cardColors  com.example.weatherwidget.widget  
cardElevation  com.example.weatherwidget.widget  configurationUseCase  com.example.weatherwidget.widget  context  com.example.weatherwidget.widget  create  com.example.weatherwidget.widget  	emptyList  com.example.weatherwidget.widget  fillMaxSize  com.example.weatherwidget.widget  fillMaxWidth  com.example.weatherwidget.widget  find  com.example.weatherwidget.widget  firstOrNull  com.example.weatherwidget.widget  forEach  com.example.weatherwidget.widget  formatLastUpdated  com.example.weatherwidget.widget  getInstance  com.example.weatherwidget.widget  getThemeColors  com.example.weatherwidget.widget  getValue  com.example.weatherwidget.widget  getWeatherIconResource  com.example.weatherwidget.widget  glanceFillMaxSize  com.example.weatherwidget.widget  glanceFillMaxWidth  com.example.weatherwidget.widget  glanceHeight  com.example.weatherwidget.widget  
glanceSize  com.example.weatherwidget.widget  glanceWidth  com.example.weatherwidget.widget  height  com.example.weatherwidget.widget  heightIn  com.example.weatherwidget.widget  isEmpty  com.example.weatherwidget.widget  java  com.example.weatherwidget.widget  launch  com.example.weatherwidget.widget  padding  com.example.weatherwidget.widget  painterResource  com.example.weatherwidget.widget  provideDelegate  com.example.weatherwidget.widget  rangeTo  com.example.weatherwidget.widget  
selectable  com.example.weatherwidget.widget  selectableGroup  com.example.weatherwidget.widget  size  com.example.weatherwidget.widget  spacedBy  com.example.weatherwidget.widget  toInt  com.example.weatherwidget.widget  	updateAll  com.example.weatherwidget.widget  weatherRepository  com.example.weatherwidget.widget  
weatherWidget  com.example.weatherwidget.widget  weight  com.example.weatherwidget.widget  widgetConfigurationUseCase  com.example.weatherwidget.widget  width  com.example.weatherwidget.widget  withContext  com.example.weatherwidget.widget  Intent .com.example.weatherwidget.widget.OpenAppAction  MainActivity .com.example.weatherwidget.widget.OpenAppAction  apply .com.example.weatherwidget.widget.OpenAppAction  java .com.example.weatherwidget.widget.OpenAppAction  DefaultWidgetConfiguration .com.example.weatherwidget.widget.WeatherWidget  Dispatchers .com.example.weatherwidget.widget.WeatherWidget  GlanceWeatherWidgetContent .com.example.weatherwidget.widget.WeatherWidget  Pair .com.example.weatherwidget.widget.WeatherWidget  WeatherWidgetContent .com.example.weatherwidget.widget.WeatherWidget  android .com.example.weatherwidget.widget.WeatherWidget  create .com.example.weatherwidget.widget.WeatherWidget  firstOrNull .com.example.weatherwidget.widget.WeatherWidget  getValue .com.example.weatherwidget.widget.WeatherWidget  inject .com.example.weatherwidget.widget.WeatherWidget  	javaClass .com.example.weatherwidget.widget.WeatherWidget  provideContent .com.example.weatherwidget.widget.WeatherWidget  provideDelegate .com.example.weatherwidget.widget.WeatherWidget  update .com.example.weatherwidget.widget.WeatherWidget  	updateAll .com.example.weatherwidget.widget.WeatherWidget  weatherRepository .com.example.weatherwidget.widget.WeatherWidget  withContext .com.example.weatherwidget.widget.WeatherWidget  Activity <com.example.weatherwidget.widget.WeatherWidgetConfigActivity  AppWidgetManager <com.example.weatherwidget.widget.WeatherWidgetConfigActivity  Intent <com.example.weatherwidget.widget.WeatherWidgetConfigActivity  
MaterialTheme <com.example.weatherwidget.widget.WeatherWidgetConfigActivity  Surface <com.example.weatherwidget.widget.WeatherWidgetConfigActivity  WidgetConfigScreen <com.example.weatherwidget.widget.WeatherWidgetConfigActivity  appWidgetId <com.example.weatherwidget.widget.WeatherWidgetConfigActivity  finish <com.example.weatherwidget.widget.WeatherWidgetConfigActivity  intent <com.example.weatherwidget.widget.WeatherWidgetConfigActivity  
setContent <com.example.weatherwidget.widget.WeatherWidgetConfigActivity  	setResult <com.example.weatherwidget.widget.WeatherWidgetConfigActivity  Constraints 5com.example.weatherwidget.widget.WeatherWidgetManager  Context 5com.example.weatherwidget.widget.WeatherWidgetManager  CoroutineScope 5com.example.weatherwidget.widget.WeatherWidgetManager  Dispatchers 5com.example.weatherwidget.widget.WeatherWidgetManager  ExistingPeriodicWorkPolicy 5com.example.weatherwidget.widget.WeatherWidgetManager  Long 5com.example.weatherwidget.widget.WeatherWidgetManager  NetworkType 5com.example.weatherwidget.widget.WeatherWidgetManager  PeriodicWorkRequestBuilder 5com.example.weatherwidget.widget.WeatherWidgetManager  TimeUnit 5com.example.weatherwidget.widget.WeatherWidgetManager  WEATHER_UPDATE_WORK 5com.example.weatherwidget.widget.WeatherWidgetManager  WeatherRepository 5com.example.weatherwidget.widget.WeatherWidgetManager  WeatherUpdateWorker 5com.example.weatherwidget.widget.WeatherWidgetManager  
WeatherWidget 5com.example.weatherwidget.widget.WeatherWidgetManager  WorkManager 5com.example.weatherwidget.widget.WeatherWidgetManager  cancelWidgetUpdates 5com.example.weatherwidget.widget.WeatherWidgetManager  context 5com.example.weatherwidget.widget.WeatherWidgetManager  getInstance 5com.example.weatherwidget.widget.WeatherWidgetManager  launch 5com.example.weatherwidget.widget.WeatherWidgetManager  scheduleWidgetUpdates 5com.example.weatherwidget.widget.WeatherWidgetManager  	updateAll 5com.example.weatherwidget.widget.WeatherWidgetManager  updateWidgetsNow 5com.example.weatherwidget.widget.WeatherWidgetManager  weatherRepository 5com.example.weatherwidget.widget.WeatherWidgetManager  
weatherWidget 5com.example.weatherwidget.widget.WeatherWidgetManager  widgetScope 5com.example.weatherwidget.widget.WeatherWidgetManager  Constraints ?com.example.weatherwidget.widget.WeatherWidgetManager.Companion  CoroutineScope ?com.example.weatherwidget.widget.WeatherWidgetManager.Companion  Dispatchers ?com.example.weatherwidget.widget.WeatherWidgetManager.Companion  ExistingPeriodicWorkPolicy ?com.example.weatherwidget.widget.WeatherWidgetManager.Companion  NetworkType ?com.example.weatherwidget.widget.WeatherWidgetManager.Companion  PeriodicWorkRequestBuilder ?com.example.weatherwidget.widget.WeatherWidgetManager.Companion  TimeUnit ?com.example.weatherwidget.widget.WeatherWidgetManager.Companion  WEATHER_UPDATE_WORK ?com.example.weatherwidget.widget.WeatherWidgetManager.Companion  
WeatherWidget ?com.example.weatherwidget.widget.WeatherWidgetManager.Companion  WorkManager ?com.example.weatherwidget.widget.WeatherWidgetManager.Companion  context ?com.example.weatherwidget.widget.WeatherWidgetManager.Companion  getInstance ?com.example.weatherwidget.widget.WeatherWidgetManager.Companion  launch ?com.example.weatherwidget.widget.WeatherWidgetManager.Companion  	updateAll ?com.example.weatherwidget.widget.WeatherWidgetManager.Companion  weatherRepository ?com.example.weatherwidget.widget.WeatherWidgetManager.Companion  
weatherWidget ?com.example.weatherwidget.widget.WeatherWidgetManager.Companion  AppWidgetManager 6com.example.weatherwidget.widget.WeatherWidgetReceiver  
ComponentName 6com.example.weatherwidget.widget.WeatherWidgetReceiver  CoroutineScope 6com.example.weatherwidget.widget.WeatherWidgetReceiver  Dispatchers 6com.example.weatherwidget.widget.WeatherWidgetReceiver  WeatherWidgetReceiver 6com.example.weatherwidget.widget.WeatherWidgetReceiver  configurationUseCase 6com.example.weatherwidget.widget.WeatherWidgetReceiver  forEach 6com.example.weatherwidget.widget.WeatherWidgetReceiver  getValue 6com.example.weatherwidget.widget.WeatherWidgetReceiver  inject 6com.example.weatherwidget.widget.WeatherWidgetReceiver  isEmpty 6com.example.weatherwidget.widget.WeatherWidgetReceiver  java 6com.example.weatherwidget.widget.WeatherWidgetReceiver  launch 6com.example.weatherwidget.widget.WeatherWidgetReceiver  provideDelegate 6com.example.weatherwidget.widget.WeatherWidgetReceiver  
widgetManager 6com.example.weatherwidget.widget.WeatherWidgetReceiver  allWeatherInfo 7com.example.weatherwidget.widget.WeatherWidgetViewModel  launch 7com.example.weatherwidget.widget.WeatherWidgetViewModel  refreshWeather 7com.example.weatherwidget.widget.WeatherWidgetViewModel  viewModelScope 7com.example.weatherwidget.widget.WeatherWidgetViewModel  weatherRepository 7com.example.weatherwidget.widget.WeatherWidgetViewModel  widgetConfigurationUseCase 7com.example.weatherwidget.widget.WeatherWidgetViewModel  Context  com.example.weatherwidget.worker  CoroutineWorker  com.example.weatherwidget.worker  Dispatchers  com.example.weatherwidget.worker  	Exception  com.example.weatherwidget.worker  GlanceAppWidgetManager  com.example.weatherwidget.worker  
KoinComponent  com.example.weatherwidget.worker  KoinWorkerFactory  com.example.weatherwidget.worker  ListenableWorker  com.example.weatherwidget.worker  Result  com.example.weatherwidget.worker  String  com.example.weatherwidget.worker  WeatherRepository  com.example.weatherwidget.worker  WeatherUpdateWorker  com.example.weatherwidget.worker  WeatherWidgetManager  com.example.weatherwidget.worker  WidgetConfigurationUseCase  com.example.weatherwidget.worker  
WorkerFactory  com.example.weatherwidget.worker  WorkerParameters  com.example.weatherwidget.worker  applicationContext  com.example.weatherwidget.worker  configurationUseCase  com.example.weatherwidget.worker  first  com.example.weatherwidget.worker  forEach  com.example.weatherwidget.worker  getValue  com.example.weatherwidget.worker  java  com.example.weatherwidget.worker  	javaClass  com.example.weatherwidget.worker  parametersOf  com.example.weatherwidget.worker  provideDelegate  com.example.weatherwidget.worker  updateAppWidgetState  com.example.weatherwidget.worker  weatherRepository  com.example.weatherwidget.worker  
widgetManager  com.example.weatherwidget.worker  withContext  com.example.weatherwidget.worker  WeatherUpdateWorker 2com.example.weatherwidget.worker.KoinWorkerFactory  getValue 2com.example.weatherwidget.worker.KoinWorkerFactory  inject 2com.example.weatherwidget.worker.KoinWorkerFactory  java 2com.example.weatherwidget.worker.KoinWorkerFactory  parametersOf 2com.example.weatherwidget.worker.KoinWorkerFactory  provideDelegate 2com.example.weatherwidget.worker.KoinWorkerFactory  Dispatchers 4com.example.weatherwidget.worker.WeatherUpdateWorker  GlanceAppWidgetManager 4com.example.weatherwidget.worker.WeatherUpdateWorker  Result 4com.example.weatherwidget.worker.WeatherUpdateWorker  applicationContext 4com.example.weatherwidget.worker.WeatherUpdateWorker  configurationUseCase 4com.example.weatherwidget.worker.WeatherUpdateWorker  first 4com.example.weatherwidget.worker.WeatherUpdateWorker  getValue 4com.example.weatherwidget.worker.WeatherUpdateWorker  inject 4com.example.weatherwidget.worker.WeatherUpdateWorker  	javaClass 4com.example.weatherwidget.worker.WeatherUpdateWorker  provideDelegate 4com.example.weatherwidget.worker.WeatherUpdateWorker  updateAppWidgetState 4com.example.weatherwidget.worker.WeatherUpdateWorker  weatherRepository 4com.example.weatherwidget.worker.WeatherUpdateWorker  
widgetManager 4com.example.weatherwidget.worker.WeatherUpdateWorker  withContext 4com.example.weatherwidget.worker.WeatherUpdateWorker  SerializedName com.google.gson.annotations  IOException java.io  Class 	java.lang  	Exception 	java.lang  StringBuffer 	java.lang  name java.lang.Class  message java.lang.Exception  printStackTrace java.lang.Exception  currentTimeMillis java.lang.System  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  Date 	java.util  Locale 	java.util  
getDefault java.util.Locale  TimeUnit java.util.concurrent  HOURS java.util.concurrent.TimeUnit  SECONDS java.util.concurrent.TimeUnit  	Generated javax.annotation.processing  Array kotlin  Boolean kotlin  CharSequence kotlin  Double kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Int kotlin  IntArray kotlin  Lazy kotlin  Long kotlin  Nothing kotlin  OptIn kotlin  Pair kotlin  Result kotlin  String kotlin  Suppress kotlin  TODO kotlin  	Throwable kotlin  Unit kotlin  apply kotlin  arrayOf kotlin  getValue kotlin  lazy kotlin  let kotlin  map kotlin  hashCode 
kotlin.Any  toString 
kotlin.Any  forEach kotlin.Array  not kotlin.Boolean  isLowerCase kotlin.Char  	titlecase kotlin.Char  toString kotlin.Char  toInt 
kotlin.Double  
unaryMinus 
kotlin.Double  Float kotlin.Enum  Int kotlin.Enum  String kotlin.Enum  minus kotlin.Float  rangeTo kotlin.Float  sp kotlin.Float  times kotlin.Float  toInt kotlin.Float  invoke kotlin.Function0  invoke kotlin.Function1  and 
kotlin.Int  	compareTo 
kotlin.Int  dp 
kotlin.Int  or 
kotlin.Int  rangeTo 
kotlin.Int  times 
kotlin.Int  toDouble 
kotlin.Int  toLong 
kotlin.Int  forEach kotlin.IntArray  isEmpty kotlin.IntArray  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  value kotlin.Lazy  minus kotlin.Long  toInt kotlin.Long  
component1 kotlin.Pair  
component2 kotlin.Pair  	Companion 
kotlin.Result  failure 
kotlin.Result  	isFailure 
kotlin.Result  	isSuccess 
kotlin.Result  success 
kotlin.Result  failure kotlin.Result.Companion  success kotlin.Result.Companion  Locale 
kotlin.String  
capitalize 
kotlin.String  hashCode 
kotlin.String  isLowerCase 
kotlin.String  
isNotBlank 
kotlin.String  let 
kotlin.String  plus 
kotlin.String  removePrefix 
kotlin.String  replaceFirstChar 
kotlin.String  
startsWith 
kotlin.String  	titlecase 
kotlin.String  toInt 
kotlin.String  
trimMargin 
kotlin.String  message kotlin.Throwable  printStackTrace kotlin.Throwable  List kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  
MutableSet kotlin.collections  Set kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  	emptyList kotlin.collections  filter kotlin.collections  find kotlin.collections  firstOrNull kotlin.collections  forEach kotlin.collections  getValue kotlin.collections  isEmpty kotlin.collections  listOf kotlin.collections  map kotlin.collections  
mutableListOf kotlin.collections  mutableMapOf kotlin.collections  mutableSetOf kotlin.collections  random kotlin.collections  set kotlin.collections  filter kotlin.collections.List  find kotlin.collections.List  firstOrNull kotlin.collections.List  isEmpty kotlin.collections.List  map kotlin.collections.List  random kotlin.collections.List  Entry kotlin.collections.Map  containsKey kotlin.collections.Map  
component1 kotlin.collections.Map.Entry  
component2 kotlin.collections.Map.Entry  add kotlin.collections.MutableList  put kotlin.collections.MutableMap  set kotlin.collections.MutableMap  SuspendFunction1 kotlin.coroutines  SuspendFunction2 kotlin.coroutines  SuspendFunction3 kotlin.coroutines  EnumEntries kotlin.enums  println 	kotlin.io  
startsWith 	kotlin.io  java 
kotlin.jvm  	javaClass 
kotlin.jvm  ReadOnlyProperty kotlin.properties  getValue "kotlin.properties.ReadOnlyProperty  provideDelegate "kotlin.properties.ReadOnlyProperty  ClosedFloatingPointRange 
kotlin.ranges  ClosedRange 
kotlin.ranges  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  firstOrNull 
kotlin.ranges  random 
kotlin.ranges  rangeTo 
kotlin.ranges  contains &kotlin.ranges.ClosedFloatingPointRange  contains kotlin.ranges.IntRange  random kotlin.ranges.IntRange  KClass kotlin.reflect  KMutableProperty0 kotlin.reflect  
KProperty0 kotlin.reflect  
KProperty1 kotlin.reflect  
KProperty2 kotlin.reflect  java kotlin.reflect.KClass  Sequence kotlin.sequences  filter kotlin.sequences  find kotlin.sequences  firstOrNull kotlin.sequences  forEach kotlin.sequences  map kotlin.sequences  filter kotlin.text  find kotlin.text  firstOrNull kotlin.text  forEach kotlin.text  isEmpty kotlin.text  isLowerCase kotlin.text  
isNotBlank kotlin.text  map kotlin.text  random kotlin.text  removePrefix kotlin.text  replaceFirstChar kotlin.text   replaceFirstCharWithCharSequence kotlin.text  set kotlin.text  
startsWith kotlin.text  	titlecase kotlin.text  toInt kotlin.text  
trimMargin kotlin.text  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Delay kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  withContext kotlinx.coroutines  Constraints !kotlinx.coroutines.CoroutineScope  DefaultWidgetConfiguration !kotlinx.coroutines.CoroutineScope  ExistingPeriodicWorkPolicy !kotlinx.coroutines.CoroutineScope  GlanceAppWidgetManager !kotlinx.coroutines.CoroutineScope  NetworkType !kotlinx.coroutines.CoroutineScope  Pair !kotlinx.coroutines.CoroutineScope  PeriodicWorkRequestBuilder !kotlinx.coroutines.CoroutineScope  Result !kotlinx.coroutines.CoroutineScope  SampleWeatherData !kotlinx.coroutines.CoroutineScope  TimeUnit !kotlinx.coroutines.CoroutineScope  WEATHER_UPDATE_WORK !kotlinx.coroutines.CoroutineScope  WorkManager !kotlinx.coroutines.CoroutineScope  
_isLoading !kotlinx.coroutines.CoroutineScope  _searchError !kotlinx.coroutines.CoroutineScope  applicationContext !kotlinx.coroutines.CoroutineScope  configurationUseCase !kotlinx.coroutines.CoroutineScope  context !kotlinx.coroutines.CoroutineScope  create !kotlinx.coroutines.CoroutineScope  first !kotlinx.coroutines.CoroutineScope  firstOrNull !kotlinx.coroutines.CoroutineScope  forEach !kotlinx.coroutines.CoroutineScope  getInstance !kotlinx.coroutines.CoroutineScope  	javaClass !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  println !kotlinx.coroutines.CoroutineScope  	updateAll !kotlinx.coroutines.CoroutineScope  updateAppWidgetState !kotlinx.coroutines.CoroutineScope  weatherRepository !kotlinx.coroutines.CoroutineScope  
weatherWidget !kotlinx.coroutines.CoroutineScope  widgetConfigurationUseCase !kotlinx.coroutines.CoroutineScope  
widgetManager !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  Flow kotlinx.coroutines.flow  
FlowCollector kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  SharingStarted kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  catch kotlinx.coroutines.flow  combine kotlinx.coroutines.flow  first kotlinx.coroutines.flow  firstOrNull kotlinx.coroutines.flow  map kotlinx.coroutines.flow  stateIn kotlinx.coroutines.flow  catch kotlinx.coroutines.flow.Flow  collect kotlinx.coroutines.flow.Flow  collectAsState kotlinx.coroutines.flow.Flow  first kotlinx.coroutines.flow.Flow  firstOrNull kotlinx.coroutines.flow.Flow  map kotlinx.coroutines.flow.Flow  stateIn kotlinx.coroutines.flow.Flow  <SAM-CONSTRUCTOR> %kotlinx.coroutines.flow.FlowCollector  emit %kotlinx.coroutines.flow.FlowCollector  emptyPreferences %kotlinx.coroutines.flow.FlowCollector  map (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  	Companion &kotlinx.coroutines.flow.SharingStarted  WhileSubscribed &kotlinx.coroutines.flow.SharingStarted  WhileSubscribed 0kotlinx.coroutines.flow.SharingStarted.Companion  collectAsState !kotlinx.coroutines.flow.StateFlow  value !kotlinx.coroutines.flow.StateFlow  Serializable kotlinx.serialization  decodeFromString kotlinx.serialization  encodeToString kotlinx.serialization  Json kotlinx.serialization.json  JsonBuilder kotlinx.serialization.json  decodeFromString kotlinx.serialization.json.Json  encodeToString kotlinx.serialization.json.Json  encodeDefaults &kotlinx.serialization.json.JsonBuilder  ignoreUnknownKeys &kotlinx.serialization.json.JsonBuilder  OkHttpClient okhttp3  Builder okhttp3.OkHttpClient  	Companion okhttp3.OkHttpClient  addInterceptor okhttp3.OkHttpClient.Builder  build okhttp3.OkHttpClient.Builder  connectTimeout okhttp3.OkHttpClient.Builder  readTimeout okhttp3.OkHttpClient.Builder  writeTimeout okhttp3.OkHttpClient.Builder  HttpLoggingInterceptor okhttp3.logging  HttpLoggingInterceptor &okhttp3.logging.HttpLoggingInterceptor  Level &okhttp3.logging.HttpLoggingInterceptor  apply &okhttp3.logging.HttpLoggingInterceptor  level &okhttp3.logging.HttpLoggingInterceptor  BODY ,okhttp3.logging.HttpLoggingInterceptor.Level  androidContext org.koin.android.ext.koin  
androidLogger org.koin.android.ext.koin  
koinViewModel org.koin.androidx.compose  worker !org.koin.androidx.workmanager.dsl  workManagerFactory "org.koin.androidx.workmanager.koin  
koinInject org.koin.compose  KoinApplication 
org.koin.core  Level org.koin.core.KoinApplication  androidContext org.koin.core.KoinApplication  
androidLogger org.koin.core.KoinApplication  	appModule org.koin.core.KoinApplication  databaseModule org.koin.core.KoinApplication  listOf org.koin.core.KoinApplication  modules org.koin.core.KoinApplication  
networkModule org.koin.core.KoinApplication  repositoryModule org.koin.core.KoinApplication  testRepositoryModule org.koin.core.KoinApplication  
useCaseModule org.koin.core.KoinApplication  viewModelModule org.koin.core.KoinApplication  widgetModule org.koin.core.KoinApplication  workManagerFactory org.koin.core.KoinApplication  workerModule org.koin.core.KoinApplication  
KoinComponent org.koin.core.component  inject org.koin.core.component  	startKoin org.koin.core.context  KoinDefinition org.koin.core.definition  Level org.koin.core.logger  ERROR org.koin.core.logger.Level  Module org.koin.core.module  GsonConverterFactory org.koin.core.module.Module  HttpLoggingInterceptor org.koin.core.module.Module  KoinWorkerFactory org.koin.core.module.Module  
MainViewModel org.koin.core.module.Module  MockWeatherRepository org.koin.core.module.Module  OkHttpClient org.koin.core.module.Module  Retrofit org.koin.core.module.Module  Room org.koin.core.module.Module  TimeUnit org.koin.core.module.Module  WeatherApiService org.koin.core.module.Module  WeatherDatabase org.koin.core.module.Module  WeatherRepositoryImpl org.koin.core.module.Module  WeatherUpdateWorker org.koin.core.module.Module  WeatherWidgetManager org.koin.core.module.Module  WeatherWidgetViewModel org.koin.core.module.Module  WidgetConfigurationUseCase org.koin.core.module.Module  WidgetPreferencesManager org.koin.core.module.Module  androidContext org.koin.core.module.Module  apply org.koin.core.module.Module  	dataStore org.koin.core.module.Module  databaseBuilder org.koin.core.module.Module  java org.koin.core.module.Module  single org.koin.core.module.Module  	viewModel org.koin.core.module.Module  worker org.koin.core.module.Module  	viewModel org.koin.core.module.dsl  ParametersHolder org.koin.core.parameter  parametersOf org.koin.core.parameter  Scope org.koin.core.scope  GsonConverterFactory org.koin.core.scope.Scope  HttpLoggingInterceptor org.koin.core.scope.Scope  KoinWorkerFactory org.koin.core.scope.Scope  
MainViewModel org.koin.core.scope.Scope  MockWeatherRepository org.koin.core.scope.Scope  OkHttpClient org.koin.core.scope.Scope  Retrofit org.koin.core.scope.Scope  Room org.koin.core.scope.Scope  TimeUnit org.koin.core.scope.Scope  WeatherApiService org.koin.core.scope.Scope  WeatherDatabase org.koin.core.scope.Scope  WeatherRepositoryImpl org.koin.core.scope.Scope  WeatherUpdateWorker org.koin.core.scope.Scope  WeatherWidgetManager org.koin.core.scope.Scope  WeatherWidgetViewModel org.koin.core.scope.Scope  WidgetConfigurationUseCase org.koin.core.scope.Scope  WidgetPreferencesManager org.koin.core.scope.Scope  androidContext org.koin.core.scope.Scope  apply org.koin.core.scope.Scope  	dataStore org.koin.core.scope.Scope  databaseBuilder org.koin.core.scope.Scope  get org.koin.core.scope.Scope  java org.koin.core.scope.Scope  module org.koin.dsl  Retrofit 	retrofit2  Builder retrofit2.Retrofit  create retrofit2.Retrofit  addConverterFactory retrofit2.Retrofit.Builder  baseUrl retrofit2.Retrofit.Builder  build retrofit2.Retrofit.Builder  client retrofit2.Retrofit.Builder  GsonConverterFactory retrofit2.converter.gson  create -retrofit2.converter.gson.GsonConverterFactory  GET retrofit2.http  Query retrofit2.http                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         