{"logs": [{"outputFile": "com.example.weatherwidget.app-mergeDebugResources-80:/values-v31/values-v31.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\f958aeb604e7c143e9bc08e83e2ba828\\transformed\\glance-appwidget-1.1.1\\res\\values-v31\\values-v31.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "256"}, "to": {"startLines": "29", "startColumns": "4", "startOffsets": "2268", "endLines": "32", "endColumns": "12", "endOffsets": "2469"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\07a29fdb86ef710e85f00096aa41bc4b\\transformed\\glance-1.1.1\\res\\values-v31\\values-v31.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,138,192,255,341,397,462,542,633,715,808,891,980,1070,1151,1243,1324,1404,1493,1580,1662,1753,1833,1921,2009,2090,2180", "endColumns": "82,53,62,85,55,64,79,90,81,92,82,88,89,80,91,80,79,88,86,81,90,79,87,87,80,89,87", "endOffsets": "133,187,250,336,392,457,537,628,710,803,886,975,1065,1146,1238,1319,1399,1488,1575,1657,1748,1828,1916,2004,2085,2175,2263"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\43ef54042a4a5617b6e570bf9425eae0\\transformed\\core-splashscreen-1.0.1\\res\\values-v31\\values-v31.xml", "from": {"startLines": "2,9", "startColumns": "4,4", "startOffsets": "55,473", "endLines": "8,13", "endColumns": "12,12", "endOffsets": "468,697"}, "to": {"startLines": "33,40", "startColumns": "4,4", "startOffsets": "2474,2892", "endLines": "39,44", "endColumns": "12,12", "endOffsets": "2887,3116"}}]}]}