package com.example.weatherwidget.util

import com.example.weatherwidget.R

/**
 * Utility class to map OpenWeatherMap condition codes to appropriate weather icons
 */
object WeatherIconUtils {
    
    /**
     * Maps a weather condition ID from OpenWeatherMap API to a drawable resource ID
     * 
     * @param conditionId The weather condition ID from OpenWeatherMap
     * @param isDay Whether it's daytime (true) or nighttime (false)
     * @return The drawable resource ID for the appropriate weather icon
     */
    fun getWeatherIconResource(conditionId: Int, isDay: <PERSON>olean): Int {
        return when (conditionId) {
            // Clear sky
            in 800..800 -> if (isDay) R.drawable.ic_clear_day else R.drawable.ic_clear_night
            
            // Few clouds, scattered clouds
            in 801..802 -> if (isDay) R.drawable.ic_partly_cloudy_day else R.drawable.ic_partly_cloudy_night
            
            // Broken clouds, overcast clouds
            in 803..804 -> R.drawable.ic_cloudy
            
            // Thunderstorm
            in 200..232 -> R.drawable.ic_thunderstorm
            
            // Drizzle
            in 300..321 -> if (isDay) R.drawable.ic_rain_day else R.drawable.ic_rain_night
            
            // Rain
            in 500..531 -> R.drawable.ic_rain
            
            // Snow
            in 600..622 -> R.drawable.ic_snow
            
            // Atmosphere (fog, mist, etc.)
            in 701..781 -> R.drawable.ic_fog
            
            // Default
            else -> if (isDay) R.drawable.ic_clear_day else R.drawable.ic_clear_night
        }
    }
    
    /**
     * Determines if it's daytime based on the current hour
     * 
     * @param hour The current hour (0-23)
     * @return true if it's daytime, false if it's nighttime
     */
    fun isDaytime(hour: Int): Boolean {
        return hour in 6..18
    }
}