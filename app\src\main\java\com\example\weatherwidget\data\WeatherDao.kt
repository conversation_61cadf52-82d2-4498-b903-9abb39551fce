package com.example.weatherwidget.data

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.example.weatherwidget.data.model.WeatherInfo
import kotlinx.coroutines.flow.Flow

@Dao
interface WeatherDao {
    
    @Query("SELECT * FROM weather ORDER BY cityName ASC")
    fun getAllWeatherInfo(): Flow<List<WeatherInfo>>
    
    @Query("SELECT * FROM weather WHERE isFavorite = 1 ORDER BY cityName ASC")
    fun getFavoriteWeatherInfo(): Flow<List<WeatherInfo>>
    
    @Query("SELECT * FROM weather WHERE cityId = :cityId")
    suspend fun getWeatherInfoById(cityId: Long): WeatherInfo?
    
    @Query("SELECT * FROM weather WHERE cityId = :cityId")
    fun getWeatherInfoByIdAsFlow(cityId: Long): Flow<WeatherInfo?>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertWeatherInfo(weatherInfo: WeatherInfo)
    
    @Update
    suspend fun updateWeatherInfo(weatherInfo: WeatherInfo)
    
    @Query("UPDATE weather SET isFavorite = :isFavorite WHERE cityId = :cityId")
    suspend fun updateFavoriteStatus(cityId: Long, isFavorite: Boolean)
    
    @Query("DELETE FROM weather WHERE cityId = :cityId")
    suspend fun deleteWeatherInfo(cityId: Long)
    
    @Query("SELECT COUNT(*) FROM weather")
    suspend fun getWeatherCount(): Int
}