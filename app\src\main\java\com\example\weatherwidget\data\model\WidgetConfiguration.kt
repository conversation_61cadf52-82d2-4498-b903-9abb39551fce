package com.example.weatherwidget.data.model

import kotlinx.serialization.Serializable

/**
 * Comprehensive widget configuration data models
 */

@Serializable
data class WidgetConfiguration(
    val widgetId: Int,
    val theme: WidgetTheme = WidgetTheme.SYSTEM,
    val transparency: Float = 0.0f, // 0.0 to 1.0 (0% to 100% transparency)
    val locationDisplay: LocationDisplayConfig = LocationDisplayConfig(),
    val timeDisplay: TimeDisplayConfig = TimeDisplayConfig(),
    val weatherElements: WeatherElementsConfig = WeatherElementsConfig(),
    val visualCustomization: VisualCustomizationConfig = VisualCustomizationConfig(),
    val updateSettings: UpdateSettingsConfig = UpdateSettingsConfig()
)

@Serializable
enum class WidgetTheme {
    DARK,
    LIGHT,
    BLUE,
    SYSTEM
}

@Serializable
data class LocationDisplayConfig(
    val showCityName: Boolean = true,
    val selectedCityId: Long? = null
)

@Serializable
data class TimeDisplayConfig(
    val showLastUpdated: Boolean = true
)

@Serializable
data class WeatherElementsConfig(
    val showTemperature: Boolean = true,
    val showCondition: Boolean = true,
    val showHumidity: Boolean = true,
    val showWindSpeed: Boolean = true
)

@Serializable
data class VisualCustomizationConfig(
    val iconSize: IconSize = IconSize.MEDIUM,
    val fontSize: FontSize = FontSize.MEDIUM,
    val cornerRadius: CornerRadius = CornerRadius.ROUNDED_16
)

@Serializable
enum class IconSize(val dp: Int) {
    SMALL(16),
    MEDIUM(24),
    LARGE(32)
}

@Serializable
enum class FontSize(val scale: Float) {
    SMALL(0.8f),
    MEDIUM(1.0f),
    LARGE(1.2f)
}

@Serializable
enum class CornerRadius(val dp: Int) {
    SQUARE(0),
    ROUNDED_8(8),
    ROUNDED_16(16)
}

@Serializable
data class UpdateSettingsConfig(
    val refreshRate: RefreshRate = RefreshRate.ONE_HOUR,
    val networkConstraint: NetworkConstraint = NetworkConstraint.ANY
)

@Serializable
enum class RefreshRate(val minutes: Int, val displayName: String) {
    THIRTY_MINUTES(30, "30 minutes"),
    ONE_HOUR(60, "1 hour"),
    TWO_HOURS(120, "2 hours"),
    FOUR_HOURS(240, "4 hours")
}

@Serializable
enum class NetworkConstraint(val displayName: String) {
    ANY("Any network"),
    WIFI_ONLY("Wi-Fi only")
}

/**
 * Default widget configuration
 */
object DefaultWidgetConfiguration {
    fun create(widgetId: Int) = WidgetConfiguration(
        widgetId = widgetId,
        theme = WidgetTheme.SYSTEM,
        transparency = 0.0f,
        locationDisplay = LocationDisplayConfig(
            showCityName = true,
            selectedCityId = null
        ),
        timeDisplay = TimeDisplayConfig(
            showLastUpdated = true
        ),
        weatherElements = WeatherElementsConfig(
            showTemperature = true,
            showCondition = true,
            showHumidity = true,
            showWindSpeed = true
        ),
        visualCustomization = VisualCustomizationConfig(
            iconSize = IconSize.MEDIUM,
            fontSize = FontSize.MEDIUM,
            cornerRadius = CornerRadius.ROUNDED_16
        ),
        updateSettings = UpdateSettingsConfig(
            refreshRate = RefreshRate.ONE_HOUR,
            networkConstraint = NetworkConstraint.ANY
        )
    )
}

/**
 * Widget theme colors and styling
 */
data class WidgetThemeColors(
    val backgroundColor: Long,
    val primaryTextColor: Long,
    val secondaryTextColor: Long,
    val iconTint: Long
)

object WidgetThemes {
    val DARK = WidgetThemeColors(
        backgroundColor = 0xFF1E1E1E,
        primaryTextColor = 0xFFFFFFFF,
        secondaryTextColor = 0xFFB3B3B3,
        iconTint = 0xFFFFFFFF
    )

    val LIGHT = WidgetThemeColors(
        backgroundColor = 0xFFFFFFFF,
        primaryTextColor = 0xFF000000,
        secondaryTextColor = 0xFF666666,
        iconTint = 0xFF000000
    )

    val BLUE = WidgetThemeColors(
        backgroundColor = 0xFF2196F3,
        primaryTextColor = 0xFFFFFFFF,
        secondaryTextColor = 0xFFE3F2FD,
        iconTint = 0xFFFFFFFF
    )

    fun getThemeColors(theme: WidgetTheme, isSystemDark: Boolean): WidgetThemeColors {
        return when (theme) {
            WidgetTheme.DARK -> DARK
            WidgetTheme.LIGHT -> LIGHT
            WidgetTheme.BLUE -> BLUE
            WidgetTheme.SYSTEM -> if (isSystemDark) DARK else LIGHT
        }
    }
}

// Usage in your widget code - convert Long to Color properly:
// ColorProvider(Color(themeColors.secondaryTextColor.toULong()))