package com.example.weatherwidget.widget

import android.appwidget.AppWidgetManager
import android.content.ComponentName
import android.content.Context
import androidx.glance.appwidget.GlanceAppWidget
import androidx.glance.appwidget.GlanceAppWidgetReceiver
import com.example.weatherwidget.domain.usecase.WidgetConfigurationUseCase
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

/**
 * Receiver for the Weather Widget
 * Handles widget updates and other widget-related broadcasts
 */
class WeatherWidgetReceiver : GlanceAppWidgetReceiver(), KoinComponent {

    private val widgetManager: WeatherWidgetManager by inject()
    private val configurationUseCase: WidgetConfigurationUseCase by inject()

    override val glanceAppWidget: GlanceAppWidget
        get() = widgetManager.weatherWidget

    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray
    ) {
        super.onUpdate(context, appWidgetManager, appWidgetIds)

        // Schedule periodic updates for the widget
        widgetManager.scheduleWidgetUpdates()
    }

    override fun onDeleted(context: Context, appWidgetIds: IntArray) {
        super.onDeleted(context, appWidgetIds)

        // Delete widget configurations for removed widgets
        CoroutineScope(Dispatchers.IO).launch {
            appWidgetIds.forEach { widgetId ->
                configurationUseCase.deleteWidgetConfiguration(widgetId)
            }
        }

        // If all widgets are removed, cancel the scheduled updates
        val manager = AppWidgetManager.getInstance(context)
        val componentName = ComponentName(context, WeatherWidgetReceiver::class.java)
        val remainingWidgets = manager.getAppWidgetIds(componentName)

        if (remainingWidgets.isEmpty()) {
            widgetManager.cancelWidgetUpdates()
        }
    }

    override fun onEnabled(context: Context) {
        super.onEnabled(context)

        // First widget added, schedule updates
        widgetManager.scheduleWidgetUpdates()
    }

    override fun onDisabled(context: Context) {
        super.onDisabled(context)

        // Last widget removed, cancel updates
        widgetManager.cancelWidgetUpdates()
    }
}