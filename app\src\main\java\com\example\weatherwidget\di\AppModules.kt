package com.example.weatherwidget.di

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.preferencesDataStore
import androidx.room.Room
import androidx.work.WorkerFactory
import com.example.weatherwidget.data.WeatherDatabase
import com.example.weatherwidget.data.api.WeatherApiService
import com.example.weatherwidget.data.repository.MockWeatherRepository
import com.example.weatherwidget.data.repository.WeatherRepository
import com.example.weatherwidget.data.repository.WeatherRepositoryImpl
import com.example.weatherwidget.ui.MainViewModel
import com.example.weatherwidget.widget.WeatherWidgetManager
import com.example.weatherwidget.widget.WeatherWidgetViewModel
import com.example.weatherwidget.worker.KoinWorkerFactory
import com.example.weatherwidget.worker.WeatherUpdateWorker
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import org.koin.android.ext.koin.androidContext
import org.koin.core.module.dsl.viewModel
import org.koin.androidx.workmanager.dsl.worker
import org.koin.dsl.module
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

// DataStore instance at the application level
val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "weather_preferences")

// App Module
val appModule = module {
    // DataStore Preferences
    single { androidContext().dataStore }
}

// Database Module
val databaseModule = module {
    single {
        Room.databaseBuilder(
            androidContext(),
            WeatherDatabase::class.java,
            "weather_database"
        ).build()
    }

    single { get<WeatherDatabase>().weatherDao() }
}

// Network Module
val networkModule = module {
    single {
        HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        }
    }

    single {
        OkHttpClient.Builder()
            .addInterceptor(get<HttpLoggingInterceptor>())
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build()
    }

    single {
        Retrofit.Builder()
            .baseUrl("https://api.openweathermap.org/data/2.5/")
            .client(get())
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }

    single { get<Retrofit>().create(WeatherApiService::class.java) }
}

// Repository Module
val repositoryModule = module {
    single<WeatherRepository> { WeatherRepositoryImpl(get(), get(), get()) }
}

// ViewModel Module
val viewModelModule = module {
    viewModel { MainViewModel(get()) }
    viewModel { WeatherWidgetViewModel(get()) }
}

// Widget Module
val widgetModule = module {
    single { WeatherWidgetManager(get(), get()) }
}

// Worker Module
val workerModule = module {
    single<WorkerFactory> { KoinWorkerFactory() }
    worker { WeatherUpdateWorker(get(), get(), get()) }
}


val testRepositoryModule = module {
    // Use mock repository for testing
    single<WeatherRepository> { MockWeatherRepository() }
}


