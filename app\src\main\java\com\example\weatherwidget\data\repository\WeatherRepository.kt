package com.example.weatherwidget.data.repository

import com.example.weatherwidget.data.model.WeatherInfo
import com.example.weatherwidget.data.model.WidgetConfiguration
import kotlinx.coroutines.flow.Flow

interface WeatherRepository {

    // Weather data operations
    fun getAllWeatherInfo(): Flow<List<WeatherInfo>>
    fun getFavoriteWeatherInfo(): Flow<List<WeatherInfo>>
    fun getWeatherInfoById(cityId: Long): Flow<WeatherInfo?>
    suspend fun refreshWeatherById(cityId: Long): Result<WeatherInfo>
    suspend fun searchWeatherByCity(cityName: String): Result<WeatherInfo>
    suspend fun updateFavoriteStatus(cityId: Long, isFavorite: Boolean)
    suspend fun deleteWeatherInfo(cityId: Long)

    // Widget preferences
    suspend fun getSelectedWidgetCityId(): Long?
    suspend fun setSelectedWidgetCityId(cityId: Long)
    suspend fun getWidgetUpdateFrequencyHours(): Int
    suspend fun setWidgetUpdateFrequencyHours(hours: Int)

    // API key management
    suspend fun getApiKey(): String
    suspend fun setApiKey(apiKey: String)

    // Widget configuration management
    fun getWidgetConfiguration(widgetId: Int): Flow<WidgetConfiguration>
    suspend fun saveWidgetConfiguration(config: WidgetConfiguration)
    suspend fun deleteWidgetConfiguration(widgetId: Int)
    fun getAllWidgetConfigurations(): Flow<Map<Int, WidgetConfiguration>>
}