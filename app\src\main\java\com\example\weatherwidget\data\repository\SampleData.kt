package com.example.weatherwidget.data.repository

import com.example.weatherwidget.data.model.WeatherInfo
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.map

/**
 * Sample weather data for UI testing
 * Contains 20 diverse weather conditions from different cities worldwide
 */
object SampleWeatherData {

    private val currentTime = System.currentTimeMillis()

    val sampleWeatherList = listOf(
        WeatherInfo(
            cityId = 1L,
            cityName = "New York",
            country = "US",
            temperature = 22.5,
            feelsLike = 25.0,
            humidity = 65,
            pressure = 1013,
            windSpeed = 8.2,
            windDirection = 180,
            weatherCondition = "Clear",
            weatherDescription = "clear sky",
            weatherIconCode = "01d",
            lastUpdated = currentTime - 300000, // 5 minutes ago
            isFavorite = true
        ),
        WeatherInfo(
            cityId = 2L,
            cityName = "London",
            country = "GB",
            temperature = 15.8,
            feelsLike = 13.2,
            humidity = 78,
            pressure = 1008,
            windSpeed = 12.5,
            windDirection = 225,
            weatherCondition = "Rain",
            weatherDescription = "light rain",
            weatherIconCode = "10d",
            lastUpdated = currentTime - 600000, // 10 minutes ago
            isFavorite = true
        ),
        WeatherInfo(
            cityId = 3L,
            cityName = "Tokyo",
            country = "JP",
            temperature = 28.3,
            feelsLike = 32.1,
            humidity = 82,
            pressure = 1015,
            windSpeed = 5.4,
            windDirection = 90,
            weatherCondition = "Clouds",
            weatherDescription = "overcast clouds",
            weatherIconCode = "04d",
            lastUpdated = currentTime - 180000, // 3 minutes ago
            isFavorite = false
        ),
        WeatherInfo(
            cityId = 4L,
            cityName = "Dubai",
            country = "AE",
            temperature = 42.7,
            feelsLike = 48.5,
            humidity = 35,
            pressure = 1010,
            windSpeed = 15.8,
            windDirection = 315,
            weatherCondition = "Clear",
            weatherDescription = "clear sky",
            weatherIconCode = "01d",
            lastUpdated = currentTime - 900000, // 15 minutes ago
            isFavorite = false
        ),
        WeatherInfo(
            cityId = 5L,
            cityName = "Moscow",
            country = "RU",
            temperature = -8.2,
            feelsLike = -15.3,
            humidity = 71,
            pressure = 1025,
            windSpeed = 18.7,
            windDirection = 45,
            weatherCondition = "Snow",
            weatherDescription = "heavy snow",
            weatherIconCode = "13d",
            lastUpdated = currentTime - 450000, // 7.5 minutes ago
            isFavorite = true
        ),
        WeatherInfo(
            cityId = 6L,
            cityName = "Sydney",
            country = "AU",
            temperature = 26.4,
            feelsLike = 24.8,
            humidity = 58,
            pressure = 1018,
            windSpeed = 22.3,
            windDirection = 270,
            weatherCondition = "Clouds",
            weatherDescription = "few clouds",
            weatherIconCode = "02d",
            lastUpdated = currentTime - 720000, // 12 minutes ago
            isFavorite = false
        ),
        WeatherInfo(
            cityId = 7L,
            cityName = "Mumbai",
            country = "IN",
            temperature = 34.8,
            feelsLike = 41.2,
            humidity = 89,
            pressure = 1002,
            windSpeed = 7.6,
            windDirection = 135,
            weatherCondition = "Rain",
            weatherDescription = "moderate rain",
            weatherIconCode = "10d",
            lastUpdated = currentTime - 240000, // 4 minutes ago
            isFavorite = true
        ),
        WeatherInfo(
            cityId = 8L,
            cityName = "São Paulo",
            country = "BR",
            temperature = 19.7,
            feelsLike = 18.5,
            humidity = 72,
            pressure = 1016,
            windSpeed = 6.8,
            windDirection = 200,
            weatherCondition = "Drizzle",
            weatherDescription = "light intensity drizzle",
            weatherIconCode = "09d",
            lastUpdated = currentTime - 360000, // 6 minutes ago
            isFavorite = false
        ),
        WeatherInfo(
            cityId = 9L,
            cityName = "Cairo",
            country = "EG",
            temperature = 31.5,
            feelsLike = 35.8,
            humidity = 42,
            pressure = 1012,
            windSpeed = 11.2,
            windDirection = 300,
            weatherCondition = "Dust",
            weatherDescription = "sand/dust whirls",
            weatherIconCode = "50d",
            lastUpdated = currentTime - 540000, // 9 minutes ago
            isFavorite = false
        ),
        WeatherInfo(
            cityId = 10L,
            cityName = "Reykjavik",
            country = "IS",
            temperature = 2.1,
            feelsLike = -3.4,
            humidity = 85,
            pressure = 995,
            windSpeed = 25.6,
            windDirection = 315,
            weatherCondition = "Clouds",
            weatherDescription = "broken clouds",
            weatherIconCode = "03d",
            lastUpdated = currentTime - 120000, // 2 minutes ago
            isFavorite = true
        ),
        WeatherInfo(
            cityId = 11L,
            cityName = "Singapore",
            country = "SG",
            temperature = 30.2,
            feelsLike = 36.7,
            humidity = 84,
            pressure = 1009,
            windSpeed = 3.1,
            windDirection = 90,
            weatherCondition = "Thunderstorm",
            weatherDescription = "thunderstorm with light rain",
            weatherIconCode = "11d",
            lastUpdated = currentTime - 780000, // 13 minutes ago
            isFavorite = false
        ),
        WeatherInfo(
            cityId = 12L,
            cityName = "Vancouver",
            country = "CA",
            temperature = 12.6,
            feelsLike = 10.8,
            humidity = 76,
            pressure = 1020,
            windSpeed = 9.4,
            windDirection = 250,
            weatherCondition = "Mist",
            weatherDescription = "mist",
            weatherIconCode = "50d",
            lastUpdated = currentTime - 660000, // 11 minutes ago
            isFavorite = true
        ),
        WeatherInfo(
            cityId = 13L,
            cityName = "Bangkok",
            country = "TH",
            temperature = 33.9,
            feelsLike = 39.5,
            humidity = 77,
            pressure = 1007,
            windSpeed = 4.7,
            windDirection = 120,
            weatherCondition = "Clouds",
            weatherDescription = "scattered clouds",
            weatherIconCode = "03d",
            lastUpdated = currentTime - 420000, // 7 minutes ago
            isFavorite = false
        ),
        WeatherInfo(
            cityId = 14L,
            cityName = "Cape Town",
            country = "ZA",
            temperature = 18.3,
            feelsLike = 16.7,
            humidity = 68,
            pressure = 1024,
            windSpeed = 28.4,
            windDirection = 180,
            weatherCondition = "Clear",
            weatherDescription = "clear sky",
            weatherIconCode = "01d",
            lastUpdated = currentTime - 840000, // 14 minutes ago
            isFavorite = false
        ),
        WeatherInfo(
            cityId = 15L,
            cityName = "Stockholm",
            country = "SE",
            temperature = 8.7,
            feelsLike = 5.2,
            humidity = 73,
            pressure = 1017,
            windSpeed = 14.6,
            windDirection = 200,
            weatherCondition = "Rain",
            weatherDescription = "heavy intensity rain",
            weatherIconCode = "10d",
            lastUpdated = currentTime - 210000, // 3.5 minutes ago
            isFavorite = true
        ),
        WeatherInfo(
            cityId = 16L,
            cityName = "Mexico City",
            country = "MX",
            temperature = 23.4,
            feelsLike = 22.8,
            humidity = 55,
            pressure = 1019,
            windSpeed = 7.2,
            windDirection = 45,
            weatherCondition = "Clouds",
            weatherDescription = "few clouds",
            weatherIconCode = "02d",
            lastUpdated = currentTime - 480000, // 8 minutes ago
            isFavorite = false
        ),
        WeatherInfo(
            cityId = 17L,
            cityName = "Seoul",
            country = "KR",
            temperature = 16.9,
            feelsLike = 14.3,
            humidity = 67,
            pressure = 1021,
            windSpeed = 12.8,
            windDirection = 270,
            weatherCondition = "Haze",
            weatherDescription = "haze",
            weatherIconCode = "50d",
            lastUpdated = currentTime - 330000, // 5.5 minutes ago
            isFavorite = false
        ),
        WeatherInfo(
            cityId = 18L,
            cityName = "Buenos Aires",
            country = "AR",
            temperature = 21.1,
            feelsLike = 19.6,
            humidity = 61,
            pressure = 1014,
            windSpeed = 16.3,
            windDirection = 150,
            weatherCondition = "Clear",
            weatherDescription = "clear sky",
            weatherIconCode = "01d",
            lastUpdated = currentTime - 600000, // 10 minutes ago
            isFavorite = true
        ),
        WeatherInfo(
            cityId = 19L,
            cityName = "Jakarta",
            country = "ID",
            temperature = 29.8,
            feelsLike = 34.2,
            humidity = 81,
            pressure = 1008,
            windSpeed = 8.9,
            windDirection = 60,
            weatherCondition = "Rain",
            weatherDescription = "shower rain",
            weatherIconCode = "09d",
            lastUpdated = currentTime - 750000, // 12.5 minutes ago
            isFavorite = false
        ),
        WeatherInfo(
            cityId = 20L,
            cityName = "Oslo",
            country = "NO",
            temperature = 5.4,
            feelsLike = 1.8,
            humidity = 79,
            pressure = 1011,
            windSpeed = 19.7,
            windDirection = 315,
            weatherCondition = "Snow",
            weatherDescription = "light snow",
            weatherIconCode = "13d",
            lastUpdated = currentTime - 150000, // 2.5 minutes ago
            isFavorite = true
        )
    )
}

/**
 * Extension function to get sample data for testing
 */
fun WeatherRepository.getSampleData(): List<WeatherInfo> = SampleWeatherData.sampleWeatherList

/**
 * Mock repository implementation for testing UI
 */
class MockWeatherRepository : WeatherRepository {

    private val _weatherData = MutableStateFlow(SampleWeatherData.sampleWeatherList)
    private var selectedWidgetCityId: Long? = 1L
    private var widgetUpdateFrequency: Int = 4
    private var apiKey: String = "test_api_key"

    override fun getAllWeatherInfo(): Flow<List<WeatherInfo>> = _weatherData

    override fun getFavoriteWeatherInfo(): Flow<List<WeatherInfo>> =
        _weatherData.map { list -> list.filter { it.isFavorite } }

    override fun getWeatherInfoById(cityId: Long): Flow<WeatherInfo?> =
        _weatherData.map { list -> list.find { it.cityId == cityId } }

    override suspend fun refreshWeatherById(cityId: Long): Result<WeatherInfo> {
        // Simulate network delay
        delay(1000)
        val weather = _weatherData.value.find { it.cityId == cityId }
        return if (weather != null) {
            val updated = weather.copy(lastUpdated = System.currentTimeMillis())
            _weatherData.value = _weatherData.value.map {
                if (it.cityId == cityId) updated else it
            }
            Result.success(updated)
        } else {
            Result.failure(Exception("Weather not found"))
        }
    }

    override suspend fun searchWeatherByCity(cityName: String): Result<WeatherInfo> {
        // Simulate network delay
        delay(1500)
        return Result.success(
            WeatherInfo(
                cityId = System.currentTimeMillis(),
                cityName = cityName,
                country = "XX",
                temperature = (15..35).random().toDouble(),
                feelsLike = (15..35).random().toDouble(),
                humidity = (40..90).random(),
                pressure = (990..1030).random(),
                windSpeed = (0..25).random().toDouble(),
                windDirection = (0..360).random(),
                weatherCondition = listOf("Clear", "Clouds", "Rain").random(),
                weatherDescription = "test weather",
                weatherIconCode = "01d"
            )
        )
    }

    override suspend fun updateFavoriteStatus(cityId: Long, isFavorite: Boolean) {
        _weatherData.value = _weatherData.value.map {
            if (it.cityId == cityId) it.copy(isFavorite = isFavorite) else it
        }
    }

    override suspend fun deleteWeatherInfo(cityId: Long) {
        _weatherData.value = _weatherData.value.filter { it.cityId != cityId }
    }

    override suspend fun getSelectedWidgetCityId(): Long? = selectedWidgetCityId
    override suspend fun setSelectedWidgetCityId(cityId: Long) {
        selectedWidgetCityId = cityId
    }

    override suspend fun getWidgetUpdateFrequencyHours(): Int = widgetUpdateFrequency
    override suspend fun setWidgetUpdateFrequencyHours(hours: Int) {
        widgetUpdateFrequency = hours
    }

    override suspend fun getApiKey(): String = apiKey
    override suspend fun setApiKey(apiKey: String) {
        this.apiKey = apiKey
    }
}