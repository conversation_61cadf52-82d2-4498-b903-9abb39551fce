{"logs": [{"outputFile": "com.example.weatherwidget.app-mergeDebugResources-81:/values-v23/values-v23.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\a6d2b7e5ecb18270abb575bfe8bf3041\\transformed\\appcompat-1.7.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,3,4,5,6,20,34,35,36,39,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,325,400,487,1225,1975,2094,2221,2443,2667,2782,2889,3002", "endLines": "2,3,4,5,19,33,34,35,38,42,43,44,45,49", "endColumns": "134,134,74,86,12,12,118,126,12,12,114,106,112,12", "endOffsets": "185,320,395,482,1220,1970,2089,2216,2438,2662,2777,2884,2997,3227"}, "to": {"startLines": "5,6,7,8,9,23,37,38,39,42,46,47,48,49", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "240,375,510,585,672,1410,2160,2279,2406,2628,2852,2967,3074,3187", "endLines": "5,6,7,8,22,36,37,38,41,45,46,47,48,52", "endColumns": "134,134,74,86,12,12,118,126,12,12,114,106,112,12", "endOffsets": "370,505,580,667,1405,2155,2274,2401,2623,2847,2962,3069,3182,3412"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\ea6cdc6a4f354f494fb001d5b96313da\\transformed\\work-runtime-2.10.1\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,121", "endColumns": "65,62", "endOffsets": "116,179"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\f958aeb604e7c143e9bc08e83e2ba828\\transformed\\glance-appwidget-1.1.1\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "55", "endOffsets": "106"}, "to": {"startLines": "4", "startColumns": "4", "startOffsets": "184", "endColumns": "55", "endOffsets": "235"}}]}]}