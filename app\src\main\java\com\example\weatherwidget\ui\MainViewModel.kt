package com.example.weatherwidget.ui

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.weatherwidget.BuildConfig
import com.example.weatherwidget.data.model.WeatherInfo
import com.example.weatherwidget.data.repository.SampleWeatherData
import com.example.weatherwidget.data.repository.WeatherRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

class MainViewModel(
    private val weatherRepository: WeatherRepository
) : ViewModel() {

    // Weather list from repository
    val weatherList: Flow<List<WeatherInfo>> = weatherRepository.getAllWeatherInfo()

    // Loading state
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading

    // Error state
    private val _searchError = MutableStateFlow<String?>(null)
    val searchError: StateFlow<String?> = _searchError

    // UI state for easier compose integration
    val uiState: StateFlow<WeatherUiState> = combine(
        weatherList,
        isLoading,
        searchError
    ) { weatherList, loading, error ->
        WeatherUiState(
            weatherList = weatherList,
            isLoading = loading,
            errorMessage = error
        )
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = WeatherUiState()
    )

    init {
        if (uiState.value.weatherList.isEmpty()) {
            loadSampleData()
        }
    }

    /**
     * Load sample data for testing (Debug only)
     */
    fun loadSampleData() {
        if (BuildConfig.DEBUG) {
            viewModelScope.launch {
                _isLoading.value = true
                try {
                    // If using MockWeatherRepository, it already has sample data
                    // For production repository, you might want to insert sample data
                    SampleWeatherData.sampleWeatherList.forEach { weather ->
                        // This would require adding insertWeatherInfo to your repository
                        // weatherRepository.insertWeatherInfo(weather)
                    }
                } catch (e: Exception) {
                    _searchError.value = "Failed to load sample data: ${e.message}"
                } finally {
                    _isLoading.value = false
                }
            }
        }
    }

    /**
     * Search for a city and add it to the database
     */
    fun searchCity(cityName: String) {
        viewModelScope.launch {
            _isLoading.value = true
            _searchError.value = null

            try {
                val result = weatherRepository.searchWeatherByCity(cityName)
                if (result.isFailure) {
                    _searchError.value = "City not found or network error"
                }
            } catch (e: Exception) {
                _searchError.value = "Error: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Refresh weather data for a specific city
     */
    fun refreshWeather(cityId: Long) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                weatherRepository.refreshWeatherById(cityId)
            } catch (e: Exception) {
                _searchError.value = "Failed to refresh weather: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Refresh all weather data
     */
    fun refreshAllWeather() {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                // Get all weather info and refresh each one
                weatherRepository.getAllWeatherInfo().first().forEach { weather ->
                    try {
                        weatherRepository.refreshWeatherById(weather.cityId)
                    } catch (e: Exception) {
                        // Continue with next item if one fails
                        println("Failed to refresh weather for ${weather.cityName}: ${e.message}")
                    }
                }
            } catch (e: Exception) {
                _searchError.value = "Failed to refresh all weather: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Toggle favorite status for a city
     */
    fun toggleFavorite(cityId: Long, isFavorite: Boolean) {
        viewModelScope.launch {
            try {
                weatherRepository.updateFavoriteStatus(cityId, isFavorite)
            } catch (e: Exception) {
                _searchError.value = "Failed to update favorite: ${e.message}"
            }
        }
    }

    /**
     * Delete weather info for a city
     */
    fun deleteWeather(cityId: Long) {
        viewModelScope.launch {
            try {
                weatherRepository.deleteWeatherInfo(cityId)
            } catch (e: Exception) {
                _searchError.value = "Failed to delete weather: ${e.message}"
            }
        }
    }

    /**
     * Clear error message
     */
    fun clearError() {
        _searchError.value = null
    }
}

/**
 * UI State data class for easier state management
 */
data class WeatherUiState(
    val weatherList: List<WeatherInfo> = emptyList(),
    val isLoading: Boolean = false,
    val errorMessage: String? = null
)

/*
class MainViewModel(
    private val weatherRepository: WeatherRepository
) : ViewModel() {

    // Weather list from repository
    val weatherList: Flow<List<WeatherInfo>> = weatherRepository.getAllWeatherInfo()
    
    // Loading state
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading
    
    // Error state
    private val _searchError = MutableStateFlow<String?>(null)
    val searchError: StateFlow<String?> = _searchError

    */
/**
 * Search for a city and add it to the database
 *//*

    suspend fun searchCity(cityName: String) {
        _isLoading.value = true
        _searchError.value = null
        
        try {
            val result = weatherRepository.searchWeatherByCity(cityName)
            if (result.isFailure) {
                _searchError.value = "City not found or network error"
            }
        } catch (e: Exception) {
            _searchError.value = "Error: ${e.message}"
        } finally {
            _isLoading.value = false
        }
    }

    */
/**
 * Refresh weather data for a specific city
 *//*

    suspend fun refreshWeather(cityId: Long) {
        _isLoading.value = true
        try {
            weatherRepository.refreshWeatherById(cityId)
        } catch (e: Exception) {
            // Handle error
        } finally {
            _isLoading.value = false
        }
    }

    */
/**
 * Refresh all weather data
 *//*

    suspend fun refreshAllWeather() {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                // Get all weather info and refresh each one
                weatherRepository.getAllWeatherInfo().collect { weatherList ->
                    weatherList.forEach { weather ->
                        try {
                            weatherRepository.refreshWeatherById(weather.cityId)
                        } catch (e: Exception) {
                            // Continue with next item if one fails
                        }
                    }
                    _isLoading.value = false
                    // Break after first collection
                    return@collect
                }
            } catch (e: Exception) {
                _isLoading.value = false
            }
        }
    }

    */
/**
 * Toggle favorite status for a city
 *//*

    suspend fun toggleFavorite(cityId: Long, isFavorite: Boolean) {
        weatherRepository.updateFavoriteStatus(cityId, isFavorite)
    }

    */
/**
 * Delete weather info for a city
 *//*

    suspend fun deleteWeather(cityId: Long) {
        weatherRepository.deleteWeatherInfo(cityId)
    }
}*/
