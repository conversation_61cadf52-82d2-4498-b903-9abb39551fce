{"logs": [{"outputFile": "com.example.weatherwidget.app-mergeDebugResources-81:/values-de/values-de.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\26a7be6aa46fc6df32cf278684c813b2\\transformed\\material3-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,186,315,424,553,663,758,870,1014,1132,1288,1373,1478,1573,1675,1793,1919,2029,2165,2302,2437,2616,2744,2867,2995,3120,3216,3314,3434,3563,3663,3768,3870,4011,4159,4265,4367,4447,4543,4638,4758,4844,4933,5034,5114,5200,5300,5406,5501,5602,5690,5799,5900,6004,6142,6231,6336", "endColumns": "130,128,108,128,109,94,111,143,117,155,84,104,94,101,117,125,109,135,136,134,178,127,122,127,124,95,97,119,128,99,104,101,140,147,105,101,79,95,94,119,85,88,100,79,85,99,105,94,100,87,108,100,103,137,88,104,95", "endOffsets": "181,310,419,548,658,753,865,1009,1127,1283,1368,1473,1568,1670,1788,1914,2024,2160,2297,2432,2611,2739,2862,2990,3115,3211,3309,3429,3558,3658,3763,3865,4006,4154,4260,4362,4442,4538,4633,4753,4839,4928,5029,5109,5195,5295,5401,5496,5597,5685,5794,5895,5999,6137,6226,6331,6427"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7094,7225,7354,7463,7592,7702,7797,7909,8053,8171,8327,8412,8517,8612,8714,8832,8958,9068,9204,9341,9476,9655,9783,9906,10034,10159,10255,10353,10473,10602,10702,10807,10909,11050,11198,11304,11406,11486,11582,11677,11797,11883,11972,12073,12153,12239,12339,12445,12540,12641,12729,12838,12939,13043,13181,13270,13375", "endColumns": "130,128,108,128,109,94,111,143,117,155,84,104,94,101,117,125,109,135,136,134,178,127,122,127,124,95,97,119,128,99,104,101,140,147,105,101,79,95,94,119,85,88,100,79,85,99,105,94,100,87,108,100,103,137,88,104,95", "endOffsets": "7220,7349,7458,7587,7697,7792,7904,8048,8166,8322,8407,8512,8607,8709,8827,8953,9063,9199,9336,9471,9650,9778,9901,10029,10154,10250,10348,10468,10597,10697,10802,10904,11045,11193,11299,11401,11481,11577,11672,11792,11878,11967,12068,12148,12234,12334,12440,12535,12636,12724,12833,12934,13038,13176,13265,13370,13466"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\f958aeb604e7c143e9bc08e83e2ba828\\transformed\\glance-appwidget-1.1.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,262,383", "endColumns": "206,120,117", "endOffsets": "257,378,496"}, "to": {"startLines": "60,61,62", "startColumns": "4,4,4", "startOffsets": "6471,6678,6799", "endColumns": "206,120,117", "endOffsets": "6673,6794,6912"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\821e8c1a7433219b3163b17b67af0011\\transformed\\material-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "93", "endOffsets": "144"}, "to": {"startLines": "122", "startColumns": "4", "startOffsets": "13471", "endColumns": "93", "endOffsets": "13560"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\30287725489871af330b7aa82a2b9f1f\\transformed\\core-1.16.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,455,563,668,786", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "148,250,350,450,558,663,781,882"}, "to": {"startLines": "30,31,32,33,34,35,36,133", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2920,3018,3120,3220,3320,3428,3533,14371", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "3013,3115,3215,3315,3423,3528,3646,14467"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\6bdbe06209508b26c4e38693a2ab85d8\\transformed\\play-services-basement-18.4.0\\res\\values-de\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "47", "startColumns": "4", "startOffsets": "4897", "endColumns": "144", "endOffsets": "5037"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\06d808385d814fc5793833c3be2ca602\\transformed\\foundation-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,143,230", "endColumns": "87,86,89", "endOffsets": "138,225,315"}, "to": {"startLines": "29,137,138", "startColumns": "4,4,4", "startOffsets": "2832,14737,14824", "endColumns": "87,86,89", "endOffsets": "2915,14819,14909"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\99eb3aee547f429176c0b3577d528a52\\transformed\\play-services-base-18.5.0\\res\\values-de\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,458,582,690,864,991,1108,1223,1399,1507,1672,1799,1957,2129,2196,2255", "endColumns": "104,159,123,107,173,126,116,114,175,107,164,126,157,171,66,58,75", "endOffsets": "297,457,581,689,863,990,1107,1222,1398,1506,1671,1798,1956,2128,2195,2254,2330"}, "to": {"startLines": "39,40,41,42,43,44,45,46,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3835,3944,4108,4236,4348,4526,4657,4778,5042,5222,5334,5503,5634,5796,5972,6043,6106", "endColumns": "108,163,127,111,177,130,120,118,179,111,168,130,161,175,70,62,79", "endOffsets": "3939,4103,4231,4343,4521,4652,4773,4892,5217,5329,5498,5629,5791,5967,6038,6101,6181"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\a6d2b7e5ecb18270abb575bfe8bf3041\\transformed\\appcompat-1.7.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,612,727,805,880,972,1066,1162,1263,1370,1470,1574,1672,1770,1867,1949,2060,2162,2260,2367,2470,2574,2730,2832", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "205,303,415,501,607,722,800,875,967,1061,1157,1258,1365,1465,1569,1667,1765,1862,1944,2055,2157,2255,2362,2465,2569,2725,2827,2909"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,612,727,805,880,972,1066,1162,1263,1370,1470,1574,1672,1770,1867,1949,2060,2162,2260,2367,2470,2574,2730,13907", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "205,303,415,501,607,722,800,875,967,1061,1157,1258,1365,1465,1569,1667,1765,1862,1944,2055,2157,2255,2362,2465,2569,2725,2827,13984"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\dcea319fdd3ad3c36ece65fd708a73da\\transformed\\ui-release\\res\\values-de\\values-de.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "194,290,378,476,576,663,748,840,929,1017,1098,1182,1257,1347,1422,1494,1564,1643,1709", "endColumns": "95,87,97,99,86,84,91,88,87,80,83,74,89,74,71,69,78,65,119", "endOffsets": "285,373,471,571,658,743,835,924,1012,1093,1177,1252,1342,1417,1489,1559,1638,1704,1824"}, "to": {"startLines": "37,38,57,58,59,63,64,123,124,125,126,128,129,130,131,132,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3651,3747,6186,6284,6384,6917,7002,13565,13654,13742,13823,13989,14064,14154,14229,14301,14472,14551,14617", "endColumns": "95,87,97,99,86,84,91,88,87,80,83,74,89,74,71,69,78,65,119", "endOffsets": "3742,3830,6279,6379,6466,6997,7089,13649,13737,13818,13902,14059,14149,14224,14296,14366,14546,14612,14732"}}]}]}