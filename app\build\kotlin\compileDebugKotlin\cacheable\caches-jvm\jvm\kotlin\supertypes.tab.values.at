/ Header Record For PersistentHashMapValueStorage* )com.example.weatherwidget.data.WeatherDao/ .com.example.weatherwidget.data.WeatherDatabase= android.app.Application$androidx.work.Configuration.Provider androidx.room.RoomDatabase3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum kotlin.Enum kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum kotlin.Enum< ;com.example.weatherwidget.data.repository.WeatherRepository< ;com.example.weatherwidget.data.repository.WeatherRepository$ #androidx.activity.ComponentActivity androidx.lifecycle.ViewModelP )androidx.glance.appwidget.GlanceAppWidget%org.koin.core.component.KoinComponent0 /androidx.glance.appwidget.action.ActionCallback$ #androidx.activity.ComponentActivityX 1androidx.glance.appwidget.GlanceAppWidgetReceiver%org.koin.core.component.KoinComponent androidx.lifecycle.ViewModelB androidx.work.WorkerFactory%org.koin.core.component.KoinComponentD androidx.work.CoroutineWorker%org.koin.core.component.KoinComponent* )com.example.weatherwidget.data.WeatherDao/ .com.example.weatherwidget.data.WeatherDatabase< ;com.example.weatherwidget.data.repository.WeatherRepositoryP )androidx.glance.appwidget.GlanceAppWidget%org.koin.core.component.KoinComponent0 /androidx.glance.appwidget.action.ActionCallback androidx.room.RoomDatabase< ;com.example.weatherwidget.data.repository.WeatherRepository$ #androidx.activity.ComponentActivity androidx.lifecycle.ViewModel$ #androidx.activity.ComponentActivity androidx.lifecycle.ViewModelD androidx.work.CoroutineWorker%org.koin.core.component.KoinComponent