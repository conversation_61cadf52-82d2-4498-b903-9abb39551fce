{"src\\main\\java\\com\\example\\weatherwidget\\ui\\MainViewModel.kt": ["uiState:com.example.weatherwidget.ui.MainViewModel", "refreshAllWeather:com.example.weatherwidget.ui.MainViewModel", "deleteWeather:com.example.weatherwidget.ui.MainViewModel", "component2:com.example.weatherwidget.ui.WeatherUiState", "loadSampleData:com.example.weatherwidget.ui.MainViewModel", "component3:com.example.weatherwidget.ui.WeatherUiState", "searchError:com.example.weatherwidget.ui.MainViewModel", "weatherRepository:com.example.weatherwidget.ui.MainViewModel", "isLoading:com.example.weatherwidget.ui.WeatherUiState", "weatherList:com.example.weatherwidget.ui.MainViewModel", "_searchError:com.example.weatherwidget.ui.MainViewModel", "WeatherUiState:com.example.weatherwidget.ui", "weatherList:com.example.weatherwidget.ui.WeatherUiState", "clearError:com.example.weatherwidget.ui.MainViewModel", "component1:com.example.weatherwidget.ui.WeatherUiState", "refreshWeather:com.example.weatherwidget.ui.MainViewModel", "errorMessage:com.example.weatherwidget.ui.WeatherUiState", "isLoading:com.example.weatherwidget.ui.MainViewModel", "_isLoading:com.example.weatherwidget.ui.MainViewModel", "copy:com.example.weatherwidget.ui.WeatherUiState", "searchCity:com.example.weatherwidget.ui.MainViewModel", "MainViewModel:com.example.weatherwidget.ui", "toggleFavorite:com.example.weatherwidget.ui.MainViewModel"], "src\\main\\java\\com\\example\\weatherwidget\\data\\model\\WeatherModels.kt": ["component10:com.example.weatherwidget.data.model.WeatherInfo", "component3:com.example.weatherwidget.data.model.Main", "feelsLike:com.example.weatherwidget.data.model.WeatherInfo", "icon:com.example.weatherwidget.data.model.Weather", "component3:com.example.weatherwidget.data.model.Weather", "component9:com.example.weatherwidget.data.model.WeatherInfo", "component14:com.example.weatherwidget.data.model.WeatherInfo", "windSpeed:com.example.weatherwidget.data.model.WeatherInfo", "component5:com.example.weatherwidget.data.model.WeatherInfo", "main:com.example.weatherwidget.data.model.Weather", "component1:com.example.weatherwidget.data.model.WeatherInfo", "main:com.example.weatherwidget.data.model.WeatherResponse", "wind:com.example.weatherwidget.data.model.WeatherResponse", "windDirection:com.example.weatherwidget.data.model.WeatherInfo", "country:com.example.weatherwidget.data.model.WeatherInfo", "id:com.example.weatherwidget.data.model.Weather", "country:com.example.weatherwidget.data.model.Sys", "sys:com.example.weatherwidget.data.model.WeatherResponse", "temp:com.example.weatherwidget.data.model.Main", "component1:com.example.weatherwidget.data.model.Wind", "component4:com.example.weatherwidget.data.model.WeatherResponse", "id:com.example.weatherwidget.data.model.WeatherResponse", "component4:com.example.weatherwidget.data.model.Main", "pressure:com.example.weatherwidget.data.model.WeatherInfo", "component2:com.example.weatherwidget.data.model.Weather", "isFavorite:com.example.weatherwidget.data.model.WeatherInfo", "component6:com.example.weatherwidget.data.model.WeatherInfo", "component11:com.example.weatherwidget.data.model.WeatherInfo", "Wind:com.example.weatherwidget.data.model", "description:com.example.weatherwidget.data.model.Weather", "name:com.example.weatherwidget.data.model.WeatherResponse", "component2:com.example.weatherwidget.data.model.WeatherInfo", "weatherIconCode:com.example.weatherwidget.data.model.WeatherInfo", "cityId:com.example.weatherwidget.data.model.WeatherInfo", "humidity:com.example.weatherwidget.data.model.Main", "lastUpdated:com.example.weatherwidget.data.model.WeatherInfo", "copy:com.example.weatherwidget.data.model.Main", "weather:com.example.weatherwidget.data.model.WeatherResponse", "component3:com.example.weatherwidget.data.model.WeatherResponse", "component2:com.example.weatherwidget.data.model.Wind", "component1:com.example.weatherwidget.data.model.Weather", "component1:com.example.weatherwidget.data.model.Main", "copy:com.example.weatherwidget.data.model.Sys", "toWeatherInfo:com.example.weatherwidget.data.model", "component7:com.example.weatherwidget.data.model.WeatherInfo", "component12:com.example.weatherwidget.data.model.WeatherInfo", "weatherDescription:com.example.weatherwidget.data.model.WeatherInfo", "Sys:com.example.weatherwidget.data.model", "component3:com.example.weatherwidget.data.model.WeatherInfo", "weatherCondition:com.example.weatherwidget.data.model.WeatherInfo", "copy:com.example.weatherwidget.data.model.WeatherResponse", "copy:com.example.weatherwidget.data.model.Weather", "cityName:com.example.weatherwidget.data.model.WeatherInfo", "component6:com.example.weatherwidget.data.model.WeatherResponse", "deg:com.example.weatherwidget.data.model.Wind", "temperature:com.example.weatherwidget.data.model.WeatherInfo", "component2:com.example.weatherwidget.data.model.WeatherResponse", "copy:com.example.weatherwidget.data.model.Wind", "WeatherResponse:com.example.weatherwidget.data.model", "component2:com.example.weatherwidget.data.model.Main", "component4:com.example.weatherwidget.data.model.Weather", "component8:com.example.weatherwidget.data.model.WeatherInfo", "component13:com.example.weatherwidget.data.model.WeatherInfo", "speed:com.example.weatherwidget.data.model.Wind", "copy:com.example.weatherwidget.data.model.WeatherInfo", "Main:com.example.weatherwidget.data.model", "pressure:com.example.weatherwidget.data.model.Main", "component4:com.example.weatherwidget.data.model.WeatherInfo", "humidity:com.example.weatherwidget.data.model.WeatherInfo", "feelsLike:com.example.weatherwidget.data.model.Main", "component5:com.example.weatherwidget.data.model.WeatherResponse", "WeatherInfo:com.example.weatherwidget.data.model", "component1:com.example.weatherwidget.data.model.WeatherResponse", "component1:com.example.weatherwidget.data.model.Sys", "Weather:com.example.weatherwidget.data.model"], "build\\generated\\ksp\\debug\\kotlin\\com\\example\\weatherwidget\\data\\WeatherDatabase_Impl.kt": ["_weatherDao:com.example.weatherwidget.data.WeatherDatabase_Impl", "createInvalidationTracker:com.example.weatherwidget.data.WeatherDatabase_Impl", "createOpenDelegate:com.example.weatherwidget.data.WeatherDatabase_Impl", "WeatherDatabase_Impl:com.example.weatherwidget.data", "createAutoMigrations:com.example.weatherwidget.data.WeatherDatabase_Impl", "clearAllTables:com.example.weatherwidget.data.WeatherDatabase_Impl", "getRequiredAutoMigrationSpecClasses:com.example.weatherwidget.data.WeatherDatabase_Impl", "weatherDao:com.example.weatherwidget.data.WeatherDatabase_Impl", "getRequiredTypeConverterClasses:com.example.weatherwidget.data.WeatherDatabase_Impl"], "src\\main\\java\\com\\example\\weatherwidget\\worker\\KoinWorkerFactory.kt": ["createWorker:com.example.weatherwidget.worker.KoinWorkerFactory", "KoinWorkerFactory:com.example.weatherwidget.worker"], "src\\main\\java\\com\\example\\weatherwidget\\WeatherApp.kt": ["workManagerConfiguration:com.example.weatherwidget.WeatherApp", "WeatherApp:com.example.weatherwidget", "onCreate:com.example.weatherwidget.WeatherApp"], "src\\main\\java\\com\\example\\weatherwidget\\di\\AppModules.kt": ["workerModule:com.example.weatherwidget.di", "repositoryModule:com.example.weatherwidget.di", "viewModelModule:com.example.weatherwidget.di", "appModule:com.example.weatherwidget.di", "databaseModule:com.example.weatherwidget.di", "dataStore:com.example.weatherwidget.di", "networkModule:com.example.weatherwidget.di", "testRepositoryModule:com.example.weatherwidget.di", "widgetModule:com.example.weatherwidget.di"], "src\\main\\java\\com\\example\\weatherwidget\\widget\\WeatherWidgetViewModel.kt": ["allWeatherInfo:com.example.weatherwidget.widget.WeatherWidgetViewModel", "refreshWeather:com.example.weatherwidget.widget.WeatherWidgetViewModel", "setWidgetCity:com.example.weatherwidget.widget.WeatherWidgetViewModel", "toggleFavorite:com.example.weatherwidget.widget.WeatherWidgetViewModel", "weatherRepository:com.example.weatherwidget.widget.WeatherWidgetViewModel", "setUpdateFrequency:com.example.weatherwidget.widget.WeatherWidgetViewModel", "WeatherWidgetViewModel:com.example.weatherwidget.widget", "favoriteWeatherInfo:com.example.weatherwidget.widget.WeatherWidgetViewModel"], "src\\main\\java\\com\\example\\weatherwidget\\widget\\WeatherWidgetConfigActivity.kt": ["CityItem:com.example.weatherwidget.widget", "WidgetConfigScreen:com.example.weatherwidget.widget", "appWidgetId:com.example.weatherwidget.widget.WeatherWidgetConfigActivity", "onCreate:com.example.weatherwidget.widget.WeatherWidgetConfigActivity", "WeatherWidgetConfigActivity:com.example.weatherwidget.widget"], "src\\main\\java\\com\\example\\weatherwidget\\data\\repository\\WeatherRepository.kt": ["setApiKey:com.example.weatherwidget.data.repository.WeatherRepository", "WeatherRepository:com.example.weatherwidget.data.repository", "getApiKey:com.example.weatherwidget.data.repository.WeatherRepository", "updateFavoriteStatus:com.example.weatherwidget.data.repository.WeatherRepository", "refreshWeatherById:com.example.weatherwidget.data.repository.WeatherRepository", "getWidgetUpdateFrequencyHours:com.example.weatherwidget.data.repository.WeatherRepository", "getAllWeatherInfo:com.example.weatherwidget.data.repository.WeatherRepository", "searchWeatherByCity:com.example.weatherwidget.data.repository.WeatherRepository", "setWidgetUpdateFrequencyHours:com.example.weatherwidget.data.repository.WeatherRepository", "getFavoriteWeatherInfo:com.example.weatherwidget.data.repository.WeatherRepository", "setSelectedWidgetCityId:com.example.weatherwidget.data.repository.WeatherRepository", "getWeatherInfoById:com.example.weatherwidget.data.repository.WeatherRepository", "deleteWeatherInfo:com.example.weatherwidget.data.repository.WeatherRepository", "getSelectedWidgetCityId:com.example.weatherwidget.data.repository.WeatherRepository"], "src\\main\\java\\com\\example\\weatherwidget\\ui\\MainActivity.kt": ["capitalize:com.example.weatherwidget.ui", "WeatherItem:com.example.weatherwidget.ui", "onCreate:com.example.weatherwidget.ui.MainActivity", "MainActivity:com.example.weatherwidget.ui", "formatLastUpdated:com.example.weatherwidget.ui", "MainScreen:com.example.weatherwidget.ui"], "src\\main\\java\\com\\example\\weatherwidget\\data\\api\\WeatherApiService.kt": ["getWeatherByCity:com.example.weatherwidget.data.api.WeatherApiService", "WeatherApiService:com.example.weatherwidget.data.api", "getWeatherByCityId:com.example.weatherwidget.data.api.WeatherApiService"], "build\\generated\\source\\buildConfig\\debug\\com\\example\\weatherwidget\\BuildConfig.java": ["BuildConfig:com.example.weatherwidget", "BUILD_TYPE:com.example.weatherwidget.BuildConfig", "VERSION_CODE:com.example.weatherwidget.BuildConfig", "APPLICATION_ID:com.example.weatherwidget.BuildConfig", "DEBUG:com.example.weatherwidget.BuildConfig", "VERSION_NAME:com.example.weatherwidget.BuildConfig"], "src\\main\\java\\com\\example\\weatherwidget\\data\\WeatherDatabase.kt": ["WeatherDatabase:com.example.weatherwidget.data", "weatherDao:com.example.weatherwidget.data.WeatherDatabase"], "src\\main\\java\\com\\example\\weatherwidget\\worker\\WeatherUpdateWorker.kt": ["doWork:com.example.weatherwidget.worker.WeatherUpdateWorker", "widgetManager:com.example.weatherwidget.worker.WeatherUpdateWorker", "WeatherUpdateWorker:com.example.weatherwidget.worker", "weatherRepository:com.example.weatherwidget.worker.WeatherUpdateWorker"], "src\\main\\java\\com\\example\\weatherwidget\\data\\WidgetPreferencesManager.kt": ["API_KEY:com.example.weatherwidget.data.WidgetPreferencesManager.Companion", "WidgetPreferencesManager:com.example.weatherwidget.data", "saveLastUpdateTime:com.example.weatherwidget.data.WidgetPreferencesManager", "selectedCityIdFlow:com.example.weatherwidget.data.WidgetPreferencesManager", "saveApiKey:com.example.weatherwidget.data.WidgetPreferencesManager", "saveUpdateFrequencyHours:com.example.weatherwidget.data.WidgetPreferencesManager", "Companion:com.example.weatherwidget.data.WidgetPreferencesManager", "SELECTED_CITY_ID:com.example.weatherwidget.data.WidgetPreferencesManager.Companion", "UPDATE_FREQUENCY_HOURS:com.example.weatherwidget.data.WidgetPreferencesManager.Companion", "apiKeyFlow:com.example.weatherwidget.data.WidgetPreferencesManager", "lastUpdateTimeFlow:com.example.weatherwidget.data.WidgetPreferencesManager", "LAST_UPDATE_TIME:com.example.weatherwidget.data.WidgetPreferencesManager.Companion", "context:com.example.weatherwidget.data.WidgetPreferencesManager", "updateFrequencyHoursFlow:com.example.weatherwidget.data.WidgetPreferencesManager", "DEFAULT_UPDATE_FREQUENCY_HOURS:com.example.weatherwidget.data.WidgetPreferencesManager.Companion", "DEFAULT_CITY_ID:com.example.weatherwidget.data.WidgetPreferencesManager.Companion", "saveSelectedCityId:com.example.weatherwidget.data.WidgetPreferencesManager", "dataStore:com.example.weatherwidget.data.WidgetPreferencesManager.Companion"], "build\\generated\\ksp\\debug\\kotlin\\com\\example\\weatherwidget\\data\\WeatherDao_Impl.kt": ["getWeatherInfoById:com.example.weatherwidget.data.WeatherDao_Impl", "getAllWeatherInfo:com.example.weatherwidget.data.WeatherDao_Impl", "updateFavoriteStatus:com.example.weatherwidget.data.WeatherDao_Impl", "insertWeatherInfo:com.example.weatherwidget.data.WeatherDao_Impl", "getWeatherCount:com.example.weatherwidget.data.WeatherDao_Impl", "Companion:com.example.weatherwidget.data.WeatherDao_Impl", "__db:com.example.weatherwidget.data.WeatherDao_Impl", "deleteWeatherInfo:com.example.weatherwidget.data.WeatherDao_Impl", "getWeatherInfoByIdAsFlow:com.example.weatherwidget.data.WeatherDao_Impl", "__updateAdapterOfWeatherInfo:com.example.weatherwidget.data.WeatherDao_Impl", "getFavoriteWeatherInfo:com.example.weatherwidget.data.WeatherDao_Impl", "__insertAdapterOfWeatherInfo:com.example.weatherwidget.data.WeatherDao_Impl", "updateWeatherInfo:com.example.weatherwidget.data.WeatherDao_Impl", "getRequiredConverters:com.example.weatherwidget.data.WeatherDao_Impl.Companion", "WeatherDao_Impl:com.example.weatherwidget.data"], "src\\main\\java\\com\\example\\weatherwidget\\widget\\WeatherWidgetReceiver.kt": ["onDisabled:com.example.weatherwidget.widget.WeatherWidgetReceiver", "WeatherWidgetReceiver:com.example.weatherwidget.widget", "onUpdate:com.example.weatherwidget.widget.WeatherWidgetReceiver", "widgetManager:com.example.weatherwidget.widget.WeatherWidgetReceiver", "onEnabled:com.example.weatherwidget.widget.WeatherWidgetReceiver", "glanceAppWidget:com.example.weatherwidget.widget.WeatherWidgetReceiver", "onDeleted:com.example.weatherwidget.widget.WeatherWidgetReceiver"], "src\\main\\java\\com\\example\\weatherwidget\\data\\repository\\WeatherRepositoryImpl.kt": ["setSelectedWidgetCityId:com.example.weatherwidget.data.repository.WeatherRepositoryImpl", "API_KEY:com.example.weatherwidget.data.repository.WeatherRepositoryImpl.Companion", "getWeatherInfoById:com.example.weatherwidget.data.repository.WeatherRepositoryImpl", "setWidgetUpdateFrequencyHours:com.example.weatherwidget.data.repository.WeatherRepositoryImpl", "getFavoriteWeatherInfo:com.example.weatherwidget.data.repository.WeatherRepositoryImpl", "getApiKey:com.example.weatherwidget.data.repository.WeatherRepositoryImpl", "weatherApiService:com.example.weatherwidget.data.repository.WeatherRepositoryImpl", "refreshWeatherById:com.example.weatherwidget.data.repository.WeatherRepositoryImpl", "deleteWeatherInfo:com.example.weatherwidget.data.repository.WeatherRepositoryImpl", "SELECTED_WIDGET_CITY_ID:com.example.weatherwidget.data.repository.WeatherRepositoryImpl.Companion", "Companion:com.example.weatherwidget.data.repository.WeatherRepositoryImpl", "WeatherRepositoryImpl:com.example.weatherwidget.data.repository", "weatherDao:com.example.weatherwidget.data.repository.WeatherRepositoryImpl", "searchWeatherByCity:com.example.weatherwidget.data.repository.WeatherRepositoryImpl", "setApiKey:com.example.weatherwidget.data.repository.WeatherRepositoryImpl", "DEFAULT_UPDATE_FREQUENCY_HOURS:com.example.weatherwidget.data.repository.WeatherRepositoryImpl.Companion", "updateFavoriteStatus:com.example.weatherwidget.data.repository.WeatherRepositoryImpl", "getWidgetUpdateFrequencyHours:com.example.weatherwidget.data.repository.WeatherRepositoryImpl", "DEFAULT_API_KEY:com.example.weatherwidget.data.repository.WeatherRepositoryImpl.Companion", "dataStore:com.example.weatherwidget.data.repository.WeatherRepositoryImpl", "getSelectedWidgetCityId:com.example.weatherwidget.data.repository.WeatherRepositoryImpl", "getAllWeatherInfo:com.example.weatherwidget.data.repository.WeatherRepositoryImpl", "WIDGET_UPDATE_FREQUENCY:com.example.weatherwidget.data.repository.WeatherRepositoryImpl.Companion"], "src\\main\\java\\com\\example\\weatherwidget\\widget\\WeatherWidget.kt": ["WeatherWidgetContent:com.example.weatherwidget.widget.WeatherWidget", "getWeatherIconResource:com.example.weatherwidget.widget.WeatherWidget", "weatherRepository:com.example.weatherwidget.widget.WeatherWidget", "WeatherWidget:com.example.weatherwidget.widget", "onAction:com.example.weatherwidget.widget.OpenAppAction", "provideGlance:com.example.weatherwidget.widget.WeatherWidget", "capitalize:com.example.weatherwidget.widget.WeatherWidget", "formatLastUpdated:com.example.weatherwidget.widget.WeatherWidget", "OpenAppAction:com.example.weatherwidget.widget"], "src\\main\\java\\com\\example\\weatherwidget\\widget\\WeatherWidgetManager.kt": ["widgetScope:com.example.weatherwidget.widget.WeatherWidgetManager", "WEATHER_UPDATE_WORK:com.example.weatherwidget.widget.WeatherWidgetManager.Companion", "cancelWidgetUpdates:com.example.weatherwidget.widget.WeatherWidgetManager", "context:com.example.weatherwidget.widget.WeatherWidgetManager", "Companion:com.example.weatherwidget.widget.WeatherWidgetManager", "setWidgetCity:com.example.weatherwidget.widget.WeatherWidgetManager", "updateWidgetsNow:com.example.weatherwidget.widget.WeatherWidgetManager", "weatherRepository:com.example.weatherwidget.widget.WeatherWidgetManager", "WeatherWidgetManager:com.example.weatherwidget.widget", "weatherWidget:com.example.weatherwidget.widget.WeatherWidgetManager", "scheduleWidgetUpdates:com.example.weatherwidget.widget.WeatherWidgetManager"], "src\\main\\java\\com\\example\\weatherwidget\\util\\WeatherIconUtils.kt": ["WeatherIconUtils:com.example.weatherwidget.util", "getWeatherIconResource:com.example.weatherwidget.util.WeatherIconUtils", "isDaytime:com.example.weatherwidget.util.WeatherIconUtils"], "src\\main\\java\\com\\example\\weatherwidget\\data\\WeatherDao.kt": ["WeatherDao:com.example.weatherwidget.data", "getFavoriteWeatherInfo:com.example.weatherwidget.data.WeatherDao", "updateFavoriteStatus:com.example.weatherwidget.data.WeatherDao", "getWeatherCount:com.example.weatherwidget.data.WeatherDao", "getWeatherInfoByIdAsFlow:com.example.weatherwidget.data.WeatherDao", "getWeatherInfoById:com.example.weatherwidget.data.WeatherDao", "updateWeatherInfo:com.example.weatherwidget.data.WeatherDao", "insertWeatherInfo:com.example.weatherwidget.data.WeatherDao", "getAllWeatherInfo:com.example.weatherwidget.data.WeatherDao", "deleteWeatherInfo:com.example.weatherwidget.data.WeatherDao"], "src\\main\\java\\com\\example\\weatherwidget\\data\\repository\\SampleData.kt": ["sampleWeatherList:com.example.weatherwidget.data.repository.SampleWeatherData", "selectedWidgetCityId:com.example.weatherwidget.data.repository.MockWeatherRepository", "setWidgetUpdateFrequencyHours:com.example.weatherwidget.data.repository.MockWeatherRepository", "getSelectedWidgetCityId:com.example.weatherwidget.data.repository.MockWeatherRepository", "SampleWeatherData:com.example.weatherwidget.data.repository", "getAllWeatherInfo:com.example.weatherwidget.data.repository.MockWeatherRepository", "getWidgetUpdateFrequencyHours:com.example.weatherwidget.data.repository.MockWeatherRepository", "getFavoriteWeatherInfo:com.example.weatherwidget.data.repository.MockWeatherRepository", "getSampleData:com.example.weatherwidget.data.repository", "currentTime:com.example.weatherwidget.data.repository.SampleWeatherData", "setSelectedWidgetCityId:com.example.weatherwidget.data.repository.MockWeatherRepository", "MockWeatherRepository:com.example.weatherwidget.data.repository", "_weatherData:com.example.weatherwidget.data.repository.MockWeatherRepository", "searchWeatherByCity:com.example.weatherwidget.data.repository.MockWeatherRepository", "apiKey:com.example.weatherwidget.data.repository.MockWeatherRepository", "refreshWeatherById:com.example.weatherwidget.data.repository.MockWeatherRepository", "deleteWeatherInfo:com.example.weatherwidget.data.repository.MockWeatherRepository", "widgetUpdateFrequency:com.example.weatherwidget.data.repository.MockWeatherRepository", "getApiKey:com.example.weatherwidget.data.repository.MockWeatherRepository", "updateFavoriteStatus:com.example.weatherwidget.data.repository.MockWeatherRepository", "getWeatherInfoById:com.example.weatherwidget.data.repository.MockWeatherRepository", "setApiKey:com.example.weatherwidget.data.repository.MockWeatherRepository"]}