{"logs": [{"outputFile": "com.example.weatherwidget.app-mergeDebugResources-80:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\15e33be53af35534a537a00acc2d325c\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "927", "startColumns": "4", "startOffsets": "61599", "endColumns": "82", "endOffsets": "61677"}}, {"source": "C:\\Users\\<USER>\\Pictures\\widget\\app\\src\\main\\res\\values\\dimens.xml", "from": {"startLines": "3,4,33,30,31,32,36,37,38,25,26,24,27,9,8,7,10,11,18,15,20,21,17,14,16,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "127,185,1369,1230,1275,1323,1454,1503,1548,1047,1095,998,1146,365,317,270,412,460,744,598,850,903,696,554,650,797", "endColumns": "57,55,50,44,47,45,48,44,41,47,50,48,47,46,47,46,47,48,52,51,52,55,47,43,45,52", "endOffsets": "180,236,1415,1270,1318,1364,1498,1543,1585,1090,1141,1042,1189,407,360,312,455,504,792,645,898,954,739,593,691,845"}, "to": {"startLines": "227,228,229,230,231,232,233,234,235,255,256,257,258,281,282,283,284,285,294,295,296,297,298,299,300,301", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14947,15005,15061,15112,15157,15205,15251,15300,15345,16874,16922,16973,17022,18562,18609,18657,18704,18752,19254,19307,19359,19412,19468,19516,19560,19606", "endColumns": "57,55,50,44,47,45,48,44,41,47,50,48,47,46,47,46,47,48,52,51,52,55,47,43,45,52", "endOffsets": "15000,15056,15107,15152,15200,15246,15295,15340,15382,16917,16968,17017,17065,18604,18652,18699,18747,18796,19302,19354,19407,19463,19511,19555,19601,19654"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\8fb7dc59e95cf1c12bc84f8770ca52cb\\transformed\\coil-base-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "435", "startColumns": "4", "startOffsets": "25747", "endColumns": "49", "endOffsets": "25792"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\f958aeb604e7c143e9bc08e83e2ba828\\transformed\\glance-appwidget-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,522,525,528,531,532,533,534,535,536,537,538,539,542,543,544,545,546,547,548,549,550,551,552,555,558,561,564,565,566,567,568,572", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,157,236,316,393,471,534,611,686,712,742,772,814,855,895,936,976,1015,1055,1094,1132,1174,1215,1255,1296,1336,1375,1415,1454,1492,1534,1575,1615,1656,1696,1735,1775,1814,1852,1894,1935,1975,2016,2056,2095,2135,2174,2212,2254,2295,2335,2376,2416,2455,2495,2534,2572,2614,2655,2695,2736,2776,2815,2855,2894,2932,2974,3015,3055,3096,3136,3175,3215,3254,3292,3334,3375,3415,3456,3496,3535,3575,3614,3652,3694,3735,3775,3816,3856,3895,3935,3974,4012,4054,4095,4135,4176,4216,4255,4295,4334,4372,4403,4431,4463,4490,4517,4549,4577,4603,4629,4659,4687,4716,4745,4821,4898,4975,5053,5129,5206,5283,5361,5437,5514,5591,5669,5745,5822,5899,5977,6053,6130,6207,6285,6361,6438,6515,6593,6669,6746,6823,6901,6977,7054,7131,7209,7285,7362,7439,7517,7593,7670,7747,7825,7901,7978,8055,8133,8209,8286,8363,8441,8517,8594,8671,8749,8825,8902,8979,9057,9133,9210,9287,9365,9441,9518,9595,9673,9749,9826,9903,9981,10057,10134,10211,10289,10365,10442,10519,10597,10673,10750,10827,10905,10981,11058,11135,11213,11289,11366,11443,11521,11597,11674,11751,11829,11905,11982,12059,12137,12213,12290,12367,12445,12521,12598,12675,12753,12829,12906,12983,13061,13137,13214,13291,13369,13445,13522,13599,13677,13753,13830,13907,13985,14061,14138,14215,14293,14369,14446,14523,14601,14677,14754,14831,14909,14985,15062,15139,15217,15293,15370,15447,15525,15601,15678,15755,15833,15909,15986,16063,16141,16217,16294,16371,16449,16525,16602,16679,16757,16833,16910,16987,17065,17141,17218,17295,17373,17449,17526,17603,17681,17757,17834,17911,17989,18065,18142,18219,18297,18373,18450,18527,18605,18681,18758,18835,18913,18989,19066,19143,19221,19297,19374,19451,19529,19605,19682,19759,19837,19913,19990,20067,20145,20221,20298,20375,20453,20529,20606,20683,20761,20837,20914,20991,21069,21145,21222,21299,21377,21453,21530,21607,21685,21761,21838,21915,21993,22069,22146,22223,22301,22377,22454,22531,22609,22685,22762,22839,22917,22993,23070,23147,23225,23301,23378,23455,23533,23609,23686,23763,23841,23917,23994,24071,24149,24225,24302,24379,24457,24533,24610,24687,24765,24841,24918,24995,25073,25149,25226,25303,25381,25457,25534,25611,25689,25765,25842,25919,25997,26073,26150,26227,26305,26381,26458,26535,26613,26689,26766,26843,26921,26997,27074,27151,27229,27305,27382,27459,27537,27613,27690,27767,27845,27921,27998,28075,28153,28229,28306,28383,28461,28537,28614,28691,28769,28845,28922,28999,29077,29153,29230,29307,29385,29461,29538,29615,29693,29769,29846,29923,30001,30077,30154,30231,30309,30385,30462,30539,30617,30693,30770,30847,30925,31001,31078,31155,31233,31309,31386,31463,31541,31617,31694,31771,31849,31925,32002,32079,32157,32233,32310,32387,32465,32541,32618,32695,32773,32849,32926,33003,33081,33157,33234,33311,33389,33465,33542,33619,33697,33773,33850,33927,34005,34081,34158,34235,34313,34389,34466,34543,34621,34697,34774,34851,34929,35005,35082,35159,35237,35313,35390,35467,35545,35700,35776,35861,36024,36174,36328,36486,36537,36591,36647,36711,36771,36831,36904,36958,37184,37236,37295,37358,37421,37472,37526,37588,37646,37705,37764,37878,38008,38140,38274,38354,38420,38486,38546,38794", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,521,524,527,530,531,532,533,534,535,536,537,538,541,542,543,544,545,546,547,548,549,550,551,554,557,560,563,564,565,566,567,571,574", "endColumns": "56,78,79,76,77,62,76,74,25,29,29,41,40,39,40,39,38,39,38,37,41,40,39,40,39,38,39,38,37,41,40,39,40,39,38,39,38,37,41,40,39,40,39,38,39,38,37,41,40,39,40,39,38,39,38,37,41,40,39,40,39,38,39,38,37,41,40,39,40,39,38,39,38,37,41,40,39,40,39,38,39,38,37,41,40,39,40,39,38,39,38,37,41,40,39,40,39,38,39,38,37,30,27,31,26,26,31,27,25,25,29,27,28,28,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,154,75,84,12,12,12,12,50,53,55,63,59,59,72,53,12,51,58,62,62,50,53,61,57,58,58,12,12,12,12,79,65,65,59,12,24", "endOffsets": "152,231,311,388,466,529,606,681,707,737,767,809,850,890,931,971,1010,1050,1089,1127,1169,1210,1250,1291,1331,1370,1410,1449,1487,1529,1570,1610,1651,1691,1730,1770,1809,1847,1889,1930,1970,2011,2051,2090,2130,2169,2207,2249,2290,2330,2371,2411,2450,2490,2529,2567,2609,2650,2690,2731,2771,2810,2850,2889,2927,2969,3010,3050,3091,3131,3170,3210,3249,3287,3329,3370,3410,3451,3491,3530,3570,3609,3647,3689,3730,3770,3811,3851,3890,3930,3969,4007,4049,4090,4130,4171,4211,4250,4290,4329,4367,4398,4426,4458,4485,4512,4544,4572,4598,4624,4654,4682,4711,4740,4816,4893,4970,5048,5124,5201,5278,5356,5432,5509,5586,5664,5740,5817,5894,5972,6048,6125,6202,6280,6356,6433,6510,6588,6664,6741,6818,6896,6972,7049,7126,7204,7280,7357,7434,7512,7588,7665,7742,7820,7896,7973,8050,8128,8204,8281,8358,8436,8512,8589,8666,8744,8820,8897,8974,9052,9128,9205,9282,9360,9436,9513,9590,9668,9744,9821,9898,9976,10052,10129,10206,10284,10360,10437,10514,10592,10668,10745,10822,10900,10976,11053,11130,11208,11284,11361,11438,11516,11592,11669,11746,11824,11900,11977,12054,12132,12208,12285,12362,12440,12516,12593,12670,12748,12824,12901,12978,13056,13132,13209,13286,13364,13440,13517,13594,13672,13748,13825,13902,13980,14056,14133,14210,14288,14364,14441,14518,14596,14672,14749,14826,14904,14980,15057,15134,15212,15288,15365,15442,15520,15596,15673,15750,15828,15904,15981,16058,16136,16212,16289,16366,16444,16520,16597,16674,16752,16828,16905,16982,17060,17136,17213,17290,17368,17444,17521,17598,17676,17752,17829,17906,17984,18060,18137,18214,18292,18368,18445,18522,18600,18676,18753,18830,18908,18984,19061,19138,19216,19292,19369,19446,19524,19600,19677,19754,19832,19908,19985,20062,20140,20216,20293,20370,20448,20524,20601,20678,20756,20832,20909,20986,21064,21140,21217,21294,21372,21448,21525,21602,21680,21756,21833,21910,21988,22064,22141,22218,22296,22372,22449,22526,22604,22680,22757,22834,22912,22988,23065,23142,23220,23296,23373,23450,23528,23604,23681,23758,23836,23912,23989,24066,24144,24220,24297,24374,24452,24528,24605,24682,24760,24836,24913,24990,25068,25144,25221,25298,25376,25452,25529,25606,25684,25760,25837,25914,25992,26068,26145,26222,26300,26376,26453,26530,26608,26684,26761,26838,26916,26992,27069,27146,27224,27300,27377,27454,27532,27608,27685,27762,27840,27916,27993,28070,28148,28224,28301,28378,28456,28532,28609,28686,28764,28840,28917,28994,29072,29148,29225,29302,29380,29456,29533,29610,29688,29764,29841,29918,29996,30072,30149,30226,30304,30380,30457,30534,30612,30688,30765,30842,30920,30996,31073,31150,31228,31304,31381,31458,31536,31612,31689,31766,31844,31920,31997,32074,32152,32228,32305,32382,32460,32536,32613,32690,32768,32844,32921,32998,33076,33152,33229,33306,33384,33460,33537,33614,33692,33768,33845,33922,34000,34076,34153,34230,34308,34384,34461,34538,34616,34692,34769,34846,34924,35000,35077,35154,35232,35308,35385,35462,35540,35695,35771,35856,36019,36169,36323,36481,36532,36586,36642,36706,36766,36826,36899,36953,37179,37231,37290,37353,37416,37467,37521,37583,37641,37700,37759,37873,38003,38135,38269,38349,38415,38481,38541,38789,38922"}, "to": {"startLines": "18,88,89,90,91,245,246,247,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,438,440,441,453,454,455,457,458,459,460,463,464,465,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,976,977,978,1109,2194,2197,2200,2203,2204,2205,2206,2207,2208,2209,2210,2211,2214,2215,2216,2217,2218,2219,2220,2221,2222,2223,2224,2227,2230,2233,2236,2237,2238,2239,2694,3505", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1111,5943,6022,6102,6179,16048,16111,16188,22061,22087,22117,22147,22189,22230,22270,22311,22351,22390,22430,22469,22507,22549,22590,22630,22671,22711,22750,22790,22829,22867,22909,22950,22990,23031,23071,23110,23150,23189,23227,23269,23310,23350,23391,23431,23470,23510,23549,23587,23629,23670,23710,23751,23791,23830,23870,23909,23947,23989,24030,24070,24111,24151,24190,24230,24269,24307,24349,24390,24430,24471,24511,24550,24590,24629,24667,24709,24750,24790,24831,24871,24910,24950,24989,25027,25069,25110,25150,25191,25231,25270,25310,25349,25387,25429,25470,25510,25551,25591,25630,25670,25709,25910,25998,26026,26604,26631,26658,26732,26760,26786,26812,26953,26981,27010,28933,29009,29086,29163,29241,29317,29394,29471,29549,29625,29702,29779,29857,29933,30010,30087,30165,30241,30318,30395,30473,30549,30626,30703,30781,30857,30934,31011,31089,31165,31242,31319,31397,31473,31550,31627,31705,31781,31858,31935,32013,32089,32166,32243,32321,32397,32474,32551,32629,32705,32782,32859,32937,33013,33090,33167,33245,33321,33398,33475,33553,33629,33706,33783,33861,33937,34014,34091,34169,34245,34322,34399,34477,34553,34630,34707,34785,34861,34938,35015,35093,35169,35246,35323,35401,35477,35554,35631,35709,35785,35862,35939,36017,36093,36170,36247,36325,36401,36478,36555,36633,36709,36786,36863,36941,37017,37094,37171,37249,37325,37402,37479,37557,37633,37710,37787,37865,37941,38018,38095,38173,38249,38326,38403,38481,38557,38634,38711,38789,38865,38942,39019,39097,39173,39250,39327,39405,39481,39558,39635,39713,39789,39866,39943,40021,40097,40174,40251,40329,40405,40482,40559,40637,40713,40790,40867,40945,41021,41098,41175,41253,41329,41406,41483,41561,41637,41714,41791,41869,41945,42022,42099,42177,42253,42330,42407,42485,42561,42638,42715,42793,42869,42946,43023,43101,43177,43254,43331,43409,43485,43562,43639,43717,43793,43870,43947,44025,44101,44178,44255,44333,44409,44486,44563,44641,44717,44794,44871,44949,45025,45102,45179,45257,45333,45410,45487,45565,45641,45718,45795,45873,45949,46026,46103,46181,46257,46334,46411,46489,46565,46642,46719,46797,46873,46950,47027,47105,47181,47258,47335,47413,47489,47566,47643,47721,47797,47874,47951,48029,48105,48182,48259,48337,48413,48490,48567,48645,48721,48798,48875,48953,49029,49106,49183,49261,49337,49414,49491,49569,49645,49722,49799,49877,49953,50030,50107,50185,50261,50338,50415,50493,50569,50646,50723,50801,50877,50954,51031,51109,51185,51262,51339,51417,51493,51570,51647,51725,51801,51878,51955,52033,52109,52186,52263,52341,52417,52494,52571,52649,52725,52802,52879,52957,53033,53110,53187,53265,53341,53418,53495,53573,53649,53726,53803,53881,53957,54034,54111,54189,54265,54342,54419,54497,54573,54650,54727,54805,54881,54958,55035,55113,55189,55266,55343,55421,55497,55574,55651,55729,55805,55882,55959,56037,56113,56190,56267,56345,56421,56498,56575,56653,56729,56806,56883,56961,57037,57114,57191,57269,57345,57422,57499,57577,57653,57730,57807,57885,57961,58038,58115,58193,58269,58346,58423,58501,58577,58654,58731,58809,58885,58962,59039,59117,59193,59270,59347,59425,59501,59578,59655,66033,66188,66264,75193,144069,144219,144373,144531,144582,144636,144692,144756,144816,144876,144949,145003,145229,145281,145340,145403,145466,145517,145571,145633,145691,145750,145809,145923,146053,146185,146319,146399,146465,146531,181266,209556", "endLines": "18,88,89,90,91,245,246,247,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,438,440,441,453,454,455,457,458,459,460,463,464,465,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,976,977,978,1111,2196,2199,2202,2203,2204,2205,2206,2207,2208,2209,2210,2213,2214,2215,2216,2217,2218,2219,2220,2221,2222,2223,2226,2229,2232,2235,2236,2237,2238,2239,2697,3507", "endColumns": "56,78,79,76,77,62,76,74,25,29,29,41,40,39,40,39,38,39,38,37,41,40,39,40,39,38,39,38,37,41,40,39,40,39,38,39,38,37,41,40,39,40,39,38,39,38,37,41,40,39,40,39,38,39,38,37,41,40,39,40,39,38,39,38,37,41,40,39,40,39,38,39,38,37,41,40,39,40,39,38,39,38,37,41,40,39,40,39,38,39,38,37,41,40,39,40,39,38,39,38,37,30,27,31,26,26,31,27,25,25,29,27,28,28,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,75,76,76,77,154,75,84,12,12,12,12,50,53,55,63,59,59,72,53,12,51,58,62,62,50,53,61,57,58,58,12,12,12,12,79,65,65,59,12,24", "endOffsets": "1163,6017,6097,6174,6252,16106,16183,16258,22082,22112,22142,22184,22225,22265,22306,22346,22385,22425,22464,22502,22544,22585,22625,22666,22706,22745,22785,22824,22862,22904,22945,22985,23026,23066,23105,23145,23184,23222,23264,23305,23345,23386,23426,23465,23505,23544,23582,23624,23665,23705,23746,23786,23825,23865,23904,23942,23984,24025,24065,24106,24146,24185,24225,24264,24302,24344,24385,24425,24466,24506,24545,24585,24624,24662,24704,24745,24785,24826,24866,24905,24945,24984,25022,25064,25105,25145,25186,25226,25265,25305,25344,25382,25424,25465,25505,25546,25586,25625,25665,25704,25742,25936,26021,26053,26626,26653,26685,26755,26781,26807,26837,26976,27005,27034,29004,29081,29158,29236,29312,29389,29466,29544,29620,29697,29774,29852,29928,30005,30082,30160,30236,30313,30390,30468,30544,30621,30698,30776,30852,30929,31006,31084,31160,31237,31314,31392,31468,31545,31622,31700,31776,31853,31930,32008,32084,32161,32238,32316,32392,32469,32546,32624,32700,32777,32854,32932,33008,33085,33162,33240,33316,33393,33470,33548,33624,33701,33778,33856,33932,34009,34086,34164,34240,34317,34394,34472,34548,34625,34702,34780,34856,34933,35010,35088,35164,35241,35318,35396,35472,35549,35626,35704,35780,35857,35934,36012,36088,36165,36242,36320,36396,36473,36550,36628,36704,36781,36858,36936,37012,37089,37166,37244,37320,37397,37474,37552,37628,37705,37782,37860,37936,38013,38090,38168,38244,38321,38398,38476,38552,38629,38706,38784,38860,38937,39014,39092,39168,39245,39322,39400,39476,39553,39630,39708,39784,39861,39938,40016,40092,40169,40246,40324,40400,40477,40554,40632,40708,40785,40862,40940,41016,41093,41170,41248,41324,41401,41478,41556,41632,41709,41786,41864,41940,42017,42094,42172,42248,42325,42402,42480,42556,42633,42710,42788,42864,42941,43018,43096,43172,43249,43326,43404,43480,43557,43634,43712,43788,43865,43942,44020,44096,44173,44250,44328,44404,44481,44558,44636,44712,44789,44866,44944,45020,45097,45174,45252,45328,45405,45482,45560,45636,45713,45790,45868,45944,46021,46098,46176,46252,46329,46406,46484,46560,46637,46714,46792,46868,46945,47022,47100,47176,47253,47330,47408,47484,47561,47638,47716,47792,47869,47946,48024,48100,48177,48254,48332,48408,48485,48562,48640,48716,48793,48870,48948,49024,49101,49178,49256,49332,49409,49486,49564,49640,49717,49794,49872,49948,50025,50102,50180,50256,50333,50410,50488,50564,50641,50718,50796,50872,50949,51026,51104,51180,51257,51334,51412,51488,51565,51642,51720,51796,51873,51950,52028,52104,52181,52258,52336,52412,52489,52566,52644,52720,52797,52874,52952,53028,53105,53182,53260,53336,53413,53490,53568,53644,53721,53798,53876,53952,54029,54106,54184,54260,54337,54414,54492,54568,54645,54722,54800,54876,54953,55030,55108,55184,55261,55338,55416,55492,55569,55646,55724,55800,55877,55954,56032,56108,56185,56262,56340,56416,56493,56570,56648,56724,56801,56878,56956,57032,57109,57186,57264,57340,57417,57494,57572,57648,57725,57802,57880,57956,58033,58110,58188,58264,58341,58418,58496,58572,58649,58726,58804,58880,58957,59034,59112,59188,59265,59342,59420,59496,59573,59650,59728,66183,66259,66344,75351,144214,144368,144526,144577,144631,144687,144751,144811,144871,144944,144998,145224,145276,145335,145398,145461,145512,145566,145628,145686,145745,145804,145918,146048,146180,146314,146394,146460,146526,146586,181509,209684"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\0efd6ddfbb17a7f08ddafb44a4eb7f00\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "485", "startColumns": "4", "startOffsets": "28044", "endColumns": "42", "endOffsets": "28082"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\554ea434be603d81f0fb2f0bdd5ecc35\\transformed\\navigation-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "449,2802,3789,3792", "startColumns": "4,4,4,4", "startOffsets": "26388,185037,218592,218707", "endLines": "449,2808,3791,3794", "endColumns": "52,24,24,24", "endOffsets": "26436,185336,218702,218817"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\a6d2b7e5ecb18270abb575bfe8bf3041\\transformed\\appcompat-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "2,3,4,8,13,14,20,21,22,23,24,26,27,30,31,32,33,34,35,36,37,38,39,40,41,52,53,54,55,57,58,59,60,92,93,95,96,97,98,99,100,101,102,103,104,105,106,111,112,114,115,117,118,119,120,121,122,124,125,126,127,131,132,133,134,140,141,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,243,244,248,249,250,251,252,253,254,286,287,288,289,290,291,292,293,337,338,339,340,444,451,452,462,484,492,493,494,495,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,1052,1072,1073,1074,1075,1076,1077,1085,1086,1090,1094,1098,1103,1112,1119,1123,1127,1132,1136,1140,1144,1148,1152,1156,1162,1166,1172,1176,1182,1186,1191,1195,1198,1202,1208,1212,1218,1222,1228,1231,1235,1239,1243,1247,1251,1252,1253,1254,1257,1260,1263,1266,1270,1271,1272,1273,1274,1277,1279,1281,1283,1288,1289,1293,1299,1303,1304,1306,1318,1319,1323,1329,1333,1337,1338,1342,1369,1373,1374,1378,1406,1578,1604,1775,1801,1832,1840,1846,1862,1884,1889,1894,1904,1913,1922,1926,1933,1952,1959,1960,1969,1972,1975,1979,1983,1987,1990,1991,1996,2001,2011,2016,2023,2029,2030,2033,2037,2042,2044,2046,2049,2052,2054,2058,2061,2068,2071,2074,2078,2080,2084,2086,2088,2090,2094,2102,2110,2122,2128,2137,2140,2151,2154,2155,2160,2161,2240,2309,2379,2380,2390,2399,2400,2402,2406,2409,2412,2415,2418,2421,2424,2427,2431,2434,2437,2440,2444,2447,2451,2455,2456,2457,2458,2459,2460,2461,2462,2463,2464,2465,2466,2467,2468,2469,2470,2471,2472,2473,2474,2475,2477,2479,2480,2481,2482,2483,2484,2485,2486,2488,2489,2491,2492,2494,2496,2497,2499,2500,2501,2502,2503,2504,2506,2507,2508,2509,2510,2522,2524,2526,2528,2529,2530,2531,2532,2533,2534,2535,2536,2537,2538,2539,2540,2542,2543,2544,2545,2546,2547,2548,2550,2554,2585,2586,2587,2588,2589,2590,2594,2595,2596,2597,2599,2601,2603,2605,2607,2608,2609,2610,2612,2614,2616,2617,2618,2619,2620,2621,2622,2623,2624,2625,2626,2627,2630,2631,2632,2633,2635,2637,2638,2640,2641,2643,2645,2647,2648,2649,2650,2651,2652,2653,2654,2655,2656,2657,2658,2660,2661,2662,2663,2665,2666,2667,2668,2669,2671,2673,2675,2677,2678,2679,2680,2681,2682,2683,2684,2685,2686,2687,2688,2689,2690,2691,2698,2773,2776,2779,2782,2796,2809,2851,2854,2883,2910,2919,2983,3346,3356,3394,3422,3547,3571,3577,3596,3617,3741,3800,3806,3810,3816,3870,3902,3968,3988,4043,4055,4081", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,250,478,795,850,1229,1293,1363,1424,1499,1616,1693,1931,2016,2098,2174,2250,2327,2405,2511,2617,2696,2776,2833,3693,3767,3842,3907,4015,4075,4136,4208,6257,6324,6432,6491,6550,6609,6668,6727,6781,6835,6888,6942,6996,7050,7325,7399,7526,7599,7720,7791,7863,7935,8008,8065,8172,8245,8319,8393,8613,8685,8758,8828,9153,9213,9498,9567,9636,9706,9780,9856,9920,9997,10073,10150,10215,10284,10361,10436,10505,10573,10650,10716,10777,10874,10939,11008,11107,11178,11237,11295,11352,11411,11475,11546,11618,11690,11762,11834,11901,11969,12037,12096,12159,12223,12313,12404,12464,12530,12597,12663,12733,12797,12850,12917,12978,13045,13158,13216,13279,13344,13409,13484,13557,13629,13673,13720,13766,13815,13876,13937,13998,14060,14124,14188,14252,14317,14380,14440,14501,14567,14626,14686,14748,14819,14879,15875,15961,16263,16353,16440,16528,16610,16693,16783,18801,18853,18911,18956,19022,19086,19143,19200,21782,21839,21887,21936,26175,26508,26555,26907,28012,28411,28475,28537,28597,59733,59807,59877,59955,60009,60079,60164,60212,60258,60319,60382,60448,60512,60583,60646,60711,60775,60836,60897,60949,61022,61096,61165,61240,61314,61388,61529,71471,72622,72700,72790,72878,72974,73064,73646,73735,73982,74263,74515,74800,75356,75833,76055,76277,76553,76780,77010,77240,77470,77700,77927,78346,78572,78997,79227,79655,79874,80157,80365,80496,80723,81149,81374,81801,82022,82447,82567,82843,83144,83468,83759,84073,84210,84341,84446,84688,84855,85059,85267,85538,85650,85762,85867,85984,86198,86344,86484,86570,86918,87006,87252,87670,87919,88001,88099,88756,88856,89108,89532,89787,90147,90236,90473,92497,92739,92841,93094,95250,105931,107447,118142,119670,121427,122053,122473,123734,124999,125255,125491,126038,126532,127137,127335,127915,129283,129658,129776,130314,130471,130667,130940,131196,131366,131507,131571,131936,132303,132979,133243,133581,133934,134028,134214,134520,134782,134907,135034,135273,135484,135603,135796,135973,136428,136609,136731,136990,137103,137290,137392,137499,137628,137903,138411,138907,139784,140078,140648,140797,141529,141701,141785,142121,142213,146591,151822,157193,157255,157833,158417,158508,158621,158850,159010,159162,159333,159499,159668,159835,159998,160241,160411,160584,160755,161029,161228,161433,161763,161847,161943,162039,162137,162237,162339,162441,162543,162645,162747,162847,162943,163055,163184,163307,163438,163569,163667,163781,163875,164015,164149,164245,164357,164457,164573,164669,164781,164881,165021,165157,165321,165451,165609,165759,165900,166044,166179,166291,166441,166569,166697,166833,166965,167095,167225,167337,168235,168381,168525,168663,168729,168819,168895,168999,169089,169191,169299,169407,169507,169587,169679,169777,169887,169939,170017,170123,170215,170319,170429,170551,170714,172468,172548,172648,172738,172848,172938,173179,173273,173379,173471,173571,173683,173797,173913,174029,174123,174237,174349,174451,174571,174693,174775,174879,174999,175125,175223,175317,175405,175517,175633,175755,175867,176042,176158,176244,176336,176448,176572,176639,176765,176833,176961,177105,177233,177302,177397,177512,177625,177724,177833,177944,178055,178156,178261,178361,178491,178582,178705,178799,178911,178997,179101,179197,179285,179403,179507,179611,179737,179825,179933,180033,180123,180233,180317,180419,180503,180557,180621,180727,180813,180923,181007,181514,184130,184248,184363,184443,184804,185341,186745,186823,188167,189528,189916,192759,202812,203150,204821,206178,210538,211289,211551,212066,212445,216723,219004,219233,219384,219599,221099,221949,224975,225719,227850,228190,229501", "endLines": "2,3,4,8,13,14,20,21,22,23,24,26,27,30,31,32,33,34,35,36,37,38,39,40,41,52,53,54,55,57,58,59,60,92,93,95,96,97,98,99,100,101,102,103,104,105,106,111,112,114,115,117,118,119,120,121,122,124,125,126,127,131,132,133,134,140,141,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,243,244,248,249,250,251,252,253,254,286,287,288,289,290,291,292,293,337,338,339,340,444,451,452,462,484,492,493,494,495,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,1052,1072,1073,1074,1075,1076,1084,1085,1089,1093,1097,1102,1108,1118,1122,1126,1131,1135,1139,1143,1147,1151,1155,1161,1165,1171,1175,1181,1185,1190,1194,1197,1201,1207,1211,1217,1221,1227,1230,1234,1238,1242,1246,1250,1251,1252,1253,1256,1259,1262,1265,1269,1270,1271,1272,1273,1276,1278,1280,1282,1287,1288,1292,1298,1302,1303,1305,1317,1318,1322,1328,1332,1333,1337,1341,1368,1372,1373,1377,1405,1577,1603,1774,1800,1831,1839,1845,1861,1883,1888,1893,1903,1912,1921,1925,1932,1951,1958,1959,1968,1971,1974,1978,1982,1986,1989,1990,1995,2000,2010,2015,2022,2028,2029,2032,2036,2041,2043,2045,2048,2051,2053,2057,2060,2067,2070,2073,2077,2079,2083,2085,2087,2089,2093,2101,2109,2121,2127,2136,2139,2150,2153,2154,2159,2160,2165,2308,2378,2379,2389,2398,2399,2401,2405,2408,2411,2414,2417,2420,2423,2426,2430,2433,2436,2439,2443,2446,2450,2454,2455,2456,2457,2458,2459,2460,2461,2462,2463,2464,2465,2466,2467,2468,2469,2470,2471,2472,2473,2474,2476,2478,2479,2480,2481,2482,2483,2484,2485,2487,2488,2490,2491,2493,2495,2496,2498,2499,2500,2501,2502,2503,2505,2506,2507,2508,2509,2510,2523,2525,2527,2528,2529,2530,2531,2532,2533,2534,2535,2536,2537,2538,2539,2541,2542,2543,2544,2545,2546,2547,2549,2553,2557,2585,2586,2587,2588,2589,2593,2594,2595,2596,2598,2600,2602,2604,2606,2607,2608,2609,2611,2613,2615,2616,2617,2618,2619,2620,2621,2622,2623,2624,2625,2626,2629,2630,2631,2632,2634,2636,2637,2639,2640,2642,2644,2646,2647,2648,2649,2650,2651,2652,2653,2654,2655,2656,2657,2659,2660,2661,2662,2664,2665,2666,2667,2668,2670,2672,2674,2676,2677,2678,2679,2680,2681,2682,2683,2684,2685,2686,2687,2688,2689,2690,2691,2772,2775,2778,2781,2795,2801,2818,2853,2882,2909,2918,2982,3345,3349,3383,3421,3439,3570,3576,3582,3616,3740,3760,3805,3809,3815,3850,3881,3967,3987,4042,4054,4080,4087", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "200,245,294,514,845,907,1288,1358,1419,1494,1570,1688,1766,2011,2093,2169,2245,2322,2400,2506,2612,2691,2771,2828,2886,3762,3837,3902,3968,4070,4131,4203,4276,6319,6387,6486,6545,6604,6663,6722,6776,6830,6883,6937,6991,7045,7099,7394,7473,7594,7668,7786,7858,7930,8003,8060,8118,8240,8314,8388,8463,8680,8753,8823,8894,9208,9269,9562,9631,9701,9775,9851,9915,9992,10068,10145,10210,10279,10356,10431,10500,10568,10645,10711,10772,10869,10934,11003,11102,11173,11232,11290,11347,11406,11470,11541,11613,11685,11757,11829,11896,11964,12032,12091,12154,12218,12308,12399,12459,12525,12592,12658,12728,12792,12845,12912,12973,13040,13153,13211,13274,13339,13404,13479,13552,13624,13668,13715,13761,13810,13871,13932,13993,14055,14119,14183,14247,14312,14375,14435,14496,14562,14621,14681,14743,14814,14874,14942,15956,16043,16348,16435,16523,16605,16688,16778,16869,18848,18906,18951,19017,19081,19138,19195,19249,21834,21882,21931,21982,26204,26550,26599,26948,28039,28470,28532,28592,28649,59802,59872,59950,60004,60074,60159,60207,60253,60314,60377,60443,60507,60578,60641,60706,60770,60831,60892,60944,61017,61091,61160,61235,61309,61383,61524,61594,71519,72695,72785,72873,72969,73059,73641,73730,73977,74258,74510,74795,75188,75828,76050,76272,76548,76775,77005,77235,77465,77695,77922,78341,78567,78992,79222,79650,79869,80152,80360,80491,80718,81144,81369,81796,82017,82442,82562,82838,83139,83463,83754,84068,84205,84336,84441,84683,84850,85054,85262,85533,85645,85757,85862,85979,86193,86339,86479,86565,86913,87001,87247,87665,87914,87996,88094,88751,88851,89103,89527,89782,89876,90231,90468,92492,92734,92836,93089,95245,105926,107442,118137,119665,121422,122048,122468,123729,124994,125250,125486,126033,126527,127132,127330,127910,129278,129653,129771,130309,130466,130662,130935,131191,131361,131502,131566,131931,132298,132974,133238,133576,133929,134023,134209,134515,134777,134902,135029,135268,135479,135598,135791,135968,136423,136604,136726,136985,137098,137285,137387,137494,137623,137898,138406,138902,139779,140073,140643,140792,141524,141696,141780,142116,142208,142486,151817,157188,157250,157828,158412,158503,158616,158845,159005,159157,159328,159494,159663,159830,159993,160236,160406,160579,160750,161024,161223,161428,161758,161842,161938,162034,162132,162232,162334,162436,162538,162640,162742,162842,162938,163050,163179,163302,163433,163564,163662,163776,163870,164010,164144,164240,164352,164452,164568,164664,164776,164876,165016,165152,165316,165446,165604,165754,165895,166039,166174,166286,166436,166564,166692,166828,166960,167090,167220,167332,167472,168376,168520,168658,168724,168814,168890,168994,169084,169186,169294,169402,169502,169582,169674,169772,169882,169934,170012,170118,170210,170314,170424,170546,170709,170866,172543,172643,172733,172843,172933,173174,173268,173374,173466,173566,173678,173792,173908,174024,174118,174232,174344,174446,174566,174688,174770,174874,174994,175120,175218,175312,175400,175512,175628,175750,175862,176037,176153,176239,176331,176443,176567,176634,176760,176828,176956,177100,177228,177297,177392,177507,177620,177719,177828,177939,178050,178151,178256,178356,178486,178577,178700,178794,178906,178992,179096,179192,179280,179398,179502,179606,179732,179820,179928,180028,180118,180228,180312,180414,180498,180552,180616,180722,180808,180918,181002,181122,184125,184243,184358,184438,184799,185032,185853,186818,188162,189523,189911,192754,202807,202942,204515,206173,206745,211284,211546,211746,212440,216718,217324,219228,219379,219594,220677,221406,224970,225714,227845,228185,229496,229699"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\5062aba79baa1c7b03e20d2e3d4079f3\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "446,450", "startColumns": "4,4", "startOffsets": "26264,26441", "endColumns": "53,66", "endOffsets": "26313,26503"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\3cbac0ac1c57f5294183717eb3060b6e\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "489", "startColumns": "4", "startOffsets": "28244", "endColumns": "49", "endOffsets": "28289"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\853221b3e22986c07128ae191e7cb964\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "488", "startColumns": "4", "startOffsets": "28190", "endColumns": "53", "endOffsets": "28239"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\26a7be6aa46fc6df32cf278684c813b2\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "498,982,983,984,985,986,987,988,989,990,991,994,995,996,997,998,999,1000,1001,1002,1003,1004,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,2173,2183", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "28790,66522,66610,66696,66777,66861,66930,66995,67078,67184,67270,67390,67444,67513,67574,67643,67732,67827,67901,67998,68091,68189,68338,68429,68517,68613,68711,68775,68843,68930,69024,69091,69163,69235,69336,69445,69521,69590,69638,69704,69768,69842,69899,69956,70028,70078,70132,70203,70274,70344,70413,70471,70547,70618,70692,70778,70828,70898,142833,143548", "endLines": "498,982,983,984,985,986,987,988,989,990,993,994,995,996,997,998,999,1000,1001,1002,1003,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,2182,2185", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "28858,66605,66691,66772,66856,66925,66990,67073,67179,67265,67385,67439,67508,67569,67638,67727,67822,67896,67993,68086,68184,68333,68424,68512,68608,68706,68770,68838,68925,69019,69086,69158,69230,69331,69440,69516,69585,69633,69699,69763,69837,69894,69951,70023,70073,70127,70198,70269,70339,70408,70466,70542,70613,70687,70773,70823,70893,70958,143543,143696"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\c26fa102c23d3286c53c26e88c6db80d\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "456,487", "startColumns": "4,4", "startOffsets": "26690,28130", "endColumns": "41,59", "endOffsets": "26727,28185"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\821e8c1a7433219b3163b17b67af0011\\transformed\\material-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "57", "endOffsets": "108"}, "to": {"startLines": "1043", "startColumns": "4", "startOffsets": "70963", "endColumns": "57", "endOffsets": "71016"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\2acc0916e8403ec08af08abcba915883\\transformed\\navigation-common-release\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "3761,3774,3780,3786,3795", "startColumns": "4,4,4,4,4", "startOffsets": "217329,217968,218212,218459,218822", "endLines": "3773,3779,3785,3788,3799", "endColumns": "24,24,24,24,24", "endOffsets": "217963,218207,218454,218587,218999"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\cd949c0c700901654b232af64fc9a9c9\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "486", "startColumns": "4", "startOffsets": "28087", "endColumns": "42", "endOffsets": "28125"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\07a29fdb86ef710e85f00096aa41bc4b\\transformed\\glance-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,168,231,292,348,413,471,538,598,667,725,790,855,914,982,1038,1094,1159,1222,1280,1347,1403,1466,1529,1586,1652", "endColumns": "58,53,62,60,55,64,57,66,59,68,57,64,64,58,67,55,55,64,62,57,66,55,62,62,56,65,64", "endOffsets": "109,163,226,287,343,408,466,533,593,662,720,785,850,909,977,1033,1089,1154,1217,1275,1342,1398,1461,1524,1581,1647,1712"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4281,4340,4394,4457,4518,4574,4639,4697,4764,4824,4893,4951,5016,5081,5140,5208,5264,5320,5385,5448,5506,5573,5629,5692,5755,5812,5878", "endColumns": "58,53,62,60,55,64,57,66,59,68,57,64,64,58,67,55,55,64,62,57,66,55,62,62,56,65,64", "endOffsets": "4335,4389,4452,4513,4569,4634,4692,4759,4819,4888,4946,5011,5076,5135,5203,5259,5315,5380,5443,5501,5568,5624,5687,5750,5807,5873,5938"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\6bdbe06209508b26c4e38693a2ab85d8\\transformed\\play-services-basement-18.4.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "497,947", "startColumns": "4,4", "startOffsets": "28722,63450", "endColumns": "67,166", "endOffsets": "28785,63612"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\dcea319fdd3ad3c36ece65fd708a73da\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,63,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2452,2517,2571,2637,2738,2796,2848,2908,2970,3024,3074,3128,3174,3228,3274,3316,3356,3403,3439,3529,3641,3752", "endLines": "34,35,36,37,38,39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,62,65,70", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2512,2566,2632,2733,2791,2843,2903,2965,3019,3069,3123,3169,3223,3269,3311,3351,3398,3434,3524,3636,3747,4004"}, "to": {"startLines": "341,436,437,443,445,491,937,938,966,967,971,980,981,1044,1046,1047,1048,1053,1054,1055,1056,1057,1059,1060,1062,2170,2186,2189", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21987,25797,25855,26124,26209,28358,62291,62356,65348,65414,65690,66410,66462,71021,71153,71207,71257,71524,71570,71624,71670,71712,71823,71870,71962,142721,143701,143812", "endLines": "341,436,437,443,445,491,937,938,966,967,971,980,981,1044,1046,1047,1048,1053,1054,1055,1056,1057,1059,1060,1062,2172,2188,2193", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "22056,25850,25905,26170,26259,28406,62351,62405,65409,65510,65743,66457,66517,71078,71202,71252,71306,71565,71619,71665,71707,71747,71865,71901,72047,142828,143807,144064"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\06d808385d814fc5793833c3be2ca602\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,101,157", "endColumns": "45,55,54", "endOffsets": "96,152,207"}, "to": {"startLines": "929,1063,1064", "startColumns": "4,4,4", "startOffsets": "61734,72052,72108", "endColumns": "45,55,54", "endOffsets": "61775,72103,72158"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\43ef54042a4a5617b6e570bf9425eae0\\transformed\\core-splashscreen-1.0.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,21,23,32,45", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,174,243,315,378,450,524,600,676,753,824,893,964,1032,1113,1205,1298,1407,1528,1988,2763", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,20,22,31,44,48", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "110,169,238,310,373,445,519,595,671,748,819,888,959,1027,1108,1200,1293,1402,1523,1983,2758,3031"}, "to": {"startLines": "6,7,9,10,11,12,274,275,276,277,278,279,280,496,1334,1335,1336,2166,2168,2559,2568,2581", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "359,419,519,588,660,723,18048,18122,18198,18274,18351,18422,18491,28654,89881,89962,90054,142491,142600,170960,171420,172195", "endLines": "6,7,9,10,11,12,274,275,276,277,278,279,280,496,1334,1335,1336,2167,2169,2567,2580,2584", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "414,473,583,655,718,790,18117,18193,18269,18346,18417,18486,18557,28717,89957,90049,90142,142595,142716,171415,172190,172463"}}, {"source": "C:\\Users\\<USER>\\Pictures\\widget\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "2,16,14,15,17,13,19,20,21,18,28,30,29,41,39,38,40,34,27,26,25,24,33,6,8,9,7,5,10,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,824,670,779,873,599,1016,1076,1140,947,1474,1608,1565,2129,1979,1902,2043,1751,1404,1352,1303,1244,1695,203,355,418,295,148,485,1812", "endColumns": "51,48,108,44,73,70,59,63,63,68,90,40,42,57,63,76,85,60,69,51,48,58,55,91,62,66,59,54,64,56", "endOffsets": "102,868,774,819,942,665,1071,1135,1199,1011,1560,1644,1603,2182,2038,1974,2124,1807,1469,1399,1347,1298,1746,290,413,480,350,198,545,1864"}, "to": {"startLines": "928,957,958,959,960,961,962,963,964,965,968,969,970,972,973,974,975,979,1045,1049,1050,1051,1061,1065,1066,1067,1068,1069,1070,1071", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "61682,64743,64792,64901,64946,65020,65091,65151,65215,65279,65515,65606,65647,65748,65806,65870,65947,66349,71083,71311,71363,71412,71906,72163,72255,72318,72385,72445,72500,72565", "endColumns": "51,48,108,44,73,70,59,63,63,68,90,40,42,57,63,76,85,60,69,51,48,58,55,91,62,66,59,54,64,56", "endOffsets": "61729,64787,64896,64941,65015,65086,65146,65210,65274,65343,65601,65642,65685,65801,65865,65942,66028,66405,71148,71358,71407,71466,71957,72250,72313,72380,72440,72495,72560,72617"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\1fd9d3b4b3ad55377417080a97e270c2\\transformed\\fragment-1.8.3\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "439,461,490,3496,3501", "startColumns": "4,4,4,4,4", "startOffsets": "25941,26842,28294,209237,209407", "endLines": "439,461,490,3500,3504", "endColumns": "56,64,63,24,24", "endOffsets": "25993,26902,28353,209402,209551"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b6d561069ffee61f592209e5e368cf66\\transformed\\appcompat-resources-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2819,2835,2841,3882,3898", "startColumns": "4,4,4,4,4", "startOffsets": "185858,186283,186461,221411,221822", "endLines": "2834,2840,2850,3897,3901", "endColumns": "24,24,24,24,24", "endOffsets": "186278,186456,186740,221817,221944"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\ea6cdc6a4f354f494fb001d5b96313da\\transformed\\work-runtime-2.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "15,16,17,19", "startColumns": "4,4,4,4", "startOffsets": "912,977,1047,1168", "endColumns": "64,69,63,60", "endOffsets": "972,1042,1106,1224"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\99eb3aee547f429176c0b3577d528a52\\transformed\\play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "44,45,46,47,48,49,50,51,939,940,941,942,943,944,945,946,948,949,950,951,952,953,954,955,956,3583,3851", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3022,3112,3192,3282,3372,3452,3533,3613,62410,62515,62696,62821,62928,63108,63231,63347,63617,63805,63910,64091,64216,64391,64539,64602,64664,211751,220682", "endLines": "44,45,46,47,48,49,50,51,939,940,941,942,943,944,945,946,948,949,950,951,952,953,954,955,956,3595,3869", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "3107,3187,3277,3367,3447,3528,3608,3688,62510,62691,62816,62923,63103,63226,63342,63445,63800,63905,64086,64211,64386,64534,64597,64659,64738,212061,221094"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\30287725489871af330b7aa82a2b9f1f\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,105,106,107,108,114,124,159,180,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4484,4542,4603,4666,4723,4774,4832,4882,4943,5000,5066,5100,5135,5170,5240,5307,5379,5448,5517,5591,5663,5751,5822,5939,6140,6250,6451,6580,6652,6719,6922,7223,9029,9710,10392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,104,105,106,107,113,123,158,179,212,218", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4479,4537,4598,4661,4718,4769,4827,4877,4938,4995,5061,5095,5130,5165,5235,5302,5374,5443,5512,5586,5658,5746,5817,5934,6135,6245,6446,6575,6647,6714,6917,7218,9024,9705,10387,10554"}, "to": {"startLines": "5,28,29,42,43,107,108,236,237,238,239,240,241,242,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,447,448,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,499,930,931,932,933,934,935,936,1058,2511,2512,2516,2517,2521,2692,2693,3350,3384,3440,3475,3508,3541", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "299,1771,1843,2891,2956,7104,7173,15387,15457,15525,15597,15667,15728,15802,17070,17131,17192,17254,17318,17380,17441,17509,17609,17669,17735,17808,17877,17934,17986,19659,19731,19807,19872,19931,19990,20050,20110,20170,20230,20290,20350,20410,20470,20530,20590,20649,20709,20769,20829,20889,20949,21009,21069,21129,21189,21249,21308,21368,21428,21487,21546,21605,21664,21723,26318,26353,27039,27094,27157,27212,27270,27326,27384,27445,27508,27565,27616,27674,27724,27785,27842,27908,27942,27977,28863,61780,61847,61919,61988,62057,62131,62203,71752,167477,167594,167795,167905,168106,181127,181199,202947,204520,206750,208556,209689,210371", "endLines": "5,28,29,42,43,107,108,236,237,238,239,240,241,242,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,447,448,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,499,930,931,932,933,934,935,936,1058,2511,2515,2516,2520,2521,2692,2693,3355,3393,3474,3495,3540,3546", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "354,1838,1926,2951,3017,7168,7231,15452,15520,15592,15662,15723,15797,15870,17126,17187,17249,17313,17375,17436,17504,17604,17664,17730,17803,17872,17929,17981,18043,19726,19802,19867,19926,19985,20045,20105,20165,20225,20285,20345,20405,20465,20525,20585,20644,20704,20764,20824,20884,20944,21004,21064,21124,21184,21244,21303,21363,21423,21482,21541,21600,21659,21718,21777,26348,26383,27089,27152,27207,27265,27321,27379,27440,27503,27560,27611,27669,27719,27780,27837,27903,27937,27972,28007,28928,61842,61914,61983,62052,62126,62198,62286,71818,167589,167790,167900,168101,168230,181194,181261,203145,204816,208551,209232,210366,210533"}}, {"source": "C:\\Users\\<USER>\\Pictures\\widget\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "89", "endOffsets": "141"}, "to": {"startLines": "2558", "startColumns": "4", "startOffsets": "170871", "endColumns": "88", "endOffsets": "170955"}}, {"source": "C:\\Users\\<USER>\\Pictures\\widget\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "5,9,8,2,3,4,6,7,27,25,26,18,19,22,20,21,13,12,14,15", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "192,369,329,55,97,144,233,280,1097,999,1048,713,764,917,815,866,510,452,566,620", "endColumns": "40,41,39,41,46,47,46,48,46,48,48,50,50,49,50,50,55,57,53,55", "endOffsets": "228,406,364,92,139,187,275,324,1139,1043,1092,759,810,962,861,912,561,505,615,671"}, "to": {"startLines": "25,56,94,109,110,113,116,123,128,129,130,135,136,137,138,139,142,143,144,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1575,3973,6392,7236,7278,7478,7673,8123,8468,8515,8564,8899,8950,9001,9051,9102,9274,9330,9388,9442", "endColumns": "40,41,39,41,46,47,46,48,46,48,48,50,50,49,50,50,55,57,53,55", "endOffsets": "1611,4010,6427,7273,7320,7521,7715,8167,8510,8559,8608,8945,8996,9046,9097,9148,9325,9383,9437,9493"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\edefb81d0fd702a290e7a6aa35560d0b\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "442", "startColumns": "4", "startOffsets": "26058", "endColumns": "65", "endOffsets": "26119"}}]}]}