{"logs": [{"outputFile": "com.example.weatherwidget.app-mergeDebugResources-80:/values-ka/values-ka.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\f958aeb604e7c143e9bc08e83e2ba828\\transformed\\glance-appwidget-1.1.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,249,366", "endColumns": "193,116,118", "endOffsets": "244,361,480"}, "to": {"startLines": "60,61,62", "startColumns": "4,4,4", "startOffsets": "6311,6505,6622", "endColumns": "193,116,118", "endOffsets": "6500,6617,6736"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\26a7be6aa46fc6df32cf278684c813b2\\transformed\\material3-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,290,411,525,625,724,840,976,1094,1242,1328,1430,1524,1622,1744,1864,1971,2106,2243,2378,2550,2679,2796,2914,3035,3130,3227,3345,3484,3587,3689,3800,3938,4078,4189,4292,4369,4464,4562,4672,4758,4845,4958,5038,5123,5224,5327,5421,5523,5609,5715,5811,5919,6036,6116,6222", "endColumns": "117,116,120,113,99,98,115,135,117,147,85,101,93,97,121,119,106,134,136,134,171,128,116,117,120,94,96,117,138,102,101,110,137,139,110,102,76,94,97,109,85,86,112,79,84,100,102,93,101,85,105,95,107,116,79,105,96", "endOffsets": "168,285,406,520,620,719,835,971,1089,1237,1323,1425,1519,1617,1739,1859,1966,2101,2238,2373,2545,2674,2791,2909,3030,3125,3222,3340,3479,3582,3684,3795,3933,4073,4184,4287,4364,4459,4557,4667,4753,4840,4953,5033,5118,5219,5322,5416,5518,5604,5710,5806,5914,6031,6111,6217,6314"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6917,7035,7152,7273,7387,7487,7586,7702,7838,7956,8104,8190,8292,8386,8484,8606,8726,8833,8968,9105,9240,9412,9541,9658,9776,9897,9992,10089,10207,10346,10449,10551,10662,10800,10940,11051,11154,11231,11326,11424,11534,11620,11707,11820,11900,11985,12086,12189,12283,12385,12471,12577,12673,12781,12898,12978,13084", "endColumns": "117,116,120,113,99,98,115,135,117,147,85,101,93,97,121,119,106,134,136,134,171,128,116,117,120,94,96,117,138,102,101,110,137,139,110,102,76,94,97,109,85,86,112,79,84,100,102,93,101,85,105,95,107,116,79,105,96", "endOffsets": "7030,7147,7268,7382,7482,7581,7697,7833,7951,8099,8185,8287,8381,8479,8601,8721,8828,8963,9100,9235,9407,9536,9653,9771,9892,9987,10084,10202,10341,10444,10546,10657,10795,10935,11046,11149,11226,11321,11419,11529,11615,11702,11815,11895,11980,12081,12184,12278,12380,12466,12572,12668,12776,12893,12973,13079,13176"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\821e8c1a7433219b3163b17b67af0011\\transformed\\material-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "93", "endOffsets": "144"}, "to": {"startLines": "122", "startColumns": "4", "startOffsets": "13181", "endColumns": "93", "endOffsets": "13270"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\99eb3aee547f429176c0b3577d528a52\\transformed\\play-services-base-18.5.0\\res\\values-ka\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,439,563,669,819,949,1067,1171,1340,1444,1595,1719,1876,2011,2073,2130", "endColumns": "100,144,123,105,149,129,117,103,168,103,150,123,156,134,61,56,71", "endOffsets": "293,438,562,668,818,948,1066,1170,1339,1443,1594,1718,1875,2010,2072,2129,2201"}, "to": {"startLines": "39,40,41,42,43,44,45,46,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3799,3904,4053,4181,4291,4445,4579,4701,4952,5125,5233,5388,5516,5677,5816,5882,5943", "endColumns": "104,148,127,109,153,133,121,107,172,107,154,127,160,138,65,60,75", "endOffsets": "3899,4048,4176,4286,4440,4574,4696,4804,5120,5228,5383,5511,5672,5811,5877,5938,6014"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\a6d2b7e5ecb18270abb575bfe8bf3041\\transformed\\appcompat-1.7.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,316,427,513,618,731,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1912,2025,2131,2229,2342,2447,2551,2709,2808", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "208,311,422,508,613,726,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1907,2020,2126,2224,2337,2442,2546,2704,2803,2885"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,316,427,513,618,731,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1912,2025,2131,2229,2342,2447,2551,2709,13632", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "208,311,422,508,613,726,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1907,2020,2126,2224,2337,2442,2546,2704,2803,13709"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\30287725489871af330b7aa82a2b9f1f\\transformed\\core-1.16.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,779", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,875"}, "to": {"startLines": "30,31,32,33,34,35,36,133", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2893,2989,3091,3190,3289,3395,3499,14107", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "2984,3086,3185,3284,3390,3494,3612,14203"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\dcea319fdd3ad3c36ece65fd708a73da\\transformed\\ui-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "191,287,373,472,575,665,745,841,931,1018,1107,1198,1270,1360,1438,1516,1591,1670,1740", "endColumns": "95,85,98,102,89,79,95,89,86,88,90,71,89,77,77,74,78,69,120", "endOffsets": "282,368,467,570,660,740,836,926,1013,1102,1193,1265,1355,1433,1511,1586,1665,1735,1856"}, "to": {"startLines": "37,38,57,58,59,63,64,123,124,125,126,128,129,130,131,132,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3617,3713,6019,6118,6221,6741,6821,13275,13365,13452,13541,13714,13786,13876,13954,14032,14208,14287,14357", "endColumns": "95,85,98,102,89,79,95,89,86,88,90,71,89,77,77,74,78,69,120", "endOffsets": "3708,3794,6113,6216,6306,6816,6912,13360,13447,13536,13627,13781,13871,13949,14027,14102,14282,14352,14473"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\06d808385d814fc5793833c3be2ca602\\transformed\\foundation-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,140,228", "endColumns": "84,87,90", "endOffsets": "135,223,314"}, "to": {"startLines": "29,137,138", "startColumns": "4,4,4", "startOffsets": "2808,14478,14566", "endColumns": "84,87,90", "endOffsets": "2888,14561,14652"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\6bdbe06209508b26c4e38693a2ab85d8\\transformed\\play-services-basement-18.4.0\\res\\values-ka\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "47", "startColumns": "4", "startOffsets": "4809", "endColumns": "142", "endOffsets": "4947"}}]}]}