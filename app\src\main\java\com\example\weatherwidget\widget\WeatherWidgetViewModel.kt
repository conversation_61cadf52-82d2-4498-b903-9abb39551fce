package com.example.weatherwidget.widget

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.weatherwidget.data.model.WeatherInfo
import com.example.weatherwidget.data.repository.WeatherRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.launch

class WeatherWidgetViewModel(
    private val weatherRepository: WeatherRepository
) : ViewModel() {

    // Get all weather information
    val allWeatherInfo: Flow<List<WeatherInfo>> = weatherRepository.getAllWeatherInfo()
    
    // Get favorite weather information
    val favoriteWeatherInfo: Flow<List<WeatherInfo>> = weatherRepository.getFavoriteWeatherInfo()
    
    /**
     * Set the city for the widget
     */
    suspend fun setWidgetCity(cityId: Long) {
        weatherRepository.setSelectedWidgetCityId(cityId)
        refreshWeather(cityId)
    }
    
    /**
     * Toggle favorite status for a city
     */
    fun toggleFavorite(cityId: Long, isFavorite: Boolean) {
        viewModelScope.launch {
            weatherRepository.updateFavoriteStatus(cityId, isFavorite)
        }
    }
    
    /**
     * Refresh weather data for a specific city
     */
    private suspend fun refreshWeather(cityId: Long) {
        weatherRepository.refreshWeatherById(cityId)
    }
    
    /**
     * Set the widget update frequency
     */
    fun setUpdateFrequency(hours: Int) {
        viewModelScope.launch {
            weatherRepository.setWidgetUpdateFrequencyHours(hours)
        }
    }
}