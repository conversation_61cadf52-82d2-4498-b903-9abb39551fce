package com.example.weatherwidget.widget

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.weatherwidget.data.model.WeatherInfo
import com.example.weatherwidget.data.model.WidgetConfiguration
import com.example.weatherwidget.data.repository.WeatherRepository
import com.example.weatherwidget.domain.usecase.WidgetConfigurationUseCase
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.launch

class WeatherWidgetViewModel(
    private val weatherRepository: WeatherRepository,
    private val widgetConfigurationUseCase: WidgetConfigurationUseCase
) : ViewModel() {

    // Get all weather information
    val allWeatherInfo: Flow<List<WeatherInfo>> = weatherRepository.getAllWeatherInfo()

    // Get favorite weather information
    val favoriteWeatherInfo: Flow<List<WeatherInfo>> = weatherRepository.getFavoriteWeatherInfo()

    /**
     * Set the city for the widget
     */
    suspend fun setWidgetCity(cityId: Long) {
        weatherRepository.setSelectedWidgetCityId(cityId)
        refreshWeather(cityId)
    }

    /**
     * Toggle favorite status for a city
     */
    fun toggleFavorite(cityId: Long, isFavorite: Boolean) {
        viewModelScope.launch {
            weatherRepository.updateFavoriteStatus(cityId, isFavorite)
        }
    }

    /**
     * Refresh weather data for a specific city
     */
    private suspend fun refreshWeather(cityId: Long) {
        weatherRepository.refreshWeatherById(cityId)
    }

    /**
     * Set the widget update frequency
     */
    fun setUpdateFrequency(hours: Int) {
        viewModelScope.launch {
            weatherRepository.setWidgetUpdateFrequencyHours(hours)
        }
    }

    /**
     * Get widget configuration
     */
    fun getWidgetConfiguration(widgetId: Int): Flow<WidgetConfiguration> {
        return widgetConfigurationUseCase.getWidgetConfiguration(widgetId)
    }

    /**
     * Save widget configuration
     */
    fun saveWidgetConfiguration(configuration: WidgetConfiguration) {
        viewModelScope.launch {
            widgetConfigurationUseCase.saveWidgetConfiguration(configuration)
        }
    }

    /**
     * Reset widget configuration to defaults
     */
    fun resetWidgetConfiguration(widgetId: Int) {
        viewModelScope.launch {
            widgetConfigurationUseCase.resetWidgetConfiguration(widgetId)
        }
    }
}