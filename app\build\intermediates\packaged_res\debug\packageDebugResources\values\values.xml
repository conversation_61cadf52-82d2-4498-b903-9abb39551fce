<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accent">#FF9800</color>
    <color name="divider">#BDBDBD</color>
    <color name="icons">#FFFFFF</color>
    <color name="primary">#2196F3</color>
    <color name="primary_dark">#1976D2</color>
    <color name="primary_light">#BBDEFB</color>
    <color name="primary_text">#212121</color>
    <color name="secondary_text">#757575</color>
    <color name="status_error">#F44336</color>
    <color name="status_success">#4CAF50</color>
    <color name="status_warning">#FFC107</color>
    <color name="temperature_cold">#64B5F6</color>
    <color name="temperature_cool">#81D4FA</color>
    <color name="temperature_hot">#FF7043</color>
    <color name="temperature_mild">#FFD54F</color>
    <color name="temperature_warm">#FFA726</color>
    <color name="widget_background_end">#1976D2</color>
    <color name="widget_background_start">#2196F3</color>
    <color name="widget_text_primary">#FFFFFF</color>
    <color name="widget_text_secondary">#E0E0E0</color>
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="button_corner_radius">4dp</dimen>
    <dimen name="button_height">48dp</dimen>
    <dimen name="button_min_width">88dp</dimen>
    <dimen name="button_padding">16dp</dimen>
    <dimen name="card_corner_radius">8dp</dimen>
    <dimen name="card_elevation">4dp</dimen>
    <dimen name="card_margin">8dp</dimen>
    <dimen name="list_item_height">72dp</dimen>
    <dimen name="list_item_icon_size">48dp</dimen>
    <dimen name="list_item_padding">16dp</dimen>
    <dimen name="list_item_spacing">8dp</dimen>
    <dimen name="text_size_large">16sp</dimen>
    <dimen name="text_size_medium">14sp</dimen>
    <dimen name="text_size_small">12sp</dimen>
    <dimen name="text_size_xlarge">20sp</dimen>
    <dimen name="text_size_xxlarge">24sp</dimen>
    <dimen name="widget_city_text_size">18sp</dimen>
    <dimen name="widget_corner_radius">16dp</dimen>
    <dimen name="widget_desc_text_size">14sp</dimen>
    <dimen name="widget_details_text_size">12sp</dimen>
    <dimen name="widget_icon_size">40dp</dimen>
    <dimen name="widget_margin">8dp</dimen>
    <dimen name="widget_padding">12dp</dimen>
    <dimen name="widget_temp_text_size">24sp</dimen>
    <string name="app_name">Weather Widget</string>
    <string name="config_cancel">Cancel</string>
    <string name="config_no_cities">No saved cities found. Please add cities in the main app first.</string>
    <string name="config_save">Save</string>
    <string name="config_success">Widget configured successfully</string>
    <string name="config_title">Select a city for your widget</string>
    <string name="config_update_1_hour">Every hour</string>
    <string name="config_update_2_hours">Every 2 hours</string>
    <string name="config_update_4_hours">Every 4 hours</string>
    <string name="config_update_frequency">Update frequency</string>
    <string name="delete_confirmation">Are you sure you want to delete this city?</string>
    <string name="delete_no">No</string>
    <string name="delete_yes">Yes</string>
    <string name="error_api_key">Invalid API key</string>
    <string name="error_city_not_found">City not found</string>
    <string name="error_loading_weather">Error loading weather data</string>
    <string name="error_network">Network error. Please check your connection</string>
    <string name="humidity_format">Humidity: %1$d%%</string>
    <string name="no_weather_data">No weather data available</string>
    <string name="refresh_all">Refresh All</string>
    <string name="search_button">Search</string>
    <string name="search_hint">Search for a city…</string>
    <string name="temperature_format">%1$.1f°C</string>
    <string name="widget_description">Displays current weather for a selected city</string>
    <string name="widget_error">Error loading weather</string>
    <string name="widget_last_updated">Last updated: %1$s</string>
    <string name="widget_loading">Loading weather…</string>
    <string name="widget_name">Weather Widget</string>
    <string name="widget_tap_to_refresh">Tap to refresh</string>
    <string name="wind_format">Wind: %1$.1f m/s</string>
    <style name="Theme.CleanWeather" parent="android:Theme.Material.Light.NoActionBar"/>
</resources>