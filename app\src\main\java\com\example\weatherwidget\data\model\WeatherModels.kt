package com.example.weatherwidget.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

/**
 * Database entity for storing weather information
 */
@Entity(tableName = "weather")
data class WeatherInfo(
    @PrimaryKey
    val cityId: Long,
    val cityName: String,
    val country: String,
    val temperature: Double,
    val feelsLike: Double,
    val humidity: Int,
    val pressure: Int,
    val windSpeed: Double,
    val windDirection: Int,
    val weatherCondition: String,
    val weatherDescription: String,
    val weatherIconCode: String,
    val lastUpdated: Long = System.currentTimeMillis(),
    val isFavorite: Boolean = false
)

/**
 * API response models
 */
data class WeatherResponse(
    val id: Long,
    val name: String,
    val sys: Sys,
    val main: Main,
    val wind: Wind,
    val weather: List<Weather>
)

data class Sys(
    val country: String
)

data class Main(
    val temp: Double,
    @SerializedName("feels_like")
    val feelsLike: Double,
    val humidity: Int,
    val pressure: Int
)

data class Wind(
    val speed: Double,
    val deg: Int
)

data class Weather(
    val id: Int,
    val main: String,
    val description: String,
    val icon: String
)

/**
 * Extension function to convert API response to database entity
 */
fun WeatherResponse.toWeatherInfo(): WeatherInfo {
    return WeatherInfo(
        cityId = id,
        cityName = name,
        country = sys.country,
        temperature = main.temp,
        feelsLike = main.feelsLike,
        humidity = main.humidity,
        pressure = main.pressure,
        windSpeed = wind.speed,
        windDirection = wind.deg,
        weatherCondition = weather.firstOrNull()?.main ?: "",
        weatherDescription = weather.firstOrNull()?.description ?: "",
        weatherIconCode = weather.firstOrNull()?.icon ?: ""
    )
}