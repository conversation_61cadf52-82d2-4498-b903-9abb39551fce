package com.example.weatherwidget.domain.usecase

import com.example.weatherwidget.data.WidgetPreferencesManager
import com.example.weatherwidget.data.model.WidgetConfiguration
import com.example.weatherwidget.data.model.DefaultWidgetConfiguration
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first

/**
 * Use case for managing widget configurations
 */
class WidgetConfigurationUseCase(
    private val widgetPreferencesManager: WidgetPreferencesManager
) {

    /**
     * Get widget configuration for a specific widget instance
     */
    fun getWidgetConfiguration(widgetId: Int): Flow<WidgetConfiguration> {
        return widgetPreferencesManager.getWidgetConfiguration(widgetId)
    }

    /**
     * Save widget configuration
     */
    suspend fun saveWidgetConfiguration(config: WidgetConfiguration) {
        widgetPreferencesManager.saveWidgetConfiguration(config)
    }

    /**
     * Delete widget configuration
     */
    suspend fun deleteWidgetConfiguration(widgetId: Int) {
        widgetPreferencesManager.deleteWidgetConfiguration(widgetId)
    }

    /**
     * Get all widget configurations
     */
    fun getAllWidgetConfigurations(): Flow<Map<Int, WidgetConfiguration>> {
        return widgetPreferencesManager.getAllWidgetConfigurations()
    }

    /**
     * Reset widget configuration to defaults
     */
    suspend fun resetWidgetConfiguration(widgetId: Int) {
        val defaultConfig = DefaultWidgetConfiguration.create(widgetId)
        saveWidgetConfiguration(defaultConfig)
    }

    /**
     * Update widget theme
     */
    suspend fun updateWidgetTheme(widgetId: Int, theme: com.example.weatherwidget.data.model.WidgetTheme) {
        val currentConfig = getWidgetConfiguration(widgetId).first()
        val updatedConfig = currentConfig.copy(theme = theme)
        saveWidgetConfiguration(updatedConfig)
    }

    /**
     * Update widget transparency
     */
    suspend fun updateWidgetTransparency(widgetId: Int, transparency: Float) {
        val currentConfig = getWidgetConfiguration(widgetId).first()
        val updatedConfig = currentConfig.copy(transparency = transparency)
        saveWidgetConfiguration(updatedConfig)
    }

    /**
     * Update location display settings
     */
    suspend fun updateLocationDisplay(
        widgetId: Int, 
        showCityName: Boolean, 
        selectedCityId: Long?
    ) {
        val currentConfig = getWidgetConfiguration(widgetId).first()
        val updatedLocationConfig = currentConfig.locationDisplay.copy(
            showCityName = showCityName,
            selectedCityId = selectedCityId
        )
        val updatedConfig = currentConfig.copy(locationDisplay = updatedLocationConfig)
        saveWidgetConfiguration(updatedConfig)
    }

    /**
     * Update time display settings
     */
    suspend fun updateTimeDisplay(widgetId: Int, showLastUpdated: Boolean) {
        val currentConfig = getWidgetConfiguration(widgetId).first()
        val updatedTimeConfig = currentConfig.timeDisplay.copy(showLastUpdated = showLastUpdated)
        val updatedConfig = currentConfig.copy(timeDisplay = updatedTimeConfig)
        saveWidgetConfiguration(updatedConfig)
    }

    /**
     * Update weather elements settings
     */
    suspend fun updateWeatherElements(
        widgetId: Int,
        showTemperature: Boolean,
        showCondition: Boolean,
        showHumidity: Boolean,
        showWindSpeed: Boolean
    ) {
        val currentConfig = getWidgetConfiguration(widgetId).first()
        val updatedElementsConfig = currentConfig.weatherElements.copy(
            showTemperature = showTemperature,
            showCondition = showCondition,
            showHumidity = showHumidity,
            showWindSpeed = showWindSpeed
        )
        val updatedConfig = currentConfig.copy(weatherElements = updatedElementsConfig)
        saveWidgetConfiguration(updatedConfig)
    }

    /**
     * Update visual customization settings
     */
    suspend fun updateVisualCustomization(
        widgetId: Int,
        iconSize: com.example.weatherwidget.data.model.IconSize,
        fontSize: com.example.weatherwidget.data.model.FontSize,
        cornerRadius: com.example.weatherwidget.data.model.CornerRadius
    ) {
        val currentConfig = getWidgetConfiguration(widgetId).first()
        val updatedVisualConfig = currentConfig.visualCustomization.copy(
            iconSize = iconSize,
            fontSize = fontSize,
            cornerRadius = cornerRadius
        )
        val updatedConfig = currentConfig.copy(visualCustomization = updatedVisualConfig)
        saveWidgetConfiguration(updatedConfig)
    }

    /**
     * Update update settings
     */
    suspend fun updateUpdateSettings(
        widgetId: Int,
        refreshRate: com.example.weatherwidget.data.model.RefreshRate,
        networkConstraint: com.example.weatherwidget.data.model.NetworkConstraint
    ) {
        val currentConfig = getWidgetConfiguration(widgetId).first()
        val updatedUpdateConfig = currentConfig.updateSettings.copy(
            refreshRate = refreshRate,
            networkConstraint = networkConstraint
        )
        val updatedConfig = currentConfig.copy(updateSettings = updatedUpdateConfig)
        saveWidgetConfiguration(updatedConfig)
    }

    /**
     * Check if widget configuration exists
     */
    suspend fun hasWidgetConfiguration(widgetId: Int): Boolean {
        val allConfigs = getAllWidgetConfigurations().first()
        return allConfigs.containsKey(widgetId)
    }

    /**
     * Validate widget configuration
     */
    fun validateConfiguration(config: WidgetConfiguration): Boolean {
        return config.transparency in 0.0f..1.0f &&
                config.visualCustomization.iconSize.dp > 0 &&
                config.updateSettings.refreshRate.minutes > 0
    }
}
