<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Pictures\widget\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Pictures\widget\app\src\main\res"><file name="ic_clear_day" path="C:\Users\<USER>\Pictures\widget\app\src\main\res\drawable\ic_clear_day.xml" qualifiers="" type="drawable"/><file name="ic_clear_night" path="C:\Users\<USER>\Pictures\widget\app\src\main\res\drawable\ic_clear_night.xml" qualifiers="" type="drawable"/><file name="ic_cloudy" path="C:\Users\<USER>\Pictures\widget\app\src\main\res\drawable\ic_cloudy.xml" qualifiers="" type="drawable"/><file name="ic_fog" path="C:\Users\<USER>\Pictures\widget\app\src\main\res\drawable\ic_fog.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\Pictures\widget\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Pictures\widget\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_partly_cloudy_day" path="C:\Users\<USER>\Pictures\widget\app\src\main\res\drawable\ic_partly_cloudy_day.xml" qualifiers="" type="drawable"/><file name="ic_partly_cloudy_night" path="C:\Users\<USER>\Pictures\widget\app\src\main\res\drawable\ic_partly_cloudy_night.xml" qualifiers="" type="drawable"/><file name="ic_rain" path="C:\Users\<USER>\Pictures\widget\app\src\main\res\drawable\ic_rain.xml" qualifiers="" type="drawable"/><file name="ic_rain_day" path="C:\Users\<USER>\Pictures\widget\app\src\main\res\drawable\ic_rain_day.xml" qualifiers="" type="drawable"/><file name="ic_rain_night" path="C:\Users\<USER>\Pictures\widget\app\src\main\res\drawable\ic_rain_night.xml" qualifiers="" type="drawable"/><file name="ic_snow" path="C:\Users\<USER>\Pictures\widget\app\src\main\res\drawable\ic_snow.xml" qualifiers="" type="drawable"/><file name="ic_thunderstorm" path="C:\Users\<USER>\Pictures\widget\app\src\main\res\drawable\ic_thunderstorm.xml" qualifiers="" type="drawable"/><file name="widget_background" path="C:\Users\<USER>\Pictures\widget\app\src\main\res\drawable\widget_background.xml" qualifiers="" type="drawable"/><file name="widget_preview" path="C:\Users\<USER>\Pictures\widget\app\src\main\res\drawable\widget_preview.xml" qualifiers="" type="drawable"/><file name="widget_loading" path="C:\Users\<USER>\Pictures\widget\app\src\main\res\layout\widget_loading.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="C:\Users\<USER>\Pictures\widget\app\src\main\res\mipmap-anydpi\ic_launcher.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Pictures\widget\app\src\main\res\mipmap-anydpi\ic_launcher_round.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Pictures\widget\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Pictures\widget\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Pictures\widget\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Pictures\widget\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Pictures\widget\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Pictures\widget\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Pictures\widget\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Pictures\widget\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Pictures\widget\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Pictures\widget\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="weather_widget_flow_diagram" path="C:\Users\<USER>\Pictures\widget\app\src\main\res\raw\weather_widget_flow_diagram.svg" qualifiers="" type="raw"/><file path="C:\Users\<USER>\Pictures\widget\app\src\main\res\values\colors.xml" qualifiers=""><color name="primary">#2196F3</color><color name="primary_dark">#1976D2</color><color name="primary_light">#BBDEFB</color><color name="accent">#FF9800</color><color name="primary_text">#212121</color><color name="secondary_text">#757575</color><color name="icons">#FFFFFF</color><color name="divider">#BDBDBD</color><color name="widget_background_start">#2196F3</color><color name="widget_background_end">#1976D2</color><color name="widget_text_primary">#FFFFFF</color><color name="widget_text_secondary">#E0E0E0</color><color name="temperature_cold">#64B5F6</color><color name="temperature_cool">#81D4FA</color><color name="temperature_mild">#FFD54F</color><color name="temperature_warm">#FFA726</color><color name="temperature_hot">#FF7043</color><color name="status_success">#4CAF50</color><color name="status_warning">#FFC107</color><color name="status_error">#F44336</color></file><file path="C:\Users\<USER>\Pictures\widget\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="activity_horizontal_margin">16dp</dimen><dimen name="activity_vertical_margin">16dp</dimen><dimen name="text_size_small">12sp</dimen><dimen name="text_size_medium">14sp</dimen><dimen name="text_size_large">16sp</dimen><dimen name="text_size_xlarge">20sp</dimen><dimen name="text_size_xxlarge">24sp</dimen><dimen name="widget_margin">8dp</dimen><dimen name="widget_corner_radius">16dp</dimen><dimen name="widget_padding">12dp</dimen><dimen name="widget_icon_size">40dp</dimen><dimen name="widget_city_text_size">18sp</dimen><dimen name="widget_temp_text_size">24sp</dimen><dimen name="widget_desc_text_size">14sp</dimen><dimen name="widget_details_text_size">12sp</dimen><dimen name="list_item_padding">16dp</dimen><dimen name="list_item_height">72dp</dimen><dimen name="list_item_icon_size">48dp</dimen><dimen name="list_item_spacing">8dp</dimen><dimen name="button_height">48dp</dimen><dimen name="button_min_width">88dp</dimen><dimen name="button_padding">16dp</dimen><dimen name="button_corner_radius">4dp</dimen><dimen name="card_corner_radius">8dp</dimen><dimen name="card_elevation">4dp</dimen><dimen name="card_margin">8dp</dimen></file><file path="C:\Users\<USER>\Pictures\widget\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Weather Widget</string><string name="widget_name">Weather Widget</string><string name="widget_description">Displays current weather for a selected city</string><string name="widget_loading">Loading weather…</string><string name="widget_error">Error loading weather</string><string name="widget_last_updated">Last updated: %1$s</string><string name="widget_tap_to_refresh">Tap to refresh</string><string name="config_title">Select a city for your widget</string><string name="config_no_cities">No saved cities found. Please add cities in the main app first.</string><string name="config_save">Save</string><string name="config_cancel">Cancel</string><string name="config_success">Widget configured successfully</string><string name="config_update_frequency">Update frequency</string><string name="config_update_1_hour">Every hour</string><string name="config_update_2_hours">Every 2 hours</string><string name="config_update_4_hours">Every 4 hours</string><string name="search_hint">Search for a city…</string><string name="search_button">Search</string><string name="refresh_all">Refresh All</string><string name="no_weather_data">No weather data available</string><string name="delete_confirmation">Are you sure you want to delete this city?</string><string name="delete_yes">Yes</string><string name="delete_no">No</string><string name="temperature_format">%1$.1f°C</string><string name="humidity_format">Humidity: %1$d%%</string><string name="wind_format">Wind: %1$.1f m/s</string><string name="error_loading_weather">Error loading weather data</string><string name="error_city_not_found">City not found</string><string name="error_network">Network error. Please check your connection</string><string name="error_api_key">Invalid API key</string></file><file path="C:\Users\<USER>\Pictures\widget\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.CleanWeather" parent="android:Theme.Material.Light.NoActionBar"/></file><file name="backup_rules" path="C:\Users\<USER>\Pictures\widget\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\Pictures\widget\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="weather_widget_info" path="C:\Users\<USER>\Pictures\widget\app\src\main\res\xml\weather_widget_info.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Pictures\widget\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Pictures\widget\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Pictures\widget\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\Pictures\widget\app\build\generated\aboutLibraries\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Pictures\widget\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\Pictures\widget\app\build\generated\aboutLibraries\debug\res"><file name="aboutlibraries" path="C:\Users\<USER>\Pictures\widget\app\build\generated\aboutLibraries\debug\res\raw\aboutlibraries.json" qualifiers="" type="raw"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="legacy_api_res$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="legacy_api_res" generated-set="legacy_api_res$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>