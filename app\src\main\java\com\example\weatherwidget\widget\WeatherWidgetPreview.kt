package com.example.weatherwidget.widget

import android.content.Context
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.glance.GlanceModifier
import androidx.glance.appwidget.cornerRadius
import androidx.glance.background
import androidx.glance.layout.*
import androidx.glance.text.Text
import androidx.glance.text.TextAlign as GlanceTextAlign
import androidx.glance.text.TextStyle
import androidx.glance.unit.ColorProvider
import com.example.weatherwidget.data.model.*
import com.example.weatherwidget.util.WeatherIconUtils.getWeatherIconResource
import java.text.SimpleDateFormat
import java.util.*

/**
 * Reusable widget preview component that matches the actual widget appearance
 * Used both in configuration screen and as the actual widget
 */
@Composable
fun WeatherWidgetPreview(
    weatherInfo: WeatherInfo?,
    configuration: WidgetConfiguration,
    modifier: Modifier = Modifier,
    isSystemDark: Boolean = false
) {
    val context = LocalContext.current
    val themeColors = WidgetThemes.getThemeColors(configuration.theme, isSystemDark)
    
    Box(
        modifier = modifier
            .size(180.dp, 110.dp)
            .alpha(1f - configuration.transparency)
            .clip(
                RoundedCornerShape(
                    when (configuration.visualCustomization.cornerRadius) {
                        CornerRadius.SQUARE -> 0.dp
                        CornerRadius.ROUNDED_8 -> 8.dp
                        CornerRadius.ROUNDED_16 -> 16.dp
                    }
                )
            )
            .background(Color(themeColors.backgroundColor))
            .padding(12.dp)
    ) {
        if (weatherInfo != null) {
            WeatherContent(
                weatherInfo = weatherInfo,
                configuration = configuration,
                themeColors = themeColors,
                context = context
            )
        } else {
            // No weather data placeholder
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "Tap to set up weather widget",
                    color = Color(themeColors.primaryTextColor),
                    fontSize = (12 * configuration.visualCustomization.fontSize.scale).sp,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

@Composable
private fun WeatherContent(
    weatherInfo: WeatherInfo,
    configuration: WidgetConfiguration,
    themeColors: WidgetThemeColors,
    context: Context
) {
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.SpaceBetween
    ) {
        // City name (if enabled)
        if (configuration.locationDisplay.showCityName) {
            Text(
                text = "${weatherInfo.cityName}, ${weatherInfo.country}",
                color = Color(themeColors.primaryTextColor),
                fontSize = (14 * configuration.visualCustomization.fontSize.scale).sp,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center,
                maxLines = 1
            )
        }

        // Main weather info
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Weather icon (if condition is enabled)
            if (configuration.weatherElements.showCondition) {
                Icon(
                    painter = androidx.compose.ui.res.painterResource(
                        getWeatherIconResource(weatherInfo.weatherIconCode)
                    ),
                    contentDescription = weatherInfo.weatherDescription,
                    modifier = Modifier.size(configuration.visualCustomization.iconSize.dp.dp),
                    tint = Color(themeColors.iconTint)
                )
            }

            // Temperature (if enabled)
            if (configuration.weatherElements.showTemperature) {
                Text(
                    text = "${weatherInfo.temperature.toInt()}°C",
                    color = Color(themeColors.primaryTextColor),
                    fontSize = (18 * configuration.visualCustomization.fontSize.scale).sp,
                    fontWeight = FontWeight.Bold
                )
            }
        }

        // Additional weather details
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            if (configuration.weatherElements.showHumidity) {
                Text(
                    text = "💧${weatherInfo.humidity}%",
                    color = Color(themeColors.secondaryTextColor),
                    fontSize = (10 * configuration.visualCustomization.fontSize.scale).sp
                )
            }
            
            if (configuration.weatherElements.showWindSpeed) {
                Text(
                    text = "💨${weatherInfo.windSpeed.toInt()}m/s",
                    color = Color(themeColors.secondaryTextColor),
                    fontSize = (10 * configuration.visualCustomization.fontSize.scale).sp
                )
            }
        }

        // Last updated time (if enabled)
        if (configuration.timeDisplay.showLastUpdated) {
            Text(
                text = "Updated: ${formatLastUpdated(weatherInfo.lastUpdated)}",
                color = Color(themeColors.secondaryTextColor),
                fontSize = (8 * configuration.visualCustomization.fontSize.scale).sp,
                textAlign = TextAlign.Center
            )
        }
    }
}

/**
 * Glance version of the widget content for actual widget rendering
 */
@Composable
fun GlanceWeatherWidgetContent(
    weatherInfo: WeatherInfo?,
    configuration: WidgetConfiguration,
    isSystemDark: Boolean = false
) {
    val themeColors = WidgetThemes.getThemeColors(configuration.theme, isSystemDark)
    
    Column(
        modifier = GlanceModifier
            .fillMaxSize()
            .background(ColorProvider(Color(themeColors.backgroundColor)))
            .cornerRadius(
                when (configuration.visualCustomization.cornerRadius) {
                    CornerRadius.SQUARE -> 0.dp
                    CornerRadius.ROUNDED_8 -> 8.dp
                    CornerRadius.ROUNDED_16 -> 16.dp
                }
            )
            .padding(12.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalAlignment = Alignment.CenterVertically
    ) {
        if (weatherInfo != null) {
            GlanceWeatherContent(weatherInfo, configuration, themeColors)
        } else {
            Text(
                text = "Tap to set up weather widget",
                style = TextStyle(
                    fontSize = (12 * configuration.visualCustomization.fontSize.scale).sp,
                    color = ColorProvider(Color(themeColors.primaryTextColor)),
                    textAlign = GlanceTextAlign.Center
                )
            )
        }
    }
}

@Composable
private fun GlanceWeatherContent(
    weatherInfo: WeatherInfo,
    configuration: WidgetConfiguration,
    themeColors: WidgetThemeColors
) {
    // City name (if enabled)
    if (configuration.locationDisplay.showCityName) {
        Text(
            text = "${weatherInfo.cityName}, ${weatherInfo.country}",
            style = TextStyle(
                fontSize = (14 * configuration.visualCustomization.fontSize.scale).sp,
                color = ColorProvider(Color(themeColors.primaryTextColor)),
                fontWeight = androidx.glance.text.FontWeight.Bold,
                textAlign = GlanceTextAlign.Center
            )
        )
        Spacer(modifier = GlanceModifier.height(4.dp))
    }

    // Main weather info row
    Row(
        modifier = GlanceModifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Weather icon and temperature
        if (configuration.weatherElements.showCondition) {
            androidx.glance.Image(
                provider = androidx.glance.ImageProvider(getWeatherIconResource(weatherInfo.weatherIconCode)),
                contentDescription = weatherInfo.weatherDescription,
                modifier = GlanceModifier.size(configuration.visualCustomization.iconSize.dp.dp)
            )
            Spacer(modifier = GlanceModifier.width(8.dp))
        }

        if (configuration.weatherElements.showTemperature) {
            Text(
                text = "${weatherInfo.temperature.toInt()}°C",
                style = TextStyle(
                    fontSize = (18 * configuration.visualCustomization.fontSize.scale).sp,
                    color = ColorProvider(Color(themeColors.primaryTextColor)),
                    fontWeight = androidx.glance.text.FontWeight.Bold
                )
            )
        }
    }

    Spacer(modifier = GlanceModifier.height(4.dp))

    // Additional details
    Row(
        modifier = GlanceModifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        if (configuration.weatherElements.showHumidity) {
            Text(
                text = "💧${weatherInfo.humidity}%",
                style = TextStyle(
                    fontSize = (10 * configuration.visualCustomization.fontSize.scale).sp,
                    color = ColorProvider(Color(themeColors.secondaryTextColor))
                )
            )
            if (configuration.weatherElements.showWindSpeed) {
                Spacer(modifier = GlanceModifier.width(8.dp))
            }
        }
        
        if (configuration.weatherElements.showWindSpeed) {
            Text(
                text = "💨${weatherInfo.windSpeed.toInt()}m/s",
                style = TextStyle(
                    fontSize = (10 * configuration.visualCustomization.fontSize.scale).sp,
                    color = ColorProvider(Color(themeColors.secondaryTextColor))
                )
            )
        }
    }

    // Last updated (if enabled)
    if (configuration.timeDisplay.showLastUpdated) {
        Spacer(modifier = GlanceModifier.height(4.dp))
        Text(
            text = "Updated: ${formatLastUpdated(weatherInfo.lastUpdated)}",
            style = TextStyle(
                fontSize = (8 * configuration.visualCustomization.fontSize.scale).sp,
                color = ColorProvider(Color(themeColors.secondaryTextColor)),
                textAlign = GlanceTextAlign.Center
            )
        )
    }
}

private fun formatLastUpdated(timestamp: Long): String {
    val formatter = SimpleDateFormat("HH:mm", Locale.getDefault())
    return formatter.format(Date(timestamp))
}
