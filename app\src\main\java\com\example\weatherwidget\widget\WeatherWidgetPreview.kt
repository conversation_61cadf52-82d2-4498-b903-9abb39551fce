package com.example.weatherwidget.widget

import android.content.Context
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.glance.GlanceModifier
import androidx.glance.Image
import androidx.glance.ImageProvider
import androidx.glance.appwidget.cornerRadius
import androidx.glance.background
import com.example.weatherwidget.data.model.CornerRadius
import com.example.weatherwidget.data.model.WeatherInfo
import com.example.weatherwidget.data.model.WidgetConfiguration
import com.example.weatherwidget.data.model.WidgetThemeColors
import com.example.weatherwidget.data.model.WidgetThemes
import com.example.weatherwidget.util.WeatherIconUtils
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import androidx.glance.layout.Alignment as GlanceAlignment
import androidx.glance.layout.Column as GlanceColumn
import androidx.glance.layout.Row as GlanceRow
import androidx.glance.layout.Spacer as GlanceSpacer
import androidx.glance.layout.fillMaxSize as glanceFillMaxSize
import androidx.glance.layout.fillMaxWidth as glanceFillMaxWidth
import androidx.glance.layout.height as glanceHeight
import androidx.glance.layout.padding as glancePadding
import androidx.glance.layout.size as glanceSize
import androidx.glance.layout.width as glanceWidth
import androidx.glance.text.FontWeight as GlanceFontWeight
import androidx.glance.text.Text as GlanceText
import androidx.glance.text.TextAlign as GlanceTextAlign
import androidx.glance.text.TextStyle as GlanceTextStyle

/**
 * Reusable widget preview component that matches the actual widget appearance
 * Used both in configuration screen and as the actual widget
 */
@Composable
fun WeatherWidgetPreview(
    weatherInfo: WeatherInfo?,
    configuration: WidgetConfiguration,
    modifier: Modifier = Modifier,
    isSystemDark: Boolean = false
) {
    val context = LocalContext.current
    val themeColors = WidgetThemes.getThemeColors(configuration.theme, isSystemDark)

    Box(
        modifier = modifier
            .size(180.dp, 110.dp)
            .alpha(1f - configuration.transparency)
            .clip(
                RoundedCornerShape(
                    when (configuration.visualCustomization.cornerRadius) {
                        CornerRadius.SQUARE -> 0.dp
                        CornerRadius.ROUNDED_8 -> 8.dp
                        CornerRadius.ROUNDED_16 -> 16.dp
                    }
                )
            )
            .background(Color(themeColors.backgroundColor))
            .padding(12.dp)
    ) {
        if (weatherInfo != null) {
            WeatherContent(
                weatherInfo = weatherInfo,
                configuration = configuration,
                themeColors = themeColors,
                context = context
            )
        } else {
            // No weather data placeholder
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "Tap to set up weather widget",
                    color = Color(themeColors.primaryTextColor),
                    fontSize = (12 * configuration.visualCustomization.fontSize.scale).sp,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

@Composable
private fun WeatherContent(
    weatherInfo: WeatherInfo,
    configuration: WidgetConfiguration,
    themeColors: WidgetThemeColors,
    context: Context
) {
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.SpaceBetween
    ) {
        // City name (if enabled)
        if (configuration.locationDisplay.showCityName) {
            Text(
                text = "${weatherInfo.cityName}, ${weatherInfo.country}",
                color = Color(themeColors.primaryTextColor),
                fontSize = (14 * configuration.visualCustomization.fontSize.scale).sp,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center,
                maxLines = 1
            )
        }

        // Main weather info
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Weather icon (if condition is enabled)
            if (configuration.weatherElements.showCondition) {
                Icon(
                    painter = painterResource(
                        WeatherIconUtils.getWeatherIconResource(weatherInfo.weatherIconCode, isDay = true)
                    ),
                    contentDescription = weatherInfo.weatherDescription,
                    modifier = Modifier.size(configuration.visualCustomization.iconSize.dp.dp),
                    tint = Color(themeColors.iconTint)
                )
            }

            // Temperature (if enabled)
            if (configuration.weatherElements.showTemperature) {
                Text(
                    text = "${weatherInfo.temperature.toInt()}°C",
                    color = Color(themeColors.primaryTextColor),
                    fontSize = (18 * configuration.visualCustomization.fontSize.scale).sp,
                    fontWeight = FontWeight.Bold
                )
            }
        }

        // Additional weather details
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            if (configuration.weatherElements.showHumidity) {
                Text(
                    text = "💧${weatherInfo.humidity}%",
                    color = Color(themeColors.secondaryTextColor),
                    fontSize = (10 * configuration.visualCustomization.fontSize.scale).sp
                )
            }

            if (configuration.weatherElements.showWindSpeed) {
                Text(
                    text = "💨${weatherInfo.windSpeed.toInt()}m/s",
                    color = Color(themeColors.secondaryTextColor),
                    fontSize = (10 * configuration.visualCustomization.fontSize.scale).sp
                )
            }
        }

        // Last updated time (if enabled)
        if (configuration.timeDisplay.showLastUpdated) {
            Text(
                text = "Updated: ${formatLastUpdated(weatherInfo.lastUpdated)}",
                color = Color(themeColors.secondaryTextColor),
                fontSize = (8 * configuration.visualCustomization.fontSize.scale).sp,
                textAlign = TextAlign.Center
            )
        }
    }
}

/**
 * Glance version of the widget content for actual widget rendering
 */
@Composable
fun GlanceWeatherWidgetContent(
    weatherInfo: WeatherInfo?,
    configuration: WidgetConfiguration,
    isSystemDark: Boolean = false
) {
    val themeColors = WidgetThemes.getThemeColors(configuration.theme, isSystemDark)

    GlanceColumn(
        modifier = GlanceModifier
            .glanceFillMaxSize()
            .background(Color(themeColors.backgroundColor))
            .cornerRadius(
                when (configuration.visualCustomization.cornerRadius) {
                    CornerRadius.SQUARE -> 0.dp
                    CornerRadius.ROUNDED_8 -> 8.dp
                    CornerRadius.ROUNDED_16 -> 16.dp
                }
            )
            .glancePadding(12.dp),
        horizontalAlignment = GlanceAlignment.CenterHorizontally,
        verticalAlignment = GlanceAlignment.CenterVertically
    ) {
        if (weatherInfo != null) {
            GlanceWeatherContent(weatherInfo, configuration, themeColors)
        } else {
            GlanceText(
                text = "Tap to set up weather widget",
                style = GlanceTextStyle(
                    fontSize = (12 * configuration.visualCustomization.fontSize.scale).sp,
                    textAlign = GlanceTextAlign.Center
                )
            )
        }
    }
}

@Composable
private fun GlanceWeatherContent(
    weatherInfo: WeatherInfo,
    configuration: WidgetConfiguration,
    themeColors: WidgetThemeColors
) {
    // City name (if enabled)
    if (configuration.locationDisplay.showCityName) {
        GlanceText(
            text = "${weatherInfo.cityName}, ${weatherInfo.country}",
            style = GlanceTextStyle(
                fontSize = (14 * configuration.visualCustomization.fontSize.scale).sp,
                fontWeight = GlanceFontWeight.Bold,
                textAlign = GlanceTextAlign.Center
            )
        )
        GlanceSpacer(modifier = GlanceModifier.glanceHeight(4.dp))
    }

    // Main weather info row
    GlanceRow(
        modifier = GlanceModifier.glanceFillMaxWidth(),
        horizontalAlignment = GlanceAlignment.CenterHorizontally,
        verticalAlignment = GlanceAlignment.CenterVertically
    ) {
        // Weather icon and temperature
        if (configuration.weatherElements.showCondition) {
            Image(
                provider = ImageProvider(
                    WeatherIconUtils.getWeatherIconResource(
                        weatherInfo.weatherIconCode,
                        isDay = true
                    )
                ),
                contentDescription = weatherInfo.weatherDescription,
                modifier = GlanceModifier.glanceSize(configuration.visualCustomization.iconSize.dp.dp)
            )
            GlanceSpacer(modifier = GlanceModifier.glanceWidth(8.dp))
        }

        if (configuration.weatherElements.showTemperature) {
            GlanceText(
                text = "${weatherInfo.temperature.toInt()}°C",
                style = GlanceTextStyle(
                    fontSize = (18 * configuration.visualCustomization.fontSize.scale).sp,
                    fontWeight = GlanceFontWeight.Bold
                )
            )
        }
    }

    GlanceSpacer(modifier = GlanceModifier.glanceHeight(4.dp))

    // Additional details
    GlanceRow(
        modifier = GlanceModifier.glanceFillMaxWidth(),
        horizontalAlignment = GlanceAlignment.CenterHorizontally
    ) {
        if (configuration.weatherElements.showHumidity) {
            GlanceText(
                text = "💧${weatherInfo.humidity}%",
                style = GlanceTextStyle(
                    fontSize = (10 * configuration.visualCustomization.fontSize.scale).sp,
                )
            )
            if (configuration.weatherElements.showWindSpeed) {
                GlanceSpacer(modifier = GlanceModifier.glanceWidth(8.dp))
            }
        }

        if (configuration.weatherElements.showWindSpeed) {
            GlanceText(
                text = "💨${weatherInfo.windSpeed.toInt()}m/s",
                style = GlanceTextStyle(
                    fontSize = (10 * configuration.visualCustomization.fontSize.scale).sp,
                )
            )
        }
    }

    // Last updated (if enabled)
    if (configuration.timeDisplay.showLastUpdated) {
        GlanceSpacer(modifier = GlanceModifier.glanceHeight(4.dp))
        GlanceText(
            text = "Updated: ${formatLastUpdated(weatherInfo.lastUpdated)}",
            style = GlanceTextStyle(
                fontSize = (8 * configuration.visualCustomization.fontSize.scale).sp,
                textAlign = GlanceTextAlign.Center
            )
        )
    }
}

private fun formatLastUpdated(timestamp: Long): String {
    val formatter = SimpleDateFormat("HH:mm", Locale.getDefault())
    return formatter.format(Date(timestamp))
}