package com.example.weatherwidget.widget

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.unit.dp
import com.example.weatherwidget.data.model.*

@Composable
fun ThemeSection(
    theme: WidgetTheme,
    transparency: Float,
    onThemeChanged: (WidgetTheme) -> Unit,
    onTransparencyChanged: (Float) -> Unit
) {
    Column {
        // Theme selection
        Text(
            text = "Theme",
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        Column(Modifier.selectableGroup()) {
            WidgetTheme.values().forEach { themeOption ->
                Row(
                    Modifier
                        .fillMaxWidth()
                        .height(48.dp)
                        .selectable(
                            selected = (theme == themeOption),
                            onClick = { onThemeChanged(themeOption) },
                            role = Role.RadioButton
                        )
                        .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = (theme == themeOption),
                        onClick = null
                    )
                    Text(
                        text = when (themeOption) {
                            WidgetTheme.DARK -> "Dark"
                            WidgetTheme.LIGHT -> "Light"
                            WidgetTheme.BLUE -> "Blue"
                            WidgetTheme.SYSTEM -> "System Default"
                        },
                        style = MaterialTheme.typography.bodyLarge,
                        modifier = Modifier.padding(start = 16.dp)
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Transparency slider
        Text(
            text = "Transparency: ${(transparency * 100).toInt()}%",
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        Slider(
            value = transparency,
            onValueChange = onTransparencyChanged,
            valueRange = 0f..1f,
            steps = 9, // 10% increments
            modifier = Modifier.fillMaxWidth()
        )
    }
}

@Composable
fun WeatherElementsSection(
    elements: WeatherElementsConfig,
    timeDisplay: TimeDisplayConfig,
    onElementsChanged: (WeatherElementsConfig) -> Unit,
    onTimeDisplayChanged: (TimeDisplayConfig) -> Unit
) {
    Column {
        // Weather elements toggles
        Text(
            text = "Display Elements",
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        SwitchRow(
            text = "Temperature",
            checked = elements.showTemperature,
            onCheckedChange = { 
                onElementsChanged(elements.copy(showTemperature = it))
            }
        )
        
        SwitchRow(
            text = "Weather Condition",
            checked = elements.showCondition,
            onCheckedChange = { 
                onElementsChanged(elements.copy(showCondition = it))
            }
        )
        
        SwitchRow(
            text = "Humidity",
            checked = elements.showHumidity,
            onCheckedChange = { 
                onElementsChanged(elements.copy(showHumidity = it))
            }
        )
        
        SwitchRow(
            text = "Wind Speed",
            checked = elements.showWindSpeed,
            onCheckedChange = { 
                onElementsChanged(elements.copy(showWindSpeed = it))
            }
        )
        
        Spacer(modifier = Modifier.height(12.dp))
        
        // Time display
        SwitchRow(
            text = "Show Last Updated Time",
            checked = timeDisplay.showLastUpdated,
            onCheckedChange = { 
                onTimeDisplayChanged(timeDisplay.copy(showLastUpdated = it))
            }
        )
    }
}

@Composable
fun VisualCustomizationSection(
    visual: VisualCustomizationConfig,
    onVisualChanged: (VisualCustomizationConfig) -> Unit
) {
    Column {
        // Icon size selection
        Text(
            text = "Icon Size",
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        Column(Modifier.selectableGroup()) {
            IconSize.values().forEach { sizeOption ->
                Row(
                    Modifier
                        .fillMaxWidth()
                        .height(48.dp)
                        .selectable(
                            selected = (visual.iconSize == sizeOption),
                            onClick = { onVisualChanged(visual.copy(iconSize = sizeOption)) },
                            role = Role.RadioButton
                        )
                        .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = (visual.iconSize == sizeOption),
                        onClick = null
                    )
                    Text(
                        text = when (sizeOption) {
                            IconSize.SMALL -> "Small (16dp)"
                            IconSize.MEDIUM -> "Medium (24dp)"
                            IconSize.LARGE -> "Large (32dp)"
                        },
                        style = MaterialTheme.typography.bodyLarge,
                        modifier = Modifier.padding(start = 16.dp)
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Font size selection
        Text(
            text = "Font Size",
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        Column(Modifier.selectableGroup()) {
            FontSize.values().forEach { fontOption ->
                Row(
                    Modifier
                        .fillMaxWidth()
                        .height(48.dp)
                        .selectable(
                            selected = (visual.fontSize == fontOption),
                            onClick = { onVisualChanged(visual.copy(fontSize = fontOption)) },
                            role = Role.RadioButton
                        )
                        .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = (visual.fontSize == fontOption),
                        onClick = null
                    )
                    Text(
                        text = when (fontOption) {
                            FontSize.SMALL -> "Small"
                            FontSize.MEDIUM -> "Medium"
                            FontSize.LARGE -> "Large"
                        },
                        style = MaterialTheme.typography.bodyLarge,
                        modifier = Modifier.padding(start = 16.dp)
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Corner radius selection
        Text(
            text = "Corner Style",
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        Column(Modifier.selectableGroup()) {
            CornerRadius.values().forEach { cornerOption ->
                Row(
                    Modifier
                        .fillMaxWidth()
                        .height(48.dp)
                        .selectable(
                            selected = (visual.cornerRadius == cornerOption),
                            onClick = { onVisualChanged(visual.copy(cornerRadius = cornerOption)) },
                            role = Role.RadioButton
                        )
                        .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = (visual.cornerRadius == cornerOption),
                        onClick = null
                    )
                    Text(
                        text = when (cornerOption) {
                            CornerRadius.SQUARE -> "Square"
                            CornerRadius.ROUNDED_8 -> "Slightly Rounded"
                            CornerRadius.ROUNDED_16 -> "Rounded"
                        },
                        style = MaterialTheme.typography.bodyLarge,
                        modifier = Modifier.padding(start = 16.dp)
                    )
                }
            }
        }
    }
}

@Composable
fun UpdateSettingsSection(
    updateSettings: UpdateSettingsConfig,
    onUpdateSettingsChanged: (UpdateSettingsConfig) -> Unit
) {
    Column {
        // Refresh rate selection
        Text(
            text = "Refresh Rate",
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        Column(Modifier.selectableGroup()) {
            RefreshRate.values().forEach { rateOption ->
                Row(
                    Modifier
                        .fillMaxWidth()
                        .height(48.dp)
                        .selectable(
                            selected = (updateSettings.refreshRate == rateOption),
                            onClick = { onUpdateSettingsChanged(updateSettings.copy(refreshRate = rateOption)) },
                            role = Role.RadioButton
                        )
                        .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = (updateSettings.refreshRate == rateOption),
                        onClick = null
                    )
                    Text(
                        text = rateOption.displayName,
                        style = MaterialTheme.typography.bodyLarge,
                        modifier = Modifier.padding(start = 16.dp)
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Network constraint selection
        Text(
            text = "Network Constraint",
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        Column(Modifier.selectableGroup()) {
            NetworkConstraint.values().forEach { constraintOption ->
                Row(
                    Modifier
                        .fillMaxWidth()
                        .height(48.dp)
                        .selectable(
                            selected = (updateSettings.networkConstraint == constraintOption),
                            onClick = { onUpdateSettingsChanged(updateSettings.copy(networkConstraint = constraintOption)) },
                            role = Role.RadioButton
                        )
                        .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = (updateSettings.networkConstraint == constraintOption),
                        onClick = null
                    )
                    Text(
                        text = constraintOption.displayName,
                        style = MaterialTheme.typography.bodyLarge,
                        modifier = Modifier.padding(start = 16.dp)
                    )
                }
            }
        }
    }
}

@Composable
private fun SwitchRow(
    text: String,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(text = text)
        Switch(
            checked = checked,
            onCheckedChange = onCheckedChange
        )
    }
}
