package com.example.weatherwidget.worker

import android.content.Context
import androidx.glance.appwidget.GlanceAppWidgetManager
import androidx.glance.appwidget.state.updateAppWidgetState
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.example.weatherwidget.data.repository.WeatherRepository
import com.example.weatherwidget.domain.usecase.WidgetConfigurationUseCase
import com.example.weatherwidget.widget.WeatherWidgetManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

class WeatherUpdateWorker(
    context: Context,
    params: WorkerParameters,
    private val weatherRepository: WeatherRepository
) : CoroutineWorker(context, params), KoinComponent {

    private val widgetManager: WeatherWidgetManager by inject()
    private val configurationUseCase: WidgetConfigurationUseCase by inject()

    override suspend fun doWork(): Result = withContext(Dispatchers.IO) {
        try {
            // Get all widget configurations
            val allConfigurations = weatherRepository.getAllWidgetConfigurations()
            val glanceIds = GlanceAppWidgetManager(applicationContext).getGlanceIds(widgetManager.weatherWidget.javaClass)

            var hasSuccessfulUpdate = false

            // Update each widget based on its configuration
            glanceIds.forEach { glanceId ->
                try {
                    val widgetId = glanceId.toString().hashCode()
                    val configuration = configurationUseCase.getWidgetConfiguration(widgetId)

                    // Get city ID from configuration or fallback to global setting
                    val cityId = configuration.first().locationDisplay.selectedCityId
                        ?: weatherRepository.getSelectedWidgetCityId()

                    if (cityId != null) {
                        // Fetch fresh weather data for this city
                        val weatherResult = weatherRepository.refreshWeatherById(cityId)

                        if (weatherResult.isSuccess) {
                            // Update this specific widget
                            updateAppWidgetState(applicationContext, glanceId) { prefs ->
                                // The actual state update is handled by the WeatherWidget class
                                // which reads from the repository and configuration
                            }
                            widgetManager.weatherWidget.update(applicationContext, glanceId)
                            hasSuccessfulUpdate = true
                        }
                    }
                } catch (e: Exception) {
                    // Log error for this specific widget but continue with others
                    e.printStackTrace()
                }
            }

            return@withContext if (hasSuccessfulUpdate) Result.success() else Result.retry()
        } catch (e: Exception) {
            // Log the error
            e.printStackTrace()
            Result.failure()
        }
    }
}