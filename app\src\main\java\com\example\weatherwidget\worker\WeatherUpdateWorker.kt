package com.example.weatherwidget.worker

import android.content.Context
import androidx.glance.appwidget.GlanceAppWidgetManager
import androidx.glance.appwidget.state.updateAppWidgetState
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.example.weatherwidget.data.repository.WeatherRepository
import com.example.weatherwidget.widget.WeatherWidgetManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

class WeatherUpdateWorker(
    context: Context,
    params: WorkerParameters,
    private val weatherRepository: WeatherRepository
) : CoroutineWorker(context, params), KoinComponent {

    private val widgetManager: WeatherWidgetManager by inject()

    override suspend fun doWork(): Result = withContext(Dispatchers.IO) {
        try {
            // Get the selected city ID for the widget
            val cityId = weatherRepository.getSelectedWidgetCityId()
            
            if (cityId != null) {
                // Fetch fresh weather data
                val weatherResult = weatherRepository.refreshWeatherById(cityId)
                
                if (weatherResult.isSuccess) {
                    // Update all widgets
                    val glanceId = GlanceAppWidgetManager(applicationContext).getGlanceIds(widgetManager.weatherWidget.javaClass)
                    glanceId.forEach { id ->
                        updateAppWidgetState(applicationContext, id) { prefs ->
                            // The actual state update is handled by the WeatherWidget class
                            // which reads from the repository
                        }
                        widgetManager.weatherWidget.update(applicationContext, id)
                    }
                    
                    return@withContext Result.success()
                }
            }
            
            // If we reach here, something went wrong but it's not a fatal error
            Result.retry()
        } catch (e: Exception) {
            // Log the error
            e.printStackTrace()
            Result.failure()
        }
    }
}