{"logs": [{"outputFile": "com.example.weatherwidget.app-mergeDebugResources-80:/values-en-rCA/values-en-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\06d808385d814fc5793833c3be2ca602\\transformed\\foundation-release\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,216", "endColumns": "74,85,84", "endOffsets": "125,211,296"}, "to": {"startLines": "29,119,120", "startColumns": "4,4,4", "startOffsets": "2762,11939,12025", "endColumns": "74,85,84", "endOffsets": "2832,12020,12105"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\26a7be6aa46fc6df32cf278684c813b2\\transformed\\material3-release\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,289,400,514,613,708,820,956,1072,1208,1292,1391,1482,1579,1698,1823,1927,2054,2177,2305,2467,2588,2704,2827,2952,3044,3142,3259,3383,3480,3582,3684,3814,3953,4059,4158,4234,4330,4424,4528,4615,4702,4804,4884,4968,5069,5170,5270,5369,5457,5563,5664,5768,5884,5964,6064", "endColumns": "117,115,110,113,98,94,111,135,115,135,83,98,90,96,118,124,103,126,122,127,161,120,115,122,124,91,97,116,123,96,101,101,129,138,105,98,75,95,93,103,86,86,101,79,83,100,100,99,98,87,105,100,103,115,79,99,94", "endOffsets": "168,284,395,509,608,703,815,951,1067,1203,1287,1386,1477,1574,1693,1818,1922,2049,2172,2300,2462,2583,2699,2822,2947,3039,3137,3254,3378,3475,3577,3679,3809,3948,4054,4153,4229,4325,4419,4523,4610,4697,4799,4879,4963,5064,5165,5265,5364,5452,5558,5659,5763,5879,5959,6059,6154"}, "to": {"startLines": "47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4592,4710,4826,4937,5051,5150,5245,5357,5493,5609,5745,5829,5928,6019,6116,6235,6360,6464,6591,6714,6842,7004,7125,7241,7364,7489,7581,7679,7796,7920,8017,8119,8221,8351,8490,8596,8695,8771,8867,8961,9065,9152,9239,9341,9421,9505,9606,9707,9807,9906,9994,10100,10201,10305,10421,10501,10601", "endColumns": "117,115,110,113,98,94,111,135,115,135,83,98,90,96,118,124,103,126,122,127,161,120,115,122,124,91,97,116,123,96,101,101,129,138,105,98,75,95,93,103,86,86,101,79,83,100,100,99,98,87,105,100,103,115,79,99,94", "endOffsets": "4705,4821,4932,5046,5145,5240,5352,5488,5604,5740,5824,5923,6014,6111,6230,6355,6459,6586,6709,6837,6999,7120,7236,7359,7484,7576,7674,7791,7915,8012,8114,8216,8346,8485,8591,8690,8766,8862,8956,9060,9147,9234,9336,9416,9500,9601,9702,9802,9901,9989,10095,10196,10300,10416,10496,10596,10691"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\dcea319fdd3ad3c36ece65fd708a73da\\transformed\\ui-release\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "181,273,355,449,548,634,716,806,895,979,1057,1139,1212,1296,1372,1444,1514,1591,1657", "endColumns": "91,81,93,98,85,81,89,88,83,77,81,72,83,75,71,69,76,65,119", "endOffsets": "268,350,444,543,629,711,801,890,974,1052,1134,1207,1291,1367,1439,1509,1586,1652,1772"}, "to": {"startLines": "37,38,39,40,41,45,46,105,106,107,108,110,111,112,113,114,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3555,3647,3729,3823,3922,4420,4502,10784,10873,10957,11035,11200,11273,11357,11433,11505,11676,11753,11819", "endColumns": "91,81,93,98,85,81,89,88,83,77,81,72,83,75,71,69,76,65,119", "endOffsets": "3642,3724,3818,3917,4003,4497,4587,10868,10952,11030,11112,11268,11352,11428,11500,11570,11748,11814,11934"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\821e8c1a7433219b3163b17b67af0011\\transformed\\material-release\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "104", "startColumns": "4", "startOffsets": "10696", "endColumns": "87", "endOffsets": "10779"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\30287725489871af330b7aa82a2b9f1f\\transformed\\core-1.16.0\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,657,773", "endColumns": "95,101,98,98,103,101,115,100", "endOffsets": "146,248,347,446,550,652,768,869"}, "to": {"startLines": "30,31,32,33,34,35,36,115", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2837,2933,3035,3134,3233,3337,3439,11575", "endColumns": "95,101,98,98,103,101,115,100", "endOffsets": "2928,3030,3129,3228,3332,3434,3550,11671"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\f958aeb604e7c143e9bc08e83e2ba828\\transformed\\glance-appwidget-1.1.1\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,246,352", "endColumns": "190,105,114", "endOffsets": "241,347,462"}, "to": {"startLines": "42,43,44", "startColumns": "4,4,4", "startOffsets": "4008,4199,4305", "endColumns": "190,105,114", "endOffsets": "4194,4300,4415"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\a6d2b7e5ecb18270abb575bfe8bf3041\\transformed\\appcompat-1.7.0\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,11117", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,11195"}}]}]}